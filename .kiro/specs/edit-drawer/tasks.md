# 实施计划

- [x] 1. 设置项目结构和核心配置
  - 在 `client/pages/omni-channel/config.ts` 中添加新的枚举和配置项
  - 扩展 `YouzanGoodsUpdateRuleItem` 枚举以包含新字段
  - 添加 `EItemSyncRange` 和 `EAutoPublishSync` 枚举
  - _需求: 1.1, 3.1, 3.6_

- [x] 2. 创建基础抽屉组件结构
  - 创建 `client/pages/omni-channel/pages/edit/edit-drawer.tsx` 文件
  - 实现基本的抽屉布局使用 Zent Drawer 组件
  - 设置抽屉属性：placement="right", width=800
  - 使用 Drawer.Body 和 Drawer.Footer 结构
  - 设置表单管理使用 `Form.useForm(FormStrategy.View)`
  - 添加抽屉的打开/关闭状态管理
  - 添加 mode 属性支持编辑和预览模式
  - 添加加载状态管理和 BlockLoading 组件
  - 重用编辑页面的条件显示逻辑（canShowSetting, showFormElement）
  - 添加必要的导入和常量定义
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 6.4_

- [x] 3. 实现店铺模块
  - 重用现有的 `StoreOperatingStatus` 组件
  - 重用现有的 `SynchronizationOfBusinessHours` 组件
  - 确保与编辑页面相同的表单逻辑和验证
  - _需求: 2.1, 2.2, 2.3_

- [x] 4. 实现商品同步规则模块 - 更新范围字段
  - 创建 `ItemSyncRangeField` 组件
  - 实现必选的单选字段，包含三个选项
  - 添加帮助文本显示
  - 使用 `StoreHandleConfig.itemSyncRange` 作为字段名
  - 添加 onChange 监听以触发其他字段的条件显示
  - _需求: 3.1, 3.3, 3.4_

- [x] 4.1 实现条件显示逻辑控制器
  - 创建条件显示逻辑函数
  - 当选择"指定部分信息"时，隐藏基础信息和价格信息部分
  - 当选择"仅绑定商品，不更新信息"时，隐藏除"商品库发布外卖商品时"外的所有字段
  - 监听销售信息中"商品库存"复选框的变化，控制库存同步规则字段的显示
  - 确保隐藏字段的数据保持不变
  - _需求: 3.3, 3.4, 3.8, 3.8.1_

- [x] 4.2 实现自动切换和验证逻辑
  - 实现自动切换逻辑：当"全部信息"模式下所有基础信息和价格信息字段都取消选中时，自动切换到"指定部分信息"
  - 实现验证提示：当"全部信息"模式下基础信息和价格信息都未选中时，显示红色错误提示
  - 监听复选框变化，实时更新自动切换和验证状态
  - _需求: 3.4.1, 3.4.2_

- [x] 5. 实现商品同步规则模块 - 销售信息部分
  - 创建销售信息复选框组
  - 实现 "销售状态上下架" 和 "商品库存" 复选框
  - 添加帮助文本 "仅饿了么外卖和京东外卖渠道，支持同步销售状态"
  - 添加条件显示逻辑：当更新范围为"仅绑定商品，不更新信息"时隐藏
  - 使用 `YouzanGoodsUpdateRuleItem` 枚举值
  - _需求: 3.5, 3.4_

- [x] 6. 实现商品同步规则模块 - 基础信息部分
  - 创建基础信息复选框组
  - 实现所有基础信息字段：商品名称、规格名称、商品/规格条码、商品类目、属性、商品主图、商品分组、商品描述、最小起购量
  - 重用现有字段的枚举值，添加新字段到 `YouzanGoodsUpdateRuleItem`
  - 移除规格条码/编码(skuBarcode)字段
  - 将商品条码字段文案修改为"商品/规格条码"
  - 添加属性字段的帮助图标和提示文本
  - 添加条件显示逻辑：仅当更新范围为"全部信息"时显示
  - _需求: 3.7, 3.3_

- [x] 7. 实现商品同步规则模块 - 价格信息部分
  - 创建价格信息复选框组
  - 实现 "价格" 和 "商品打包费" 复选框
  - 根据 `isMeiTuanShangou` 条件显示/隐藏商品打包费
  - 添加条件显示逻辑：仅当更新范围为"全部信息"时显示
  - 使用现有的 `YouzanGoodsUpdateRuleItem` 枚举值
  - _需求: 3.8, 3.3_

- [x] 8. 实现库存同步规则字段
  - 创建 `StockSyncRulesField` 组件
  - 实现双重条件显示逻辑：
    - 必须选中销售信息中的"商品库存"复选框
    - 更新范围不能是"仅绑定商品，不更新信息"
  - 更新单选选项文案为新的要求
  - 添加每个选项下的帮助文本
  - 使用 `StoreHandleConfig.stockRealTimeSync` 作为字段名
  - _需求: 3.8, 3.8.1, 3.9, 3.10, 3.11, 3.4_

- [x] 9. 实现自动发布设置字段
  - 创建 `AutoPublishSyncField` 组件
  - 实现必选的单选字段
  - 使用 `EAutoPublishSync` 枚举值
  - 使用 `StoreHandleConfig.autoPublishSync` 作为字段名
  - _需求: 3.12_

- [x] 10. 实现总部更新规则字段
  - 重用现有的 `GoodsUpdateRuleField` 组件
  - 作为商品同步规则模块的最后一个字段
  - 添加条件显示逻辑（仅在非特定条件下显示）
  - 确保与编辑页面相同的逻辑
  - _需求: 3.14_

- [x] 11. 实现库存共享比例模块
  - 重用现有的 `InventorySynchronizationRatio` 组件
  - 添加条件显示逻辑（仅在非多渠道时显示）
  - 包含实物可售库存和计划可售库存两个字段
  - 确保与编辑页面相同的表单逻辑和验证
  - _需求: 3.15, 3.16, 3.17, 3.18_

- [x] 12. 实现外卖开票设置模块
  - 重用现有的 `OrderInvoiceSwitchField` 组件
  - 修改默认值为开启状态
  - 确保与编辑页面相同的表单逻辑
  - _需求: 4.1_

- [x] 13. 实现数据加载和初始化逻辑
  - 重用编辑页面的 `api.getStoreProperty` 函数
  - 实现 `initExtSyncConfigs` 函数（与编辑页面相同）
  - 添加库存共享比例数据加载（仅非多渠道时）
  - 设置新字段的默认值
  - 确保外卖订单开票设置默认为开启
  - 使用与编辑页面相同的初始化模式
  - _需求: 5.1, 5.2, 3.15, 3.16, 3.17_

- [x] 14. 实现数据保存逻辑
  - 重用编辑页面的 `api.updateChannelShop` 函数
  - 实现 `formatExtSyncConfigs4Submit` 函数（与编辑页面相同）
  - 添加库存共享比例保存逻辑（使用 `api.updateChannelStockAllocation`）
  - 添加新字段到 extConfigs 中
  - 实现保存时的字段清零逻辑：
    - "指定部分信息"：将所有基础信息和价格信息字段设置为0（通过从选中数组中移除）
    - "仅绑定商品，不更新信息"：将所有销售信息、基础信息和价格信息字段设置为0（通过清空选中数组）
    - 利用 `formatExtSyncConfigs4Submit` 函数的特性：不在数组中的字段自动设置为0
  - 确保与编辑页面相同的保存逻辑
  - _需求: 5.3, 5.4, 3.4.3, 3.4.4, 3.15, 3.16, 3.17_

- [x] 15. 实现表单验证
  - 添加必填字段验证：更新范围、自动发布设置
  - 添加库存共享比例验证：实物可售库存（1-100%）和计划可售库存（必选）
  - 实现条件验证：库存同步规则（当选中商品库存复选框且销售信息部分可见时）
  - 实现复选框组验证：当"全部信息"模式下基础信息和价格信息至少选中一个
  - 使用与编辑页面相同的验证模式
  - 添加适当的错误消息
  - _需求: 5.3, 3.8, 3.8.1, 3.4.2, 3.16, 3.17_

- [x] 16. 实现预览模式功能
  - 实现模式控制逻辑，根据 mode 属性切换编辑/预览状态
  - 在预览模式下禁用所有表单字段
  - 在预览模式下修改抽屉标题为"查看渠道设置"
  - 在预览模式下只显示"关闭"按钮，隐藏"保存"按钮
  - 确保预览模式下条件显示逻辑正常工作
  - _需求: 6.1, 6.2, 6.3, 6.4, 6.5_

- [x] 17. 实现抽屉集成和触发逻辑
  - 在父组件中添加抽屉触发逻辑
  - 替换现有的页面跳转为抽屉打开
  - 实现成功保存后的回调处理
  - 添加抽屉关闭时的状态清理
  - 支持传入 mode 参数控制编辑/预览模式
  - _需求: 1.1, 5.5_

- [x] 18. 添加样式和布局
  - 创建抽屉的 CSS 样式文件
  - 实现与编辑页面一致的表单布局
  - 优化 Drawer 的宽度和高度适配
  - 添加 `custom-label` 样式类
  - 添加预览模式的视觉样式（如禁用状态的样式）
  - 确保 Drawer.Footer 的按钮布局和样式
  - 确保响应式设计和适当的间距
  - _需求: 1.2, 1.3, 6.2_

- [ ] 19. 实现错误处理和用户反馈
  - 添加加载状态管理
  - 实现保存成功/失败的通知（仅编辑模式）
  - 添加网络错误的重试机制
  - 确保与编辑页面相同的错误处理模式
  - _需求: 5.4, 5.5_

- [ ] 20. 编写单元测试
  - 测试组件渲染和表单字段
  - 测试表单验证规则
  - 测试条件字段显示逻辑（基于更新范围的不同选项）
  - 测试库存同步规则的双重条件显示逻辑
  - 测试库存共享比例模块的条件显示（仅非多渠道时）
  - 测试自动切换逻辑：全部信息模式下取消所有基础信息和价格信息时自动切换到指定部分信息
  - 测试验证提示逻辑：全部信息模式下基础信息和价格信息都未选中时显示错误提示
  - 测试保存时的字段清零逻辑
  - 测试预览模式和编辑模式的切换
  - 测试预览模式下字段禁用状态
  - 测试数据转换函数
  - 测试字段隐藏时数据保持不变的逻辑
  - _需求: 所有需求的测试覆盖_

- [ ] 21. 编写集成测试
  - 测试完整的表单提交流程（编辑模式）
  - 测试 API 集成和数据保存
  - 测试抽屉打开/关闭行为
  - 测试预览模式的完整流程
  - 测试错误场景和恢复
  - _需求: 所有需求的集成测试_

- [ ] 22. 识别和替换现有跳转逻辑
  - 已识别需要替换的具体文件位置：
    - `client/pages/omni-channel/pages/list/components/table/index.tsx` (第619-623行)
    - `client/pages/omni-channel/pages/new-list/components/table/operate/channel-setting.tsx` (第25-28行)
    - `client/pages/omni-channel/pages/new-list/jd-takeout/index.tsx` (第104-105行)
  - 分析每个位置的参数传递方式和数据流
  - 确认替换方案的可行性
  - _需求: 7.1, 7.2_

- [ ] 23. 替换页面跳转为抽屉打开
  - 替换主列表页面 (`table/index.tsx`) 的 history.push 为抽屉打开逻辑
  - 修改渠道设置组件 (`channel-setting.tsx`) 添加 onOpenEditDrawer 回调参数
  - 替换京东外卖页面 (`jd-takeout/index.tsx`) 的 window.open 为抽屉打开逻辑
  - 在每个父组件中添加抽屉状态管理和 EditDrawer 组件
  - 保持相同的参数传递（kdtId, channelId, accountType等）
  - _需求: 7.3, 7.4_

- [ ] 24. 实现抽屉关闭后的数据刷新
  - 在主列表页面的 onSuccess 回调中调用 `tableRef.current?.reload()`
  - 在京东外卖页面的 onSuccess 回调中调用 `tableRef.current?.reload()`
  - 在渠道设置组件中通过回调函数触发父组件的数据刷新
  - 处理抽屉关闭时的状态清理（重置 editDrawerProps）
  - 测试各个页面的数据刷新正确性
  - _需求: 7.5_

- [ ] 25. 最终集成和测试
  - 将抽屉组件集成到全渠道列表页面
  - 支持从不同入口打开编辑模式和预览模式
  - 测试所有替换后的跳转逻辑
  - 进行端到端测试
  - 验证所有需求是否满足
  - 进行性能优化和代码审查
  - _需求: 1.1, 5.1, 5.2, 5.3, 5.4, 5.5, 6.1, 6.2, 6.3, 6.4, 6.5, 7.1, 7.2, 7.3, 7.4, 7.5_