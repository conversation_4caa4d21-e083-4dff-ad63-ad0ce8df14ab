import { Router, Index, Metadata, Inject, API } from '@youzan/assets-route-plugin';
import _omit from 'lodash/omit';
import BaseController from './BaseController';
import XhsChannelsStatisticService from '../../../services/api/ebiz/video/channels/data/XhsChannelsStatisticService';
import { ShopAbilityUtil } from '@youzan/plugin-shop-ability';

@Router('xhs/statistics')
class StatisticsController extends BaseController {
  @Inject()
  private readonly XhsChannelsStatisticService!: XhsChannelsStatisticService;

  public async init() {
    this.channelName = 'xhs';
    super.init();
  }

  @Index(['', 'xhs-independent'])
  @Metadata('INDEX')
  async getStatisticsHtml() {
    const { ctx } = this;
    // 小红书独立版本判断
    const hasXhsIndependentAbility = await new ShopAbilityUtil(ctx).checkAbilityValid({
      keys: ['xhs_independent_ability'],
      kdtId: ctx.kdtId,
    });
    if (hasXhsIndependentAbility && ctx.url.indexOf('xhs-independent') < 0) {
      this.ctx.redirect('/v4/channel/xhs/statistics/xhs-independent');
      return;
    }
    ctx.setState('subtitle', '经营分析');

    return ctx.render('mini-app/xhs/statistics/index.html');
  }

  /**
   * 获取小红书流量转化数据
   */
  @API('GET', 'get-flow-data-statistic.json')
  public async getFlowDataStatistic() {
    const { dateType, startDay, endDay, channel } = this.ctx.getQuery();
    const params = {
      kdtId: this.ctx.kdtId,
      dateType,
      startDay,
      endDay,
      channel,
    };
    const result = await this.XhsChannelsStatisticService.getXhsFlowDataStatistic(params);
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 获取小红书交易转化数据
   */
  @API('GET', 'get-trade-data-statistic.json')
  public async getTradeDataStatistic() {
    const { dateType, startDay, endDay, channel } = this.ctx.getQuery();
    const params = {
      kdtId: this.ctx.kdtId,
      dateType,
      startDay,
      endDay,
      channel,
    };
    const result = await this.XhsChannelsStatisticService.getXhsTradeConvertDataStatistic(params);
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 流量概览数据
   */
  @API('GET', 'get-flow-overview-data-statistic.json')
  public async getFlowOverviewDataStatistic() {
    const { dateType, startDay, endDay } = this.ctx.getQuery();
    const params = {
      kdtId: this.ctx.kdtId,
      dateType,
      startDay,
      endDay,
    };
    const result = await this.XhsChannelsStatisticService.getXhsFlowOverviewDataStatistic(params);
    return this.ctx.json(
      0,
      'ok',
      {
        ...result,
        allVisitorNum: result.visitorNum,
      } || {},
    );
  }

  /**
   * 交易概览数据
   */
  @API('GET', 'get-trade-overview-data-statistic.json')
  public async getTradeOverviewDataStatistic() {
    const { dateType, startDay, endDay } = this.ctx.getQuery();
    const params = {
      kdtId: this.ctx.kdtId,
      dateType,
      startDay,
      endDay,
    };
    const result = await this.XhsChannelsStatisticService.getXhsTradeOverviewDataStatistic(params);
    return this.ctx.json(0, 'ok', {
      ...result,
      totalPayAmount: result?.paymentAmount,
    });
  }

  /**
   * 获取小红书流量趋势数据
   */
  @API('GET', 'get-flow-list-data-statistic.json')
  public async getFlowListDataStatistic() {
    const { dateType, startDay, endDay, channel } = this.ctx.getQuery();
    const params = {
      kdtId: this.ctx.kdtId,
      dateType,
      startDay,
      endDay,
      channel,
    };
    const result = await this.XhsChannelsStatisticService.getXhsFlowListDataStatistic(params);
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 获取小红书交易趋势数据
   */
  @API('GET', 'get-trade-list-data-statistic.json')
  public async getTradeListDataStatistic() {
    const { dateType, startDay, endDay, channel } = this.ctx.getQuery();
    const params = {
      kdtId: this.ctx.kdtId,
      dateType,
      startDay,
      endDay,
      channel,
    };
    const result = await this.XhsChannelsStatisticService.getXhsTradeListDataStatistic(params);
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 获取小红书商品分析
   */
  @API('GET', 'get-goods-list.json')
  public async getGoodsList() {
    const { dateType, startDay, endDay, channel, pageNo, pageSize } = this.ctx.getQuery();
    const params = {
      kdtId: this.ctx.kdtId,
      dateType,
      startDay,
      endDay,
      channel,
      pageNo,
      pageSize,
    };
    const result = await this.XhsChannelsStatisticService.getXhsItemTradeDataStatistic(params);
    return this.ctx.json(0, 'ok', result || {});
  }
}

export = StatisticsController;
