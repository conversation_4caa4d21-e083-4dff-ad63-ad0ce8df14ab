import { Router, API, Inject, Index } from '@youzan/assets-route-plugin';
import CommonBasicInfoController from '../common/BasicInfoController';
import QqMiniProgramService from '../../../services/api/channels/channel/core/QqMiniProgramService';
import ChannelAppQqMiniProgramService from '../../../services/api/channels/apps/QqMiniProgramService';
import channelMap from '../../../constants/miniAppChannelMap';
import BusinessException from '@youzan/wsc-pc-base/app/exceptions/BusinessException';
import AbilityReadService from '../../../services/api/shopcenter/shopprod/api/AbilityReadService';

@Router('qq/basic-info')
class BasicInfoController extends CommonBasicInfoController {
  @Inject()
  private readonly qqMiniProgramService!: QqMiniProgramService;

  @Inject()
  private readonly channelAppQqMiniProgramService!: ChannelAppQqMiniProgramService;

  @Inject()
  private readonly abilityReadService!: AbilityReadService;

  public async init() {
    this.channelName = 'qq';
    super.init();
  }

  @Index('')
  public async getBaseInfoHtml() {
    await this.checkIsBind();
    const { ctx } = this;
    ctx.setState('subtitle', '基础信息');
    return ctx.render('mini-app/common/basic-info/index.html');
  }

  @Index('settings')
  public async getBaseInfoSettingsHtml() {
    await this.checkIsBind();
    const { ctx } = this;
    ctx.setState('subtitle', '基础设置');
    return ctx.render('mini-app/common/basic-info/settings.html');
  }

  @Index('to-audit-reason')
  public async toAuditReasonHtml() {
    const { ctx } = this;
    const { kdtId, request } = ctx;
    const { businessType = 1, url } = request.query;
    const res = await this.channelAppQqMiniProgramService.getVerifyResultSkipUrl({
      url: String(url),
      kdtId,
      businessType,
      accountType: channelMap.qq.accountType,
    });
    if (res && res.url) {
      return ctx.redirect(decodeURIComponent(res.url));
    }
    // 返回url为空则说明qq接口返回为空 直接报错跳转报错页
    throw new BusinessException(12345, '暂无详细内容');
  }

  @API('GET', 'commericialize-ability.json')
  public async getCommercializeAbility() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const abilityInfo = await this.abilityReadService.queryShopAbilityInfo(
      kdtId,
      'wsc_ext_pkg_plugin_ability',
    );
    return ctx.json(0, 'ok', abilityInfo);
  }

  /**
   * 获取 QQ 账号信息
   */
  @API('GET', 'account-info.json')
  public async getAccountInfoJson() {
    const accountInfo = await this.getAccountInfo();
    const { headImageInfo = { headImageUrl: '' }, signatureInfo = { signature: '' } } = accountInfo;
    this.ctx.json(0, 'ok', {
      name: accountInfo.appName,
      avatar: headImageInfo.headImageUrl,
      description: signatureInfo.signature,
      category: (accountInfo.appCategory || []).map((item: string) => {
        const categorys = item.split('_');
        const category = {
          firstCategoryName: '',
          secondCategoryName: '',
          thirdCategoryName: '',
        };
        categorys.forEach((c, i) => {
          if (i === 0) {
            category.firstCategoryName = c;
          } else if (i === 1) {
            category.secondCategoryName = c;
          } else if (i === 2) {
            category.thirdCategoryName = c;
          }
        });
        return category;
      }),
      appId: accountInfo.appId,
      principal: accountInfo.principalName,
    });
  }

  protected async getAccountInfo() {
    const { kdtId, request } = this.ctx;
    const { businessType = 1 } = request.query;
    const result = await this.qqMiniProgramService.getQqMiniProgramAccountInfo({
      businessType,
      accountType: channelMap.qq.accountType,
      kdtId,
    });
    return result;
  }
}

export = BasicInfoController;
