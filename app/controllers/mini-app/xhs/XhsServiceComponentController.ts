import { Router, API, Inject } from '@youzan/assets-route-plugin';
import BaseController from './BaseController';
import XhsServiceComponentService from '../../../services/api/xhs/XhsServiceComponentService';

/**
 * 通用提审业务
 */
@Router('xhs/service-component')
class AuthController extends BaseController {
  @Inject()
  public readonly xhsServiceComponentService!: XhsServiceComponentService;

  public async init() {
    this.channelName = 'xhs';
    super.init();
  }

  // 有赞主体认证名称和小红书小程序主体认证名称是否一致
  @API('GET', 'open.json')
  public async open() {
    const { query } = this.ctx.request;
    const { kdtId, rootKdtId = kdtId }: { kdtId: number; rootKdtId?: number } = this.ctx.getState(
      'shopMetaInfo',
    );
    const { itemId } = query;
    const params = { kdtId, rootKdtId, itemId: +itemId };
    const response = await this.xhsServiceComponentService.open(params);

    return this.ctx.json(0, 'ok', response);
  }

  @API('GET', 'close.json')
  public async close() {
    const { query } = this.ctx.request;
    const { kdtId, rootKdtId = kdtId }: { kdtId: number; rootKdtId?: number } = this.ctx.getState(
      'shopMetaInfo',
    );
    const { itemId } = query;
    const params = { kdtId, rootKdtId, itemId: +itemId };
    const response = await this.xhsServiceComponentService.close(params);

    return this.ctx.json(0, 'ok', response);
  }

  @API('GET', 'getShopAuditStatus.json')
  public async getShopAuditStatus() {
    const { query } = this.ctx.request;
    const { itemId } = query;
    const { kdtId } = this.ctx.getState('shopMetaInfo');

    const response = await this.xhsServiceComponentService.getShopAuditStatus(kdtId, +itemId);
    return this.ctx.json(0, 'ok', response);
  }
}

export = AuthController;
