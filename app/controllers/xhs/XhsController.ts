import BaseController from '../base/BaseController';
import { Router, Inject, Index } from '@youzan/assets-route-plugin';
import ShopConfigReadService from '../../services/api/shopcenter/shopconfig/ShopConfigReadService';
import ShopConfigWriteService from '../../services/api/shopcenter/shopconfig/ShopConfigWriteService';
import TiktokUnderwritingService from '../../services/api/channels/tiktok/TiktokUnderwritingService';

@Router('')
class XhsController extends BaseController {
  public async init() {
    await super.init();
  }

  @Inject()
  ShopConfigReadService!: ShopConfigReadService;

  @Inject()
  shopConfigWriteService!: ShopConfigWriteService;

  @Inject()
  tiktokUnderwritingService!: TiktokUnderwritingService;

  @Index(['', 'index']) // 添加index路由匹配
  public async getBaseInfoHtml() {
    const { ctx } = this;
    ctx.setGlobal('channelMeta', {
      id: 'xhs',
    });
    return ctx.render('omni-channel/xhs.html');
  }
}

export = XhsController;
