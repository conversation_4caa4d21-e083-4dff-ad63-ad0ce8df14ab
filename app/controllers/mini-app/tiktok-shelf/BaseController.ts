import MiniAppBaseController from '../BaseController';
import PermiseCheckError from '../../../exceptions/PremiseCheckError';

class TiktokBaseController extends MiniAppBaseController {
  public async init() {
    this.channelName = 'tiktok-shelf';
    super.init();
  }

  protected async checkIsBind() {
    const bindInfo = await this.getBindInfo();
    if (!bindInfo) {
      throw new PermiseCheckError(12345, '抖音小程序未授权');
    } else {
      return bindInfo;
    }
  }
}

export default TiktokBaseController;
