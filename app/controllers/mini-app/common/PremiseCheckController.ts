import { Index, Router } from '@youzan/assets-route-plugin';
import BaseController from '../BaseController';
import channelMap from '../../../constants/miniAppChannelMap';

/**
 * 前置条件验证
 */
@Router('premise-check')
class PremiseCheckController extends BaseController {
  public async init() {
    const { ctx } = this;
    const { channelName } = ctx.query;
    const currentChannelMeta = (channelMap as any)[channelName];
    if (currentChannelMeta) {
      this.channelName = channelName;
    }
    super.init();
  }

  @Index('')
  async getIndexHtml() {
    const { ctx } = this;
    ctx.setState('subtitle', '小程序店铺');
    await ctx.render('mini-app/common/premise-check/index.html');
  }
}

export = PremiseCheckController;
