import { Router, Index, Metadata, Inject, API } from '@youzan/assets-route-plugin';
import { checkBranchStore, checkHqStore, checkPartnerStore } from '@youzan/utils-shop';
import _omit from 'lodash/omit';
import BaseController from './BaseController';
import WechatStatisticService from '../../../services/api/mall/item/WechatStatisticService';
import ItemQueryService from '../../../services/api/ic/manage/ItemQueryService';
import VideoChannelsStatisticApi from '../../../services/api/mall/item/VideoChannelsStatisticApi';
import { wxVideoGrayAuthSwitch } from '../../../utils/grayUtils';

@Router('wechat-video/statistics')
class StatisticsController extends BaseController {
  @Inject()
  private readonly wechatStatisticService!: WechatStatisticService;

  @Inject()
  private readonly itemQueryService!: ItemQueryService;

  @Inject()
  private readonly VideoChannelsStatisticApi!: VideoChannelsStatisticApi;

  public async init() {
    this.channelName = 'wechat-video';
    super.init();
  }

  isInWhiteList() {
    const isInWhiteListShift = this.isInWhiteListByApollo('statistics', {
      appId: 'wsc-pc-wxvideo',
      namespace: 'wsc-pc-wxvideo.migrate',
      useRootKdtId: true,
    });

    if (isInWhiteListShift) {
      const url = '//www.youzan.com/v4/wxvideo/video/wechat-video/statistics';
      this.ctx.redirect(url);
    }

    return isInWhiteListShift;
  }

  @Index('')
  @Metadata('INDEX')
  async getStatisticsHtml() {
    const { ctx } = this;

    if (this.isInWhiteList()) {
      return;
    }

    if (!wxVideoGrayAuthSwitch(ctx)) {
      return;
    }

    await this.initGlobal();

    return ctx.render('video-app/wechat-video/statistics.html');
  }

  async initGlobal() {
    const { ctx } = this;
    const [subShopList = []] = await Promise.all([this.getSubShopList()]);

    const pageData = {
      subShopList, // 所有分店信息
    };

    ctx.setGlobal('pageData', pageData);
  }

  /**
   * @description 获取网店列表数据
   * @param {*} ctx
   */
  async getSubShopList() {
    const { ctx } = this;
    // 连锁总店店铺或合伙人店铺，获取下属全部网店的列表
    const shopMetaInfo = ctx.getState('shopMetaInfo');
    if (checkHqStore(shopMetaInfo) || checkPartnerStore(shopMetaInfo)) {
      const operatorParam = this.getOperatorParams();
      const subShopParam = Object.assign(operatorParam, { hqKdtId: ctx.kdtId });
      return this.itemQueryService
        .getTotalCountSubShop(_omit(subShopParam, 'kdtId'))
        .catch(e => ctx.toLog('视频号数据——获取全部网店列表失败', e));
    }
    return new Promise<void>(resolve => resolve());
  }

  getChainParams() {
    const { searchKdtId, ...rest } = this.ctx.getQuery();
    const userId = +this.ctx.userId;
    let kdtId = +this.ctx.kdtId;
    let hqKdtId = +this.ctx.kdtId;
    const shopMetaInfo = this.ctx.getState('shopMetaInfo');
    const shopInfo = this.ctx.getState('shopInfo');
    const { rootKdtId } = shopInfo;
    // 店铺为连锁总店或合伙人角色则查询店铺字段使用searchKdt
    if (checkHqStore(shopMetaInfo) || checkPartnerStore(shopMetaInfo)) {
      kdtId = searchKdtId;
      hqKdtId = rootKdtId as number;
    } else if (checkBranchStore(shopMetaInfo)) {
      hqKdtId = rootKdtId as number;
    }
    return {
      ...rest,
      kdtId: !kdtId ? null : kdtId,
      hqKdtId,
      userId,
    };
  }

  /**
   * 获取视频号流量统计数据
   */
  @API('GET', 'get-flow-data-statistic.json')
  public async getFlowDataStatistic() {
    const { dateType, startDay, endDay, channel } = this.ctx.getQuery();
    const params = {
      kdtId: this.ctx.kdtId,
      dateType,
      startDay,
      endDay,
      channel,
    };
    const result = await this.wechatStatisticService.getFlowDataStatistic(params);
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 获取连锁视频号流量统计数据
   */
  @API('GET', 'get-chain-flow-data-statistic.json')
  public async getChainFlowDataStatistic() {
    const {
      dateType,
      startDay,
      endDay,
      channel,
      responseId,
      kdtId,
      hqKdtId,
      userId,
    } = this.getChainParams();
    const params = {
      kdtId,
      hqKdtId,
      userId,
      dateType,
      startDay,
      endDay,
      channel,
      responseId,
    };
    const result = await this.wechatStatisticService.getChainFlowDataStatistic(params);
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 获取视频号交易统计数据
   */
  @API('GET', 'get-trade-data-statistic.json')
  public async getTradeDataStatistic() {
    const { dateType, startDay, endDay, channel } = this.ctx.getQuery();
    const params = {
      kdtId: this.ctx.kdtId,
      dateType,
      startDay,
      endDay,
      channel,
    };
    const result = await this.wechatStatisticService.getTradeDataStatistic(params);
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 获取连锁视频号交易统计数据
   */
  @API('GET', 'get-chain-trade-data-statistic.json')
  public async getChainTradeDataStatistic() {
    const {
      dateType,
      startDay,
      endDay,
      channel,
      responseId,
      kdtId,
      hqKdtId,
      userId,
    } = this.getChainParams();
    const params = {
      kdtId,
      hqKdtId,
      userId,
      dateType,
      startDay,
      endDay,
      channel,
      responseId,
    };
    const result = await this.wechatStatisticService.getChainTradeDataStatistic(params);
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 流量概览数据
   */
  @API('GET', 'get-flow-overview-data-statistic.json')
  public async getFlowOverviewDataStatistic() {
    const { dateType, startDay, endDay } = this.ctx.getQuery();
    const params = {
      kdtId: this.ctx.kdtId,
      dateType,
      startDay,
      endDay,
    };
    const result = await this.wechatStatisticService.getFlowOverviewDataStatistic(params);
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 连锁流量概览数据
   */
  @API('GET', 'get-chain-flow-overview-data-statistic.json')
  public async getChainFlowOverviewDataStatistic() {
    const {
      dateType,
      startDay,
      endDay,
      responseId,
      kdtId,
      hqKdtId,
      userId,
    } = this.getChainParams();
    const params = {
      kdtId,
      hqKdtId,
      userId,
      dateType,
      startDay,
      endDay,
      responseId,
    };
    const result = await this.wechatStatisticService.getChainFlowOverviewDataStatistic(params);
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 交易概览数据
   */
  @API('GET', 'get-trade-overview-data-statistic.json')
  public async getTradeOverviewDataStatistic() {
    const { dateType, startDay, endDay } = this.ctx.getQuery();
    const params = {
      kdtId: this.ctx.kdtId,
      dateType,
      startDay,
      endDay,
    };
    const result = await this.wechatStatisticService.getTradeOverviewDataStatistic(params);
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 连锁交易概览数据
   */
  @API('GET', 'get-chain-trade-overview-data-statistic.json')
  public async getChainTradeOverviewDataStatistic() {
    const {
      dateType,
      startDay,
      endDay,
      responseId,
      kdtId,
      hqKdtId,
      userId,
    } = this.getChainParams();
    const params = {
      kdtId,
      hqKdtId,
      userId,
      dateType,
      startDay,
      endDay,
      responseId,
    };
    const result = await this.wechatStatisticService.getChainTradeOverviewDataStatistic(params);
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 获取视频号流量趋势数据
   */
  @API('GET', 'get-flow-list-data-statistic.json')
  public async getFlowListDataStatistic() {
    const { dateType, startDay, endDay, channel } = this.ctx.getQuery();
    const params = {
      kdtId: this.ctx.kdtId,
      dateType,
      startDay,
      endDay,
      channel,
    };
    const result = await this.wechatStatisticService.getFlowListDataStatistic(params);
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 获取连锁视频号流量趋势数据
   */
  @API('GET', 'get-chain-flow-list-data-statistic.json')
  public async getChainFlowListDataStatistic() {
    const {
      dateType,
      startDay,
      endDay,
      channel,
      responseId,
      kdtId,
      hqKdtId,
      userId,
    } = this.getChainParams();
    const params = {
      kdtId,
      hqKdtId,
      userId,
      dateType,
      startDay,
      endDay,
      channel,
      responseId,
    };
    const result = await this.wechatStatisticService.getChainFlowListDataStatistic(params);
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 获取视频号交易趋势数据
   */
  @API('GET', 'get-trade-list-data-statistic.json')
  public async getTradeListDataStatistic() {
    const { dateType, startDay, endDay, channel } = this.ctx.getQuery();
    const params = {
      kdtId: this.ctx.kdtId,
      dateType,
      startDay,
      endDay,
      channel,
    };
    const result = await this.wechatStatisticService.getTradeListDataStatistic(params);
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 获取连锁视频号交易趋势数据
   */
  @API('GET', 'get-chain-trade-list-data-statistic.json')
  public async getChainTradeListDataStatistic() {
    const {
      dateType,
      startDay,
      endDay,
      channel,
      responseId,
      kdtId,
      hqKdtId,
      userId,
    } = this.getChainParams();
    const params = {
      kdtId,
      hqKdtId,
      userId,
      dateType,
      startDay,
      endDay,
      channel,
      responseId,
    };
    const result = await this.wechatStatisticService.getChainTradeListDataStatistic(params);
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 获取交易数据的导出url
   */
  @API('GET', 'get-trade-data-export-url.json')
  public async getTradeDataExportUrl() {
    const { dateType, startDay, endDay, channel } = this.ctx.getQuery();
    const params = {
      kdtId: this.ctx.kdtId,
      dateType,
      startDay,
      endDay,
      channel,
    };
    const result = await this.VideoChannelsStatisticApi.uploadTradeDataStatistic(params);
    return this.ctx.json(0, 'ok', result || '');
  }

  /**
   * 获取连锁交易数据的导出url
   */
  @API('GET', 'get-chain-trade-data-export-url.json')
  public async getChainTradeDataExportUrl() {
    const {
      dateType,
      startDay,
      endDay,
      channel,
      responseId,
      kdtId,
      hqKdtId,
      userId,
    } = this.getChainParams();
    const params = {
      kdtId,
      hqKdtId,
      userId,
      dateType,
      startDay,
      endDay,
      channel,
      responseId,
    };
    const result = await this.VideoChannelsStatisticApi.uploadChainTradeDataStatistic(params);
    return this.ctx.json(0, 'ok', result || '');
  }

  /**
   * 获取流量数据的导出url
   */
  @API('GET', 'get-flow-data-export-url.json')
  public async getFlowDataExportUrl() {
    const { dateType, startDay, endDay, channel } = this.ctx.getQuery();
    const params = {
      kdtId: this.ctx.kdtId,
      dateType,
      startDay,
      endDay,
      channel,
    };
    const result = await this.VideoChannelsStatisticApi.uploadFlowDataStatistic(params);
    return this.ctx.json(0, 'ok', result || '');
  }

  /**
   * 获取连锁流量数据的导出url
   */
  @API('GET', 'get-chain-flow-data-export-url.json')
  public async getChainFlowDataExportUrl() {
    const {
      dateType,
      startDay,
      endDay,
      channel,
      responseId,
      kdtId,
      hqKdtId,
      userId,
    } = this.getChainParams();
    const params = {
      kdtId,
      hqKdtId,
      userId,
      dateType,
      startDay,
      endDay,
      channel,
      responseId,
    };
    const result = await this.VideoChannelsStatisticApi.uploadChainFlowDataStatistic(params);
    return this.ctx.json(0, 'ok', result || '');
  }
}

export = StatisticsController;
