import { Router, API, Inject, Index } from '@youzan/assets-route-plugin';
import CommonBasicInfoController from '../common/BasicInfoController';
import AlipayAccountService from '../../../services/api/channels/channel/core/AlipayAccountService';
import channelMap from '../../../constants/miniAppChannelMap';

@Router('alipay/basic-info')
class BasicInfoController extends CommonBasicInfoController {
  @Inject()
  private readonly alipayAccountService!: AlipayAccountService;

  public async init() {
    this.channelName = 'alipay';
    super.init();
  }

  @Index('')
  public async getBaseInfoHtml() {
    await this.checkIsBind();
    const { ctx } = this;
    ctx.setState('subtitle', '基础信息');
    return ctx.render('mini-app/common/basic-info/index.html');
  }

  @Index('settings')
  public async getBaseInfoSettingsHtml() {
    await this.checkIsBind();
    const { ctx } = this;
    ctx.setState('subtitle', '基础设置');
    return ctx.render('mini-app/common/basic-info/settings.html');
  }

  /**
   * 获取支付宝所有类目
   */
  @API('GET', 'all-category.json')
  public async getAllCategoryUrl() {
    const { kdtId, request } = this.ctx;
    const { businessType = 1 } = request.query;
    this.validator.required(businessType, 'businessType 不能为空');
    const result = await this.alipayAccountService.getAllAlipayCategory({
      businessType,
      accountType: channelMap.alipay.accountType,
      kdtId,
    });
    await this.ctx.json(0, 'ok', result);
  }

  /**
   * 获取支付宝账号信息
   */
  @API('GET', 'account-info.json')
  public async getAccountInfoJson() {
    const accountInfo = await this.getAccountInfo();
    await this.ctx.json(0, 'ok', {
      name: accountInfo.appName,
      avatar: accountInfo.appLogo,
      description: accountInfo.appDesc,
      category: accountInfo.alipayCategoryDTOList,
      appId: accountInfo.appId,
      slogan: accountInfo.appSlogan,
      englishName: accountInfo.appEnglishName,
      principal: accountInfo.principalName,
      registerSource: accountInfo.createdFrom,
    });
  }

  protected async getAccountInfo() {
    const { kdtId, request } = this.ctx;
    const { businessType = 1 } = request.query;
    const result = await this.alipayAccountService.getAlipayAccountInfo({
      businessType,
      accountType: channelMap.alipay.accountType,
      kdtId,
    });
    return result;
  }
}

export = BasicInfoController;
