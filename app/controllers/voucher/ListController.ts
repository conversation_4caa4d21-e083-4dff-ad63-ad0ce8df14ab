import { Router, Index, Inject, API } from '@youzan/assets-route-plugin';
import { checkRetailSingleStore } from '@youzan/utils-shop';
import { ShopAbilityUtil } from '@youzan/plugin-shop-ability';

import BaseController from './BaseController';
import ThirdActivityService from '../../services/api/retail/trade/ThirdActivityService';
import HQStoreSearchService from '../../services/api/retail/shop/HQStoreSearchService';
import ChannelShopService from '../../services/api/channels/omni-channel/ChannelShopService';
import GrayService from '../../services/api/channels/channel/core/GrayService';
import SalesChannelMgrService from '../../services/api/shopcenter/shopfront/SalesChannelMgrService';
import ChannelCoreAccountService from '../../services/api/channels/channel/core/ChannelCoreAccountService';
import ShopConfigReadService from '../../services/api/shopcenter/shopconfig/ShopConfigReadService';
import ShopConfigWriteService from '../../services/api/shopcenter/shopconfig/ShopConfigWriteService';
import lodash from 'lodash';
import CouponService from '../../services/api/channels/omni-channel/CouponService';

/** 卡券操作 */
enum VoucherAction {
  Suspend = 1,
  Delete = 2,
}

@Router('voucher-channel')
class ListController extends BaseController {
  public async init() {
    this.channelName = 'voucher-channel';
    super.init();
  }

  @Inject() couponService!: CouponService;

  @Inject()
  public readonly channelCoreAccountService!: ChannelCoreAccountService;

  @Inject()
  public readonly ThirdActivityService!: ThirdActivityService;

  @Inject()
  public readonly HQStoreSearchService!: HQStoreSearchService;

  @Inject()
  public readonly ChannelShopService!: ChannelShopService;

  @Inject()
  public readonly salesChannelMgrService!: SalesChannelMgrService;

  @Inject()
  public readonly ShopConfigReadService!: ShopConfigReadService;

  @Inject()
  public readonly ShopConfigWriteService!: ShopConfigWriteService;

  @Inject()
  public readonly grayService!: GrayService;

  @API('POST', 'sync-from-third.json')
  public async syncFromThird() {
    const { kdtId, request, userId } = this.ctx;
    const { body } = request;

    const { thirdActivityIds } = body;

    const result = await this.ThirdActivityService.syncFromThird({
      kdtId,
      thirdActivityIds,
      adminId: userId,
    });

    return this.ctx.json(0, 'ok', result || {});
  }

  // 判断是否是新流程北极星（休闲娱乐类目）
  async getIsNewPolarStar() {
    const rootKdtId = this.getRootKdtId();
    const relaxedChannelShopList = await this.couponService
      .isMtRelaxedChanelShop({
        rootKdtId: this.getRootKdtId(),
        adminId: this.ctx.userId,
        retailSource: 'wsc-pc-channel',
        platform: 1,
      })
      .catch(err => {
        this.ctx.logger.error('判断是否是新流程北极星（休闲娱乐类目）失败', err);
        return {};
      });

    return Boolean(relaxedChannelShopList[rootKdtId]);
  }

  @API('POST', 'update-activity-info.json')
  public async updateActivityInfo() {
    const { kdtId, request, userId } = this.ctx;
    const { body } = request;
    const {
      applicableOnlineGoodsIds,
      goodsRangeType,
      id,
      thirdActivityId,
      applicableOfflineGoodsRangeType,
      applicableOfflineGoodsIds = [],
      applicableOnlineGoodsWithSkuIds = [],
      applicableOfflineGoodsWithSkuIds = [],
      multiChannel = false,
    } = body;

    const result = await this.ThirdActivityService.updateActivityInfo({
      id,
      kdtId,
      applicableOnlineGoodsIds,
      applicableOnlineGoodsWithSkuIds,
      goodsRangeType,
      thirdActivityId,
      adminId: userId,
      multiChannel,
      ...(applicableOfflineGoodsRangeType !== undefined
        ? {
            applicableOfflineGoodsRangeType,
            applicableOfflineGoodsIds,
            applicableOfflineGoodsWithSkuIds,
          }
        : {}),
    });

    return this.ctx.json(0, 'ok', result || {});
  }

  @Index(['', 'index', 'tiktok', 'xhs-independent-index'])
  public async getBaseInfoHtml() {
    const { ctx } = this;
    // 小红书独立版本判断
    const hasXhsIndependentAbility = await new ShopAbilityUtil(this.ctx).checkAbilityValid({
      keys: ['xhs_independent_ability'],
      kdtId: this.ctx.kdtId,
    });
    if (hasXhsIndependentAbility && this.ctx.url.indexOf('xhs-independent') < 0) {
      const currentPath = this.ctx.url.split('/').pop() || '';
      const redirectPath = `/v4/channel/voucher/voucher-channel${
        currentPath ? `/xhs-independent-${currentPath}` : ''
      }`;
      this.ctx.redirect(redirectPath);

      return;
    }
    const { kdtId, userId } = ctx;
    const shopMetaInfo = ctx.getState('shopMetaInfo');
    const isRetailSingleStore = checkRetailSingleStore(shopMetaInfo);
    const isSupportMultiChannel = isRetailSingleStore
      ? false
      : await this.salesChannelMgrService.isHitWhiteList(kdtId);
    const isOpenSkuMode = await this.grayService.inGrayForCoupon(kdtId).catch(() => false);

    const rootKdtId = lodash.get(this.ctx.getState('shopInfo'), 'rootKdtId', kdtId);
    const [tiktokInfo, isLiteOnlineStoreManager, isNewPolarStar] = await Promise.all([
      this.channelCoreAccountService.queryMpBindInfoByKdtId({
        businessType: 1,
        accountType: 21,
        externalId: rootKdtId,
      }),
      this.checkLiteOnlineStoreManager(),
      this.getIsNewPolarStar(),
    ]);

    const thirdChannelList = await this.ChannelShopService.querySupportThirdChannelList({
      kdtId,
      adminId: userId,
      needAuthInfo: true,
      cloudStore: isLiteOnlineStoreManager,
    });

    // 是否绑定抖音小程序
    ctx.setGlobal('isBindTiktokApp', tiktokInfo?.accountType === 21);
    ctx.setGlobal('isSupportMultiChannel', isSupportMultiChannel);
    ctx.setGlobal('thirdChannelList', thirdChannelList);
    ctx.setGlobal('isOpenSkuMode', isOpenSkuMode);

    ctx.setGlobal('isNewPolarStar', isNewPolarStar);

    // 新建卡券，抖音、美团点评隐藏“实付价”
    this.isInWhiteListByApollo('hide_voucher_pay_amount', {
      useRootKdtId: true,
      globalKey: 'hidePayAmount',
    });

    return ctx.render('voucher/index.html');
  }

  @Index('plugin')
  public async getPluginIndexHTML() {
    this.ctx.setGlobal('isVoucherChannelPlugin', true);
    return this.getBaseInfoHtml();
  }

  /**
   * 获取所有店铺列表
   */
  @API('GET', 'all-shop-list.json')
  public async getAllShopList() {
    const { kdtId, userId, request } = this.ctx;
    // const shopInfo = this.ctx.getState('shopInfo');
    // const hqKdtId = shopInfo.rootKdtId;
    const retailSource = 'wsc-pc-channel';
    const { channelType = 800 } = request.query;

    const params = {
      // extends 中解析JSON数据
      ...request.query,
      channelIds: [+channelType],
      kdtId,
      // hqKdtId,
      adminId: userId,
      retailSource,
    };
    const res = await this.ChannelShopService.queryChannelShops(params);
    // const res = await this.HQStoreSearchService.searchWithDataPermission(params);
    return this.ctx.json(0, 'success', res);
  }

  @API('GET', 'query-activity-detail.json')
  public async queryActivityDetail() {
    const { kdtId, userId, request } = this.ctx;
    const { thirdActivityId, id } = request.query;
    const retailSource = 'wsc-pc-channel';

    const params = {
      id,
      thirdActivityId,
      kdtId,
      adminId: userId,
      retailSource,
    };

    const res = await this.ThirdActivityService.queryActivityDetail(params);
    return this.ctx.json(0, 'success', res);
  }

  @API('POST', 'create-activity.json')
  public async createActivity() {
    const { kdtId, userId, request } = this.ctx;
    const retailSource = 'wsc-pc-channel';

    const params = {
      ...request.body,
      kdtId,
      adminId: userId,
      retailSource,
    };

    const res = await this.ThirdActivityService.createActivity(params);
    return this.ctx.json(0, 'success', res);
  }

  @API('GET', 'query-shop-config.json')
  public async queryShopConfig() {
    const res = await this.ShopConfigReadService.queryShopConfigs(
      this.ctx.kdtId,
      ['third_coupon_amount_sync', 'use_third_coupon_setting'],
    );
    return this.ctx.json(0, 'success', res);
  }

  @API('POST', 'update-shop-config.json')
  public async updateShopConfig() {
    const { kdtId, request } = this.ctx;
    const { configs } = request.body;
    const baseOperator = this.getOperatorParams();
    const res = await this.ShopConfigWriteService.setShopConfigs({
      kdtId,
      configs,
      operator: {
        //  后端申请key 的时候，填写的这个应用名，只能这个来源才有权限
        fromApp: 'retail-trade-misc',
        name: baseOperator.operator.nickName,
        id: baseOperator.operator.userId,
        type: 1,
      },
    });
    return this.ctx.json(0, 'success', res);
  }

  @API('POST', 'update-activity.json')
  public async updateActivity() {
    const { kdtId, userId, request } = this.ctx;
    const retailSource = 'wsc-pc-channel';

    const params = {
      ...request.body,
      kdtId,
      adminId: userId,
      retailSource,
    };

    const res = await this.ThirdActivityService.updateActivity(params);
    return this.ctx.json(0, 'success', res);
  }

  private getListActionParams(type: number) {
    const { kdtId, userId, request } = this.ctx;
    const { yzActivityId, thirdActivityId } = request.body;

    return {
      kdtId,
      adminId: userId,
      yzActivityId,
      thirdActivityId,
      operationType: type,
    };
  }

  @API('POST', 'suspend-activity.json')
  public async suspendActivity() {
    const res = await this.ThirdActivityService.suspendSend(
      this.getListActionParams(VoucherAction.Suspend),
    );
    return this.ctx.success(res);
  }

  @API('POST', 'delete-activity.json')
  public async deleteActivity() {
    const res = await this.ThirdActivityService.deleteActivity(
      this.getListActionParams(VoucherAction.Delete),
    );
    return this.ctx.success(res);
  }
}

export = ListController;
