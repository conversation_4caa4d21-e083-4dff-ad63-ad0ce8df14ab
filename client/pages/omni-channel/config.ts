import { IsOpenFusion } from 'pages/mini-app/tiktok-shelf/goods/constant';
import { isSupportMultiChannel } from './constant';

export const StoreHandleConfig = {
  storeOperatingStatus: 'storeOperatingStatus',
  synchronizationOfBusinessHours: 'synchronizationOfBusinessHours',
  InventorySynchronizationRatio: 'InventorySynchronizationRatio',
  StockInventoryRatio: 'StockInventoryRatio',
  PlanStockInventoryRatio: 'PlanStockInventoryRatio',
  stockRealTimeSync: 'stockThreshold',
  OutItemTypeSync: 'outItemTypeSync',
  OrderInvoiceSwitch: 'allowOpenInvoice',
  HqTemplateInfoSync: 'hqTemplateInfoSync',
  OutItemInfoSync: 'outItemInfoSync',
  ShopStatusRelatedChannels: 'shopStatusRelatedChannels',
  itemSyncRange: 'itemSyncRange',
  itemAutoPublishSync: 'itemAutoPublishSync',
};
export const StoreHandleItems = {
  [StoreHandleConfig.storeOperatingStatus]: '店铺经营状态',
  [StoreHandleConfig.synchronizationOfBusinessHours]: '营业时间同步',
  [StoreHandleConfig.InventorySynchronizationRatio]: '库存共享比例',
  [StoreHandleConfig.StockInventoryRatio]: '实物可售库存',
  [StoreHandleConfig.PlanStockInventoryRatio]: '计划可售库存',
  [StoreHandleConfig.stockRealTimeSync]: '库存实时同步',
  [StoreHandleConfig.OrderInvoiceSwitch]: '外卖订单开票设置',
  [StoreHandleConfig.HqTemplateInfoSync]: '对内更新策略',
  [StoreHandleConfig.OutItemInfoSync]: '对外同步策略',
  [StoreHandleConfig.ShopStatusRelatedChannels]: '渠道经营状态',
  [StoreHandleConfig.itemSyncRange]: '更新范围',
  [StoreHandleConfig.itemAutoPublishSync]: '商品库发布外卖商品时',
};

/**
 * 批量操作类型
 */
export enum BatchActionType {
  /** 店铺经营状态 */
  StoreOperatingStatus = 'storeOperatingStatus',
  /** 营业时间同步 */
  SynchronizationOfBusinessHours = 'synchronizationOfBusinessHours',
  /** 库存共享设置 */
  StockSetting = 'stockSetting',
  /** 批量授权 */
  batchAuth = 'batchAuth',
  /** 批量导入 */
  batchImport = 'batchImport',
  /** 渠道经营状态 */
  ShopStatusRelatedChannels = 'shopStatusRelatedChannels',
  /** 抖音接单设置 */
  DyServeSetting = 'dyServeSetting',
}
export const batchActionTypeOptions = [
  {
    key: BatchActionType.StoreOperatingStatus,
    name: '店铺经营状态',
  },
  {
    key: BatchActionType.SynchronizationOfBusinessHours,
    name: '营业时间同步',
  },
  !isSupportMultiChannel && {
    key: BatchActionType.StockSetting,
    name: '库存共享设置',
  },
].filter(Boolean) as Array<{
  key: BatchActionType;
  name: string;
}>;

export const tiktokBatchActionTypeOptions = [
  {
    key: BatchActionType.ShopStatusRelatedChannels,
    name: '渠道经营状态 ',
  },
  IsOpenFusion && {
    key: BatchActionType.DyServeSetting,
    name: '抖音接单设置',
  },
  {
    key: BatchActionType.batchAuth,
    name: '批量授权',
  },
  {
    key: BatchActionType.batchImport,
    name: '渠道店铺导入',
  },
].filter(Boolean) as Array<{
  key: BatchActionType;
  name: string;
}>;

export enum EStoreOperatingStatus {
  off,
  on,
}

export enum EsynchronizationOfBusinessHours {
  off,
  on,
}

export enum AllowOpenInvoiceStatus {
  off = 'false',
  on = 'true',
}

export enum GoodsUpdateRuleItem {
  // 分组
  group = 'group',
  // 商品名称
  title = 'title',
  // 商品价格
  price = 'price',
  // 打包费
  packingCharge = 'packingCharge',
  // 最小购买量
  minimumPurchaseNum = 'minimumPurchaseNum',
  // 商品属性
  prop = 'prop',
  // 库存
  stockNum = 'stockNum',
  // 规格启/禁用
  skuDisableStatus = 'skuDisableStatus',
}

export enum YouzanGoodsUpdateRuleItem {
  // 分组
  group = 'group',
  // 商品名称
  title = 'title',
  // 商品价格
  price = 'price',
  // 打包费
  packingCharge = 'packingCharge',
  // 最小购买量
  minimumPurchaseNum = 'minimumPurchaseNum',
  // 商品属性
  prop = 'prop',
  // 库存
  stockNum = 'stockNum',
  // 商品图片
  picture = 'picture',
  // 商品类目
  category = 'category',
  // 商品描述
  sellPoint = 'sellPoint',
  // 商品条码
  barcode = 'barcode',
  // 规格名称
  skuName = 'skuName',
  // 销售状态（上下架）
  takeUpOrDown = 'takeUpOrDown',
}

export enum ChannelStatus {
  unSubscribe = 0,
  unBind = 1,
  alBind = 2,
}

export enum EItemSyncRange {
  all = '0',           // 全部信息
  partial = '1',       // 指定部分信息
  bindOnly = '2',      // 仅绑定商品，不更新信息
}

export enum EAutoPublishSync {
  auto = '1',          // 自动发布创建到外卖平台
  manual = '0',        // 手动同步创建到外卖平台
}
