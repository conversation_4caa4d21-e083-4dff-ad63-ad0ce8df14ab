import { Router, API, Inject } from '@youzan/assets-route-plugin';
import MiniAppBaseController from '../BaseController';
import TiktokUnderwritingService from '../../../services/api/channels/tiktok/TiktokUnderwritingService';
import ShopThirdChannelService from '../../../services/api/channels/omni-channel/ShopThirdChannelService';
import channelMap from '../../../constants/miniAppChannelMap';

@Router('tiktok-shelf/underwriting')
class UnderwritingController extends MiniAppBaseController {
  @Inject()
  private readonly tikTokUnderwritingService!: TiktokUnderwritingService;

  @Inject()
  private readonly shopThirdChannelService!: ShopThirdChannelService;

  public async init() {
    this.channelName = 'tiktok-shelf';
    super.init();
  }

  /**
   * 抖音查询商户号和进件信息
   */
  @API('GET', 'query-underwriting-status.json')
  public async queryUnderwritingStatus() {
    const { kdtId, userId } = this.ctx;
    const result = await this.tikTokUnderwritingService.queryMchStatus({
      kdtId,
      adminId: userId,
    });
    await this.ctx.json(0, 'ok', result);
  }

  /**
   * 抖音查询商户号和进件信息
   */
  @API('GET', 'query-underwriting-channel-status.json')
  public async queryUnderwritingChannelStatus() {
    const { kdtId, userId } = this.ctx;
    const rootKdtId = this.getRootKdtId();
    const channelConfig = channelMap['tiktok-shelf'];
    const result = await this.tikTokUnderwritingService.queryMchStatus({
      kdtId,
      adminId: userId,
      businessType: channelConfig.businessType,
    });
    const channelInfo = await this.shopThirdChannelService.getShopChannelAccount({
      kdtId: rootKdtId,
      channelAccountType: channelConfig.channelAccountType,
      channelId: channelConfig.channelId,
    });
    if (channelInfo && channelInfo[0]) {
      result.channelAccountValue = channelInfo[0]?.channelAccountValue ?? '';
    }
    await this.ctx.json(0, 'ok', result);
  }

  /**
   * 抖音提交商户号
   */
  @API('POST', 'submit-merchant.json')
  public async submitMerchant() {
    const { kdtId, userId } = this.ctx;
    const { mchId = 1, channelAccountValue } = this.ctx.request.body;
    const channelConfig = channelMap['tiktok-shelf'];
    try {
      await this.shopThirdChannelService.updateShopChannelAccount({
        channelAccountValue,
        channelAccountType: channelConfig.channelAccountType,
        channelId: channelConfig.channelId,
        loginKdtId: kdtId,
        kdtId,
      });
      // 设置成功状态
    } catch (error) {
      // status 1 来客授权失败
      return this.ctx.json(0, 'ok', { status: 1, msg: (error as { msg: string }).msg });
    }
    try {
      await this.tikTokUnderwritingService.commitMch({
        mchId,
        kdtId,
        adminId: userId,
        businessType: channelConfig.businessType,
      });
    } catch (error) {
      // status 2 商户号授权失败
      return this.ctx.json(0, 'ok', {
        status: 2,
        msg: (error as { msg: string }).msg,
        channelAccountValue,
      });
    }

    // status 4 商户号授权成功
    await this.ctx.json(0, 'ok', { status: 4, mchId, channelAccountValue });
  }

  /**
   * 抖音修改商户号
   */
  @API('POST', 'update-merchant.json')
  public async updateMerchant() {
    const { kdtId, userId } = this.ctx;
    const { mchId = 1 } = this.ctx.request.body;
    try {
      await this.tikTokUnderwritingService.modifyMch({
        mchId,
        kdtId,
        adminId: userId,
        businessType: 7,
      });
    } catch (error) {
      // status 3 商户号更新成功
      return this.ctx.json(0, 'ok', { status: 3, msg: (error as { msg: string }).msg });
    }
    // status 4 商户号更新成功
    await this.ctx.json(0, 'ok', { status: 4, mchId });
  }
}

export = UnderwritingController;
