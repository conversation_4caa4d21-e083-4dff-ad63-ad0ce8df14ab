import { Router, Index, Metadata, Inject, API } from '@youzan/assets-route-plugin';
import CommonBasicInfoController from '../common/BasicInfoController';
import XhsPoiService from '../../../services/api/ebiz/video/channels/uc/XhsPoiService';
import HQStoreSearchService from '../../../services/api/retail/shop/HQStoreSearchService';
import StorageQiniuReadService from '../../../services/api/material/materialcenter/StorageQiniuReadService';

@Router('xhs/poi-store-claim')
class PoiStoreClaimController extends CommonBasicInfoController {
  @Inject()
  private readonly XhsPoiService!: XhsPoiService;

  @Inject()
  private readonly HQStoreSearchService!: HQStoreSearchService;

  @Inject()
  public readonly StorageQiniuReadService!: StorageQiniuReadService;

  public async init() {
    this.channelName = 'xhs';
    super.init();
  }

  @Index('')
  @Metadata('INDEX')
  async getPoiStoreClaimHtml() {
    const { ctx } = this;
    ctx.setState('subtitle', 'POI门店认领');

    // const res = await this.HongShuMiniAppService.getAccountBasicInfo({
    //   kdtId: ctx.kdtId,
    //   businessType: 1,
    //   accountType: 16,
    // });

    // if (res?.tradeAbility !== 2) {
    //   return ctx.redirect('/v4/channel/xhs/dashboard');
    // }

    return ctx.render('mini-app/xhs/poi-store-claim/index.html');
  }

  @API('GET', 'getClaimablePoi.json')
  async getClaimablePoi() {
    const { ctx } = this;
    const { request } = this.ctx;
    const { province, city, pageNo, district, shopName, pageSize } = request.query;
    const { kdtId } = ctx;

    const result = await this.XhsPoiService.pageClaimablePoi({
      kdtId,
      province,
      city,
      pageNo,
      district,
      shopName,
      pageSize,
    });

    await this.ctx.json(0, 'ok', result);
  }

  @API('GET', 'queryClaimableCategory.json')
  async queryClaimableCategory() {
    const { ctx } = this;
    const { kdtId } = ctx;

    const result = await this.XhsPoiService.queryClaimableCategory({
      kdtId,
    });

    await this.ctx.json(0, 'ok', result);
  }

  @API('POST', 'saveClaimInfo.json')
  async saveClaimInfo() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { request } = this.ctx;
    const { shopList, categoryName, categoryId, categoryPath } = request.body;
    const result = await this.XhsPoiService.saveClaimInfo({
      kdtId,
      shopList,
      categoryName,
      categoryId,
      categoryPath,
    });

    await this.ctx.json(0, 'ok', result);
  }

  @API('GET', 'getDraftClaimInfo.json')
  async listDraftClaimInfo() {
    const { ctx } = this;
    const { kdtId } = ctx;

    const result = await this.XhsPoiService.listDraftClaimInfo({
      kdtId,
    });

    // 处理shopList中的隐私图片URL
    if (result && result.shopList && Array.isArray(result.shopList)) {
      const privateUrlsToRefresh: Array<{ oldPrivateUrl: string }> = [];
      const privateUrlMap = new Map<string, any>();

      // 遍历shopList中的每个店铺
      for (const shop of result.shopList) {
        // 使用Object.entries遍历对象的所有键值对
        this.collectPrivateUrls(shop, privateUrlsToRefresh, privateUrlMap);
      }

      // 如果找到了需要刷新的URL，调用刷新接口
      if (privateUrlsToRefresh.length > 0) {
        const refreshedUrls = await this.StorageQiniuReadService.refreshPrivateUrl({
          oldPrivateUrlDTOS: privateUrlsToRefresh,
        });

        // 将刷新后的URL更新回原对象
        if (refreshedUrls && Array.isArray(refreshedUrls)) {
          for (const refreshed of refreshedUrls) {
            const { oldPrivateUrl, newPrivateUrl } = refreshed;
            if (oldPrivateUrl && newPrivateUrl) {
              const targets = privateUrlMap.get(oldPrivateUrl);
              if (targets) {
                targets.forEach((target: any) => {
                  target.ref[target.key] = newPrivateUrl;
                });
              }
            }
          }
        }
      }
    }

    await this.ctx.json(0, 'ok', result);
  }

  /**
   * 收集对象中包含dn-kdt-private.qbox.me域名的URL
   */
  private collectPrivateUrls(
    obj: any,
    privateUrls: Array<{ oldPrivateUrl: string }>,
    privateUrlMap: Map<string, any[]>,
  ) {
    if (!obj || typeof obj !== 'object') return;

    // 遍历对象的所有属性
    Object.entries(obj).forEach(([key, value]) => {
      if (typeof value === 'string' && value.includes('private')) {
        // 找到包含指定域名的URL字符串
        privateUrls.push({ oldPrivateUrl: value });

        // 记录URL在对象中的引用，用于后续更新
        if (!privateUrlMap.has(value)) {
          privateUrlMap.set(value, []);
        }
        privateUrlMap.get(value)?.push({ ref: obj, key });
      } else if (value && typeof value === 'object') {
        // 如果是对象或数组，递归查找
        this.collectPrivateUrls(value, privateUrls, privateUrlMap);
      }
    });
  }

  @API('POST', 'submitClaimInfo.json')
  async submitClaimInfo() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { request } = this.ctx;
    const { shopList, categoryName, categoryId, categoryPath } = request.body;

    const result = await this.XhsPoiService.submitClaimInfo({
      kdtId,
      shopList,
      categoryName,
      categoryId,
      categoryPath,
    });

    await this.ctx.json(0, 'ok', result);
  }

  @API('GET', 'searchFilterSensitiveInfo.json')
  async searchFilterSensitiveInfo() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { request, userId } = this.ctx;
    const { latitude, longitude } = request.query;

    const result = await this.HQStoreSearchService.searchFilterSensitiveInfo({
      hqKdtId: kdtId,
      kdtId,
      sortGeo: {
        lon: longitude,
        lat: latitude,
      },
      source: 'wsc-pc-channel',
      pageNo: 1,
      pageSize: 1,
      adminId: userId,
      storeStatuses: ['try', 'valid', 'protect'],
      sortType: 1,
    });

    await this.ctx.json(0, 'ok', result);
  }

  @API('GET', 'queryPrincipalMsgByKdtId.json')
  async queryPrincipalMsgByKdtId() {
    const { request } = this.ctx;
    const { kdtId } = request.query;

    const result = await this.UnifiedCertQueryService.queryPrincipalMsgWithChannelWatermark({
      sourceId: `${kdtId}`,
      sourceIdType: 'KDT_ID',
      unifiedCertType: 1,
      watermarkChannelType: 'XHS',
    });

    await this.ctx.json(0, 'ok', result);
  }
}

export = PoiStoreClaimController;
