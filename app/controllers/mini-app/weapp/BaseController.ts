import { Inject } from '@youzan/assets-route-plugin';
import BaseController from '../BaseController';
import WeappAccountService from '../../../services/api/channels/channel/core/WeappAccountService';
import MpVersionService from '../../../services/api/channels/apps/MpVersionService';
import { compareVersion } from '../../../utils/compare-version';

class WechatModuleBaseController extends BaseController {
  @Inject()
  private readonly mpVersionService!: MpVersionService;

  @Inject()
  public readonly weappAccountService!: WeappAccountService;

  // 获取小程序基本信息
  protected async getMiniAppAccountInfo() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { businessType = 1 } = this.ctx.query;
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');

    const params = {
      accountType,
      businessType,
      kdtId,
    };
    const result = await this.channelCoreAccountService.queryMpAccountInfoByKdtId(params);
    return result;
  }

  /**
   * 获取小程序绑定情况
   */
  protected async getMiniAppBindInfo() {
    const { kdtId, request } = this.ctx;
    const { businessType = 1 } = request.body;
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    this.validator.required(accountType, 'accountType 不能为空');
    this.validator.required(businessType, 'businessType 不能为空');
    return this.channelCoreAccountService.queryMpBindInfoByKdtId({
      businessType,
      accountType,
      externalId: kdtId,
    });
  }

  /**
   * 获取小程序版本信息
   */

  protected getVersionInfo() {
    const { ctx } = this;
    const { kdtId } = ctx;
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');

    const params = {
      accountType,
      businessType: 1,
      kdtId,
    };
    return this.mpVersionService.getMpVersion(params);
  }

  /**
   * 判断版本是否可用
   */
  protected versionJudge(curVersion: string): boolean {
    const { ctx } = this;
    const { shopType } = ctx;
    if (!curVersion) {
      return false;
    }
    // 零售店铺 当前小程序版本小于3.24.6
    if (shopType === 7 && compareVersion(curVersion, '3.24.6') === -1) {
      return false;
    }
    // 微商城店铺 当前小程序版本小于2.68.5
    if (compareVersion(curVersion, '2.68.5') === -1) {
      return false;
    }
    return true;
  }
}

export default WechatModuleBaseController;
