import BaseController from '../base/BaseController';
import { Inject } from '@youzan/assets-route-plugin';
import ChannelCoreAccountService from '../../services/api/channels/channel/core/ChannelCoreAccountService';
import UnifiedCertQueryService from '../../services/api/pay/customer/cert/UnifiedCertQueryService';
import HongShuMiniAppService from '../../services/api/channels/channel/core/HongShuMiniAppService';
import lodash from 'lodash';
import channelMap from '../../constants/miniAppChannelMap';
import { TradeAbility } from '../../constants/xhsLocalLife';

class MiniAppBaseController extends BaseController {
  @Inject()
  protected readonly channelCoreAccountService!: ChannelCoreAccountService;

  @Inject()
  public readonly HongShuMiniAppService!: HongShuMiniAppService;

  @Inject()
  public readonly UnifiedCertQueryService!: UnifiedCertQueryService;

  public async init() {
    super.init();
    const { ctx } = this;
    const res = this.ctx.apolloClient.getConfig({
      appId: 'wsc-pc-channel',
      namespace: 'wsc-pc-channel.whitelist',
      key: 'channel_show_nav_link_tabs',
    });
    ctx.setGlobal('showNavLinkTabsCfg', res);
  }

  /**
   * 获取小程序绑定情况
   */
  public async getBindInfo() {
    const { kdtId: externalId, request } = this.ctx;
    let { businessType = 1 } = request.body;
    // @ts-ignore
    const { accountType, businessType: channelBusinessType } = this.ctx.getState('channelMeta');
    // 抖音场景单独设置
    const tiktokBusinessTypeList = [
      channelMap['tiktok-shelf'].businessType,
      channelMap['tiktok-coupon'].businessType,
    ];
    if (tiktokBusinessTypeList.includes(+channelBusinessType)) {
      businessType = channelBusinessType;
    }
    this.validator.required(accountType, 'accountType 不能为空');
    this.validator.required(businessType, 'businessType 不能为空');
    // const rootKdtId = lodash.get(this.ctx.getState('shopInfo'), 'rootKdtId', kdtId);
    return this.channelCoreAccountService.queryMpBindInfoByKdtId({
      businessType,
      accountType,
      externalId,
    });
  }

  // 获取小红书小程序基本信息
  public async getXhsMiniAppAccountInfo() {
    const { ctx } = this;
    const { kdtId, query } = ctx;
    const { businessType = 1 } = query;
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');

    const res = await this.HongShuMiniAppService.getAccountBasicInfo({
      kdtId,
      businessType,
      accountType,
    });

    return res;
  }

  private formatPrincipalName(name?: string) {
    if (!name) {
      return NaN;
    }

    name = name.trim();

    return name.replace(/\(/g, '（').replace(/\)/g, '）');
  }

  private formatPrincipalLicenseNo(code?: string) {
    if (!code) {
      return NaN;
    }

    code = code.trim();

    return code.toLocaleUpperCase();
  }

  // 判断小红书主体是否一致
  public async isSameXhsPrincipalName() {
    const { kdtId } = this.ctx;
    const { principalName: xhsPrincipalName, principalLicenseNo: xhsPrincipalLicenseNo } =
      (await this.getXhsMiniAppAccountInfo()) || {};

    const principalInfo = await this.UnifiedCertQueryService.queryPrincipalMsg({
      sourceId: `${kdtId}`,
      sourceIdType: 'KDT_ID',
      unifiedCertType: 1,
    }).then(res => {
      this.ctx.toLog('查询店铺主体认证信息', res);
      return res;
    });

    const shopPrincipalName =
      lodash.get(principalInfo, 'merchantOrganizationCertResult.organizationName', '') ||
      lodash.get(principalInfo, 'merchantPersonCertResult.personName', '');

    const shopCreditNo = lodash.get(principalInfo, 'merchantOrganizationCertResult.creditNo', '');

    // 有赞主体认证名称和小红书小程序主体认证名称是否一致
    const isSameName =
      this.formatPrincipalName(shopPrincipalName) === this.formatPrincipalName(xhsPrincipalName);

    const isSameNo =
      this.formatPrincipalLicenseNo(xhsPrincipalLicenseNo) ===
      this.formatPrincipalLicenseNo(shopCreditNo);

    this.ctx.logger.info(
      `【${kdtId}】小红书主体是否一致判断 -- 小红书侧主体名称：${xhsPrincipalName} - 有赞侧主体名称: ${shopPrincipalName} - 主体名称是否一致: ${isSameName} -- 小红书侧信用代码：${xhsPrincipalLicenseNo} - 有赞侧信用代码：${shopCreditNo} - 主体信用代码是否一致: ${isSameNo}`,
    );

    return isSameName || isSameNo;
  }

  public async checkXhsLocalLife() {
    let xhsLocalLife = false;
    // @ts-ignore
    const { id } = this.ctx.getState('channelMeta');
    if (id === 'xhs') {
      const xhsAccountInfo = await this.getXhsMiniAppAccountInfo();
      const { tradeAbility, appCategoryDesc, isSupportPoi = true } = xhsAccountInfo || {};
      xhsLocalLife = tradeAbility === TradeAbility.LocalLifeGuaranteePayment;

      this.ctx.setGlobal('isSupportPoi', isSupportPoi);
      this.ctx.setGlobal('tradeAbility', tradeAbility);
      this.ctx.setGlobal('appCategoryDesc', appCategoryDesc);
    }

    this.ctx.setGlobal('xhsLocalLife', xhsLocalLife);

    return xhsLocalLife;
  }
}

export default MiniAppBaseController;
