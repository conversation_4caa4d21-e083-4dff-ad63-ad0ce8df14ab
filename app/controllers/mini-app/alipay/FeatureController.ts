import { Router, API, Index } from '@youzan/assets-route-plugin';
import { get } from 'lodash';
import BaseController from './BaseController';
import PermiseCheckError from '../../../exceptions/PremiseCheckError';

@Router('alipay/feature')
class FeatureController extends BaseController {
  public async init() {
    this.channelName = 'alipay';
    super.init();
  }

  @Index('mobile-auth')
  public async getMobileAuthHtml() {
    const { ctx } = this;
    try {
      await this.checkIsBind();
      const { businessType = 1 } = ctx.request.body;
      // @ts-ignore
      const { accountType } = ctx.getState('channelMeta');
      await this.alipayAccountService.checkLatestKeyAuditStatus({
        accountType,
        businessType,
        kdtId: ctx.kdtId,
        fieldKey: 'alipayMobileFieldKey',
      });
      ctx.setState('subtitle', '手机号授权');
      return ctx.render('mini-app/alipay/feature/mobile-auth.html');
    } catch (e) {
      if (e instanceof PermiseCheckError) {
        ctx.redirect(`/v4/channel/alipay/auth/empty`);
      } else {
        throw e;
      }
    }
  }

  /**
   * 获取支付宝手机号授权申请状态
   */
  @API('GET', 'get-mobile-auth-audit-status.json')
  public async getMobileAuditStatusJson() {
    const { ctx } = this;
    const { businessType = 1 } = this.ctx.query;
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');
    const statusInfo =
      (await this.channelCoreAccountService.queryChannelAuditInfo({
        kdtId: ctx.kdtId,
        accountType,
        businessType,
        auditKey: 'alipayMobileFieldKey',
      })) || {};
    await this.ctx.json(0, 'ok', statusInfo);
  }

  /**
   * 发起支付宝手机号授权申请
   */
  @API('POST', 'audit-mobile-auth.json')
  public async postMobileAuditJson() {
    const { ctx } = this;
    const { businessType = 1 } = ctx.request.body;
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');
    const result = await this.alipayAccountService.applyFieldAuth({
      accountType,
      businessType,
      kdtId: ctx.kdtId,
      applyFieldKey: 'alipayMobileFieldKey',
    });
    await this.ctx.json(0, 'ok', result);
  }

  @Index('ant-store')
  public async geAntStoreHtml() {
    const { ctx } = this;
    const bindInfo = await this.getBindInfo();
    ctx.setGlobal('pageData', {
      bindInfo,
    });
    ctx.setState('subtitle', '蚂蚁门店');
    return ctx.render('mini-app/alipay/feature/ant-store.html');
  }

  /**
   * 前置权限校验
   */
  @API('GET', 'check-permission.json')
  public async checkMiniAppPermission() {
    const { kdtId, userId } = this.ctx;
    const rootKdtId = get(this.ctx.getState('shopInfo'), 'rootKdtId', kdtId);
    const permissionResult = await this.alipayAntShopService.checkPermission({
      rootKdtId,
      adminId: userId,
    });
    await this.ctx.json(0, 'ok', permissionResult);
  }

  /**
   * 查询蚂蚁门店列表
   */
  @API('GET', 'ant-shop-list.json')
  public async getAntShopList() {
    const { kdtId, request, userId } = this.ctx;
    const { shopMode, shopRole, shopStatus, shopName, pageSize = 10, pageNo = 1 } = request.query;
    const rootKdtId = get(this.ctx.getState('shopInfo'), 'rootKdtId', kdtId);
    const antShopList = await this.alipayAntShopService.getAntShop({
      rootKdtId,
      shopMode,
      shopRole,
      shopStatus,
      shopName,
      pageSize,
      pageNo,
      adminId: userId,
    });
    await this.ctx.json(0, 'ok', antShopList);
  }

  /**
   * 启用蚂蚁门店
   */
  @API('GET', 'sync-ant-shop.json')
  public async syncAntStore() {
    const { userId, request } = this.ctx;
    const { kdtId } = request.query;
    const syncSuccess = await this.alipayAntShopService.syncAntShop({ kdtId, adminId: userId });
    await this.ctx.json(0, 'ok', syncSuccess);
  }

  /**
   * 停用蚂蚁门店
   */
  @API('GET', 'close-ant-shop.json')
  public async closeAntStore() {
    const { userId, request } = this.ctx;
    const { kdtId } = request.query;
    const closeSuccess = await this.alipayAntShopService.closeAntShop({ kdtId, adminId: userId });
    await this.ctx.json(0, 'ok', closeSuccess);
  }

  /**
   * 删除蚂蚁门店
   */
  @API('GET', 'delete-ant-shop.json')
  public async deleteAntStore() {
    const { userId, request } = this.ctx;
    const { kdtId } = request.query;
    const deleteSuccess = await this.alipayAntShopService.deleteAntShop({ kdtId, adminId: userId });
    await this.ctx.json(0, 'ok', deleteSuccess);
  }

  /**
   * 查询可选店铺
   */
  @API('GET', 'to-be-added-shop.json')
  public async getToBeAddedShopList() {
    const { kdtId, request, userId } = this.ctx;
    const rootKdtId = get(this.ctx.getState('shopInfo'), 'rootKdtId', kdtId);
    const { businessMode, typeName, shopName, pageSize = 6, pageNo = 1 } = request.query;
    const toBeAddedShopList = await this.alipayAntShopService.getToBeAddedShop({
      rootKdtId,
      businessMode,
      typeName,
      shopName,
      pageSize,
      pageNo,
      adminId: userId,
    });
    await this.ctx.json(0, 'ok', toBeAddedShopList);
  }

  /**
   * 添加店铺
   */
  @API('POST', 'add-ant-shop.json')
  public async addAntStore() {
    const { kdtId, request } = this.ctx;
    const rootKdtId = get(this.ctx.getState('shopInfo'), 'rootKdtId', kdtId);
    const { userId } = this.ctx.getLocalSession('userInfo') || {};
    const { operateType, kdtIds } = request.body;
    const addAntShopSuccess = await this.alipayAntShopService.addAntShop({
      rootKdtId,
      operateType,
      kdtIds,
      adminId: userId,
    });
    await this.ctx.json(0, 'ok', addAntShopSuccess);
  }
}

export = FeatureController;
