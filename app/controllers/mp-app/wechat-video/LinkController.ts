import { Router, Index, API, Inject } from '@youzan/assets-route-plugin';
import BaseController from './BaseController';
import ChannelCoreAccountService from '../../../services/api/channels/channel/core/ChannelCoreAccountService';
import MpVersionService from '../../../services/api/channels/apps/MpVersionService';
import WeappVideoExtLinkFacadeService from '../../../services/api/mall/portal/WeappVideoExtLinkFacadeService';
import VideoExtLinkDataService from '../../../services/api/ebiz/video/channels/data/VideoExtLinkDataService';
import ItemQueryService from '../../../services/api/ic/manage/ItemQueryService';
import {
  checkWscShop,
  checkHqStore,
  checkPartnerStore,
  checkChainStore,
  checkBranchStore,
} from '@youzan/utils-shop';
import GoodsSelectorConfigService, {
  IQueryGoodsSelectorConfigParamType,
} from '../../../services/api/ump/manage/goods/GoodsSelectorConfigService';
import { IWscPcBaseContext, IWscPcBaseDefine } from '@youzan/wsc-pc-base/definitions';
import BusinessException from '@youzan/wsc-pc-base/app/exceptions/BusinessException';
import lodash from 'lodash';

@Router('wechat-video/link')
class LinkController extends BaseController {
  @Inject()
  public readonly channelCoreAccountService!: ChannelCoreAccountService;

  @Inject()
  public readonly mpVersionService!: MpVersionService;

  @Inject()
  public readonly weappVideoExtLinkFacadeService!: WeappVideoExtLinkFacadeService;

  @Inject()
  public readonly VideoExtLinkDataService!: VideoExtLinkDataService;

  @Inject()
  public readonly goodsSelectorConfigService!: GoodsSelectorConfigService;

  @Inject()
  public readonly ItemQueryService!: ItemQueryService;

  public async init() {
    this.channelName = 'wechat-video';
    super.init();
  }

  // 获取商品选择组件配置（商品选择组件由后端配置，只要给活动类型等信息即可）
  async _getGoodsSelectorConfig(
    ctx: IWscPcBaseContext<IWscPcBaseDefine>,
    { umpType, goodsChannel, industry }: IQueryGoodsSelectorConfigParamType,
  ) {
    const payload: IQueryGoodsSelectorConfigParamType = {
      shopId: +ctx.kdtId,
      domain: 'UMP',
      umpType: +umpType,
    };
    if (goodsChannel != null) payload.goodsChannel = goodsChannel;

    // 获得行业信息
    const getIndustry = () => {
      const shopInfo = ctx.getState('shopInfo');
      let industry = -1;
      const isWsc = checkWscShop(shopInfo);
      const isMeiye = shopInfo.shopType === 6;
      const { isSuperStore } = ctx;
      if (isWsc) industry = 0;
      else if (isMeiye) industry = 6;
      else if (isSuperStore) industry = 7;
      return industry;
    };

    payload.industry = industry || getIndustry();

    try {
      const config = await this.goodsSelectorConfigService.queryGoodsSelectorConfig(payload);
      return config;
    } catch (error) {
      console.log(error);
      throw new BusinessException(400, '商品选择配置初始化失败，请稍后重试', payload);
    }
  }

  async getCreateLinkType() {
    const { ctx } = this;
    const shopMetaInfo = ctx.getState('shopMetaInfo');
    const shopInfo = ctx.getState('shopInfo');
    const { rootKdtId } = shopInfo;
    const baseParams = {
      // 微商城
      businessType: 1,
      // 微信公众号
      accountType: 1,
      kdtId: ctx.kdtId,
    };
    // 连锁 分店 使用总店去查询公众号 小程序信息
    if (checkChainStore(shopMetaInfo) && checkBranchStore(shopMetaInfo)) {
      baseParams.kdtId = rootKdtId as number;
    }
    // 获取微信公众号绑定信息
    const mpBindInfo = await this.channelCoreAccountService.queryMpAccountInfoByKdtId({
      ...baseParams,
    });
    // 未绑定微信公众号
    if (!mpBindInfo) {
      return {
        unSupportType: 3,
        supportCreateLink: false,
      };
    }
    // 非认证服务号
    if (mpBindInfo.serviceType !== 2 || mpBindInfo.verifyType !== 0) {
      return {
        unSupportType: 1,
        supportCreateLink: false,
      };
    }
    const authInfo = await this.channelCoreAccountService.queryChannelAuthInfo({
      ...baseParams,
    });
    const authItem = authInfo && (authInfo.authInfoDTOS || []).find((it: any) => it.funcId === 1);
    // 消息管理权限暂未授权
    if (!authItem || authItem.funcState !== 1) {
      return {
        unSupportType: 2,
        supportCreateLink: false,
      };
    }
    // 获取微信小程序绑定信息
    const miniBindInfo = await this.channelCoreAccountService.queryMpAccountInfoByKdtId({
      ...baseParams,
      // 微信小程序
      accountType: 2,
    });
    // 未授权绑定微信小程序
    if (!miniBindInfo) {
      return {
        unSupportType: 4,
        supportCreateLink: false,
      };
    }
    // 获取微信小程序版本信息
    const miniVersionInfo = await this.mpVersionService.getMpVersion({
      ...baseParams,
      // 微信小程序
      accountType: 2,
    });
    // 没有已发布的小程序版本信息
    if (!miniVersionInfo.releaseVersion) {
      return {
        unSupportType: 5,
        supportCreateLink: false,
      };
    }
    return {
      unSupportType: 0,
      supportCreateLink: true,
    };
  }

  /**
   * @description 获取网店列表数据
   * @param {*} ctx
   */
  async getSubShopList() {
    const { ctx } = this;
    // 连锁总店店铺或合伙人店铺，获取下属全部网店的列表
    const shopMetaInfo = ctx.getState('shopMetaInfo');
    if (
      checkChainStore(shopMetaInfo) &&
      (checkHqStore(shopMetaInfo) || checkPartnerStore(shopMetaInfo))
    ) {
      const operatorParam = this.getOperatorParams();
      const subShopParam = Object.assign(operatorParam, { hqKdtId: ctx.kdtId });
      return this.ItemQueryService.getTotalCountSubShop(
        lodash.omit(subShopParam, 'kdtId'),
      ).catch(e => ctx.toLog('视频号数据——获取全部网店列表失败', e));
    }
    return new Promise<void>(resolve => resolve());
  }

  getKdtIdCompatibleChainSearchKdt(searchKdtId: number) {
    const { ctx } = this;
    // 连锁总店店铺或合伙人店铺，获取下属全部网店的列表
    const shopMetaInfo = ctx.getState('shopMetaInfo');
    if (
      checkChainStore(shopMetaInfo) &&
      (checkHqStore(shopMetaInfo) || checkPartnerStore(shopMetaInfo))
    ) {
      this.validator.required(searchKdtId, 'searchKdtId 不能为空');
      return searchKdtId;
    }
    return ctx.kdtId;
  }

  // 是否在迁移白名单内
  isInShiftWhiteList() {
    const { ctx } = this;

    const isInWhiteListShift = this.isInWhiteListByApollo('extend-link', {
      appId: 'wsc-pc-wxvideo',
      namespace: 'wsc-pc-wxvideo.migrate',
      useRootKdtId: true,
    });

    if (isInWhiteListShift) {
      const path = ctx.path.replace('/v4/channel/mp/wechat-video/link', '/v4/wxvideo/extend-link');
      this.ctx.redirect(`//www.youzan.com${path}`);
    }

    return isInWhiteListShift;
  }

  @Index(['', 'list', 'create'])
  async getIndexHtml() {
    // 增加迁移灰度逻辑
    if (this.isInShiftWhiteList()) {
      return;
    }

    const { ctx } = this;

    const { supportCreateLink, unSupportType } = await this.getCreateLinkType();

    const gsConfigType = 30005;
    const gsConfig = await this._getGoodsSelectorConfig(ctx, {
      // VideoExtLink 配置
      umpType: gsConfigType,
    });
    const subShopList = await this.getSubShopList();

    ctx.setGlobal('_gsConfig', gsConfig);
    ctx.setGlobal('_gsConfigType', gsConfigType);

    ctx.setGlobal('pageData', {
      supportCreateLink,
      unSupportType,
      subShopList, // 所有分店信息
    });

    await ctx.render('mp-app/wechat-video/link.html');
  }

  /**
   * 获取链接列表
   */
  @API('GET', 'list.json')
  public async getLinkList() {
    const {
      search: goodsTitle = '',
      pageSize = 20,
      pageNo: pageNumber = 1,
      selectKdtId,
    } = this.ctx.query;
    this.validator.required(this.ctx.kdtId, 'kdtId 不能为空');
    const kdtId = this.getKdtIdCompatibleChainSearchKdt(selectKdtId);
    const result =
      (await this.weappVideoExtLinkFacadeService.getLinkList({
        kdtId,
        goodsTitle,
        pageSize,
        pageNumber,
      })) || {};
    return this.ctx.json(0, 'success', {
      items: result.content || [],
      total: result.total || 0,
    });
  }

  /**
   * 删除链接
   */
  @API('DELETE', 'delete.json')
  public async deleteLink() {
    const {
      request: {
        body: { linkId, selectKdtId },
      },
    } = this.ctx;
    this.validator.required(this.ctx.kdtId, 'kdtId 不能为空');
    const kdtId = this.getKdtIdCompatibleChainSearchKdt(selectKdtId);
    const result = await this.weappVideoExtLinkFacadeService.deleteLink({
      kdtId,
      linkId,
    });
    return this.ctx.json(0, 'success', result);
  }

  /**
   * 增加链接
   */
  @API('POST', 'create.json')
  public async createLink() {
    const { ctx } = this;
    const postData = lodash.pick(ctx.request.body, [
      'authorName',
      'goodsTitle',
      'goodsImage',
      'goodsId',
      'linkDesc',
      'linkType',
      'xcxHome',
      'xcxPath',
    ]);
    this.validator.required(ctx.kdtId, 'kdtId 不能为空');
    const kdtId = this.getKdtIdCompatibleChainSearchKdt(ctx.request.body.selectKdtId);
    const result = await this.weappVideoExtLinkFacadeService.createLink({
      ...postData,
      kdtId,
    });
    return this.ctx.json(0, 'success', result);
  }

  /**
   * 查询链接生成情况
   */
  @API('GET', 'status.json')
  public async getLinkStatus() {
    const { ctx } = this;
    const { msgId, selectKdtId } = ctx.query;
    this.validator.required(msgId, '查询链接 不能为空');
    this.validator.required(ctx.kdtId, 'kdtId 不能为空');
    const kdtId = this.getKdtIdCompatibleChainSearchKdt(selectKdtId);
    const result = await this.weappVideoExtLinkFacadeService.getLinkStatus({
      msgId,
      kdtId,
    });
    return this.ctx.json(0, 'success', result);
  }

  /**
   * 扩展链接数据
   */
  @API('GET', 'statistic.json')
  public async getExtLinkStatisticData() {
    const { ctx } = this;
    const { dcPs, selectKdtId } = ctx.query;
    this.validator.required(dcPs, 'dcPs 不能为空');
    this.validator.required(ctx.kdtId, 'kdtId 不能为空');
    const kdtId = this.getKdtIdCompatibleChainSearchKdt(selectKdtId);
    const result = await this.VideoExtLinkDataService.getExtLinkStatisticData(kdtId, dcPs);
    return this.ctx.json(0, 'success', result);
  }
}

export = LinkController;
