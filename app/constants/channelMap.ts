import miniAppChannelMap from './miniAppChannelMap';
import mpAppChannelMap from './mpAppChannelMap';
import videoAppChannelMap from './videoAppChannelMap';
import omniChannelMap from './omniChannelMap';
import tencentChannelMap from './tencentChannelMap';
import voucherChannelMap from './voucherChannelMap';
import externalChannelMap from './externalChannel';

export const channelTypeEnum = {
  mpApp: 'mp',
  miniApp: 'mini',
  videoApp: 'video',
  omni: 'omni',
  tencent: 'tencent',
  voucher: 'voucher',
  external: 'external',
};
export default {
  [channelTypeEnum.mpApp]: mpAppChannelMap,
  [channelTypeEnum.miniApp]: miniAppChannelMap,
  [channelTypeEnum.videoApp]: videoAppChannelMap,
  [channelTypeEnum.omni]: omniChannelMap,
  [channelTypeEnum.tencent]: tencentChannelMap,
  [channelTypeEnum.voucher]: voucherChannelMap,
  [channelTypeEnum.external]: externalChannelMap,
};
