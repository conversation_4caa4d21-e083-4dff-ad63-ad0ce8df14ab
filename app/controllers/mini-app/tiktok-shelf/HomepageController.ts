import { Router, Index } from '@youzan/assets-route-plugin';
import CommonBasicInfoController from '../common/BasicInfoController';

@Router('tiktok-shelf/homepage')
class HomepageController extends CommonBasicInfoController {
  public async init() {
    this.channelName = 'tiktok-shelf';
    super.init();
  }

  @Index('')
  public async getHomepageHtml() {
    const isBind = await this.getAccountInfo();
    this.ctx.redirect(
      isBind
        ? `/v4/deco/homepage#/${this.channelName}`
        : `/v4/channel/${this.channelName}/auth/empty`,
    );
  }
}

export = HomepageController;
