import { Router, Index, Metadata, API } from '@youzan/assets-route-plugin';
import BaseController from './BaseController';

@Router('wechat-video')
class EntryController extends BaseController {
  public async init() {
    this.channelName = 'wechat-video';
    super.init();
  }

  @Index(['entry'])
  @Metadata('INDEX')
  async getEntryHtml() {
    const { ctx } = this;

    // 老页面直接跳转新页面
    ctx.redirect('/v4/channel/video/wechat-video/dashboard');
  }

  /**
   * 获取apollo公告配置
   */
  @API('GET', 'apollo-announcement.json')
  async getApolloAnnouncementJson() {
    const result = await this.getApolloAnnouncement();
    await this.ctx.json(0, 'ok', result);
  }
}

export = EntryController;
