import { Inject } from '@youzan/assets-route-plugin';
import _ from 'lodash';
import { checkLiteOnlineStoreManager, checkStoreOnlineStoreManager } from '@youzan/utils-shop';

import BaseController from '../base/BaseController';
import StaffServiceV2 from '../../services/api/sam/StaffServiceV2';
import RoleService from '../../services/api/rig/RoleService';

class VoucherBaseController extends BaseController {
  @Inject()
  public readonly staffServiceV2!: StaffServiceV2;

  @Inject()
  public readonly roleService!: RoleService;

  public async checkLiteOnlineStoreManager() {
    const { ctx } = this;

    const [staffInfo, roleList] = await Promise.all([
      this.staffServiceV2.getStaff({
        kdtId: ctx.kdtId,
        adminId: ctx.userId,
        biz: 'retail',
      }),
      this.roleService.getRoleList({
        thirdTenantId: `${ctx.kdtId}`,
        thirdUserId: `${ctx.adminId}`,
        namespace: 'np_yz_shop',
        rigSource: 'retail_node',
        filter: {
          bizKeyGroup: 'shop_ability',
        },
      }),
    ]);

    const roleId2extProps: Record<string, any> = _.reduce(
      roleList,
      (acc: Record<string, any>, cur) => {
        acc[cur.roleId] = cur.extProperties || null;
        return acc;
      },
      {},
    );

    const roles = _.map(staffInfo?.roleList, role => ({
      ...role,
      extProperties: roleId2extProps[role.roleId],
    }));

    return checkLiteOnlineStoreManager(roles) || checkStoreOnlineStoreManager(roles);
  }
}

export default VoucherBaseController;
