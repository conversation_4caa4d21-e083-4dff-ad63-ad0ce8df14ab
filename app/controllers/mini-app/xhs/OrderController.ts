import { Router, Inject, Index, API } from '@youzan/assets-route-plugin';

import CommonBasicInfoController from '../common/BasicInfoController';
import XiaohongshuChannelConsoleService from '../../../services/api/ebiz/video/channels/item/xhs/XiaohongshuChannelConsoleService';
import { ShopAbilityUtil } from '@youzan/plugin-shop-ability';

@Router('xhs/order')
class OrderController extends CommonBasicInfoController {
  @Inject()
  private readonly XiaohongshuChannelConsoleService!: XiaohongshuChannelConsoleService;

  public async init() {
    this.channelName = 'xhs';
    super.init();
  }

  @Index(['', 'xhs-independent'])
  public async getOrderHtml() {
    const { ctx } = this;

    // 小红书独立版本判断
    const hasXhsIndependentAbility = await new ShopAbilityUtil(ctx).checkAbilityValid({
      keys: ['xhs_independent_ability'],
      kdtId: ctx.kdtId,
    });
    if (hasXhsIndependentAbility && ctx.url.indexOf('xhs-independent') < 0) {
      this.ctx.redirect('/v4/channel/xhs/order/xhs-independent');
      return;
    }

    ctx.setState('subtitle', '推广订单');
    return ctx.render('mini-app/xhs/order/index.html');
  }

  // 获取推广订单
  @API('GET', 'get-order-commission.json')
  public async getXhsOrderList() {
    const { ctx } = this;
    const { kdtId, request } = ctx;
    const { query = {} } = request;
    const res = await this.XiaohongshuChannelConsoleService.getXhsOrderList({
      ...query,
      kdtId,
    });
    return ctx.json(0, 'ok', res);
  }
}

export = OrderController;
