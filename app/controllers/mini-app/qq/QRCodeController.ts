import { Router, API, Inject } from '@youzan/assets-route-plugin';
import BaseController from './BaseController';
import WeappQRCodeService from '../../../services/api/channels/apps/WeappQRCodeService';
import channelMap from '../../../constants/miniAppChannelMap';

@Router('qq')
class QRCodeController extends BaseController {
  @Inject()
  private readonly weappQRCodeService!: WeappQRCodeService;

  private async requestQqQrCode() {
    const { kdtId, request } = this.ctx;
    const { businessType = 1, page } = request.query;
    this.validator.required(page, 'page 不能为空');
    const result = await this.weappQRCodeService.getQqQrCode({
      businessType,
      accountType: channelMap.qq.accountType,
      kdtId,
      page,
    });
    return {
      qrCodeUrl: result ? `data:image/png;base64,${result.base64Image}` : '',
    };
  }

  /**
   * 获取小程序二维码 - 对外提供
   */
  @API('GET', 'qr-code.json')
  public async getQqQrCode() {
    try {
      await this.checkIsBind();
    } catch (e) {
      if (e && e.errorContent) {
        return this.ctx.json(e.errorContent.code, e.errorContent.msg);
      }
      throw e;
    }

    const result = await this.requestQqQrCode();
    return this.ctx.json(0, 'ok', result);
  }

  /**
   * 获取小程序二维码 - 对内使用
   */
  @API('GET', 'qr-code-internally.json')
  public async getAlipayQrCodeInternally() {
    const result = await this.requestQqQrCode();
    await this.ctx.json(0, 'ok', result);
  }
}

export = QRCodeController;
