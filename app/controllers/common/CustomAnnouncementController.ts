import { Router, API } from '@youzan/assets-route-plugin';
import BaseController from '../base/BaseController';

@Router('')
class CustomAnnouncementController extends BaseController {
  public async init() {
    super.init();
  }

  /**
   * 获取自定义apollo announcement
   */
  @API('GET', 'apollo-custom-announcement.json')
  async getCustomApolloAnnouncement() {
    const { request } = this.ctx;
    const { name } = request.query;
    const result = await this.ctx.apolloClient.getConfig({
      appId: 'wsc-pc-channel',
      namespace: `custom-announcement`,
      key: name,
    });
    return this.ctx.json(0, 'ok', result || {});
  }
}

export = CustomAnnouncementController;
