import { Router, Index, API, Metadata, Inject } from '@youzan/assets-route-plugin';
import lodash from 'lodash';
import { checkRetailSingleStore } from '@youzan/utils-shop';
import { PureObject } from '@youzan/wsc-pc-base/definitions/core';
import BaseController from './BaseController';
import ShopConfigReadService from '../../../services/api/shopcenter/shopconfig/ShopConfigReadService';
import BBSPostService from '../../../services/api/ebiz/mall/gemini/BBSPostService';
import FastRegisterTransactionWeappService from '../../../services/api/channels/channel/core/FastRegisterTransactionWeappService';
import VideoComponentManagerService from '../../../services/api/ebiz/video/channels/uc/VideoComponentManagerService';
import WeappTradeModuleService, {
  EnumSceneGroupId,
} from '../../../services/api/channels/apps/WeappTradeModuleService';
import WeappRegisterProxyService from '../../../services/api/channels/apps/WeappRegisterProxyService';
import CustomerUnifiedCertQueryService from '../../../services/api/pay/customer/cert/UnifiedCertQueryService';
import ShopAddressOuterService from '../../../services/api/shopcenter/outer/address/ShopAddressOuterService';
import { wxVideoGrayAuthSwitch, isInApolloGray } from '../../../utils/grayUtils';
import AbilityReadService from '../../../services/api/shopcenter/shopprod/api/AbilityReadService';

@Router('wechat-video')
class DashboardController extends BaseController {
  @Inject()
  public readonly shopConfigReadService!: ShopConfigReadService;

  @Inject()
  private readonly bbsPostService!: BBSPostService;

  @Inject()
  private readonly FastRegisterTransactionWeappService!: FastRegisterTransactionWeappService;

  @Inject()
  protected readonly weappTradeModuleService!: WeappTradeModuleService;

  @Inject()
  public readonly weappRegisterProxyService!: WeappRegisterProxyService;

  @Inject()
  public readonly customerUnifiedCertQueryService!: CustomerUnifiedCertQueryService;

  @Inject()
  public readonly shopAddressOuterService!: ShopAddressOuterService;

  @Inject()
  public readonly VideoComponentManagerService!: VideoComponentManagerService;

  @Inject()
  private readonly abilityReadService!: AbilityReadService;

  public async init() {
    this.channelName = 'wechat-video';
    super.init();
  }

  @Index('dashboard')
  @Metadata('INDEX')
  async getDashboardHtml() {
    const { ctx } = this;
    const { kdtId } = ctx;
    if (!wxVideoGrayAuthSwitch(ctx)) {
      return;
    }

    const shopInfo = ctx.getState('shopInfo');
    const isRetailSingleStore = checkRetailSingleStore(shopInfo);
    const [
      isInNewWxvideoOpenFlowWhite,
      weixinVideoInfo,
      miniAppBindInfo,
      tradeModuleAccessInfo,
      tradeModuleV3AccessInfo,
      isPersonWeapp,
      wechatVideoAbility,
    ] = await Promise.all([
      this.isInNewWxvideoOpenFlowWhite(),
      this.getWeixinVideoInfo(),
      this.getMiniAppBindInfo(),
      this.getTradeModuleInfo(),
      this.getTradeModuleInfo(EnumSceneGroupId.WXVIDEO_OR_WXPUBLIC),
      this.getIsPersonWeapp(),
      ...(isRetailSingleStore
        ? [this.abilityReadService.queryShopAbilityInfo(kdtId, 'wechat_video_ability')]
        : []),
    ]);

    // 交易组件是否开通且商品同步状态开启
    const wechatModuleIsOk = tradeModuleV3AccessInfo.isOpen;

    const { isEnable, applyWxVideoScene } = tradeModuleV3AccessInfo;
    // 场景申请是否通过
    const isApplySceneOk = applyWxVideoScene.status === 1;
    // 是否启用成功
    const isEnableWechatModule = isEnable;
    // 是否打开推广员页面
    const isToSetExtensionOfficer = weixinVideoInfo?.extensionOfficerOpenStatus;

    const pageData: PureObject = {
      isInNewWxvideoOpenFlowWhite,
      weixinVideoInfo: weixinVideoInfo
        ? {
            ...(weixinVideoInfo || {}),
            videoId: weixinVideoInfo?.videoChannelId,
          }
        : undefined,
      tradeModuleAccessInfo,
      tradeModuleV3AccessInfo,
      wechatModuleIsOk,
      isEnableWechatModule,
      applyWxVideoScene,
      isApplySceneOk,
      isBindWxWeapp: !lodash.isEmpty(miniAppBindInfo),
      isToSetExtensionOfficer,
      isPersonWeapp,
      enable: true,
    };

    if (isRetailSingleStore) {
      pageData.wechatVideoAbility = wechatVideoAbility;
      pageData.enable = [1].includes(wechatVideoAbility?.abilityStatus);
    }

    ctx.setGlobal('pageData', pageData);

    if (
      isPersonWeapp &&
      weixinVideoInfo &&
      wechatModuleIsOk &&
      isEnableWechatModule &&
      isApplySceneOk
    ) {
      // 个人小程序
      return ctx.render('video-app/wechat-video/dashboard.html');
    }

    if (
      weixinVideoInfo &&
      wechatModuleIsOk &&
      isEnableWechatModule &&
      isApplySceneOk &&
      isToSetExtensionOfficer
    ) {
      return ctx.render('video-app/wechat-video/dashboard.html');
    }

    return ctx.render('video-app/wechat-video/settings.html');
  }

  /**
   * 商户号-获取商户号申请状态
   */
  @API('GET', 'query-pay-apply-status.json')
  public async queryPayApplyStatus() {
    const { kdtId } = this.ctx;
    const res = await this.VideoComponentManagerService.queryPayApplyStatus(kdtId);

    return this.ctx.json(0, 'ok', res);
  }

  /**
   * 场景申请
   */
  @API('POST', 'apply-scene.json')
  public async applyScene() {
    const { ctx } = this;
    const { kdtId, userId } = ctx;
    const { scene } = ctx.request.body;
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');

    this.validator.required(scene, 'scene 不能为空');

    const result = await this.weappAccountService.applyScene({
      kdtId,
      accountType,
      businessType: 1,
      userId,
      scene,
    });
    return this.ctx.json(0, 'ok', result);
  }

  /**
   * 更新场景申请审核状态
   */
  @API('POST', 'sync-scene-group-status.json')
  public async syncSceneGroupStatus() {
    const { ctx } = this;
    const { kdtId, userId } = ctx;
    const { scene } = ctx.request.body;
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');

    this.validator.required(scene, 'scene 不能为空');

    const result = await this.weappAccountService.syncSceneGroupStatus({
      kdtId,
      accountType,
      businessType: 1,
      userId,
      scene,
    });
    return this.ctx.json(0, 'ok', result);
  }

  /**
   * 获取小程序动态帖子(五篇)
   */
  @API('GET', 'bbs_post.json')
  public async getWeappBBSPostJson() {
    const result = await this.getWeappBBS();
    return this.ctx.json(0, 'ok', result || []);
  }

  /**
   * 获取优秀案例
   */
  @API('GET', 'excellent_example.json')
  public async getExcellentExampleJson() {
    const result = await this.getApolloExcellentExample();
    return this.ctx.json(0, 'ok', result || []);
  }

  /**
   * 获取广告banner
   */
  @API('GET', 'apollo_publicity_banner.json')
  public async getAdBannerJson() {
    const result = await this.getApolloAdBanner();
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 获取apollo公告配置
   */
  @API('GET', 'apollo-announcement.json')
  public async getApolloAnnouncementJson() {
    const result = await this.getApolloAnnouncement();
    await this.ctx.json(0, 'ok', result);
  }

  /**
   * 获取小程序基本信息检查结果
   */
  @API('GET', 'mini-app-check-result.json')
  public async getMiniAppCheckResultJSON() {
    const [
      miniAppAccountCheckInfo,
      weappVersionInfo,
      weappTradeModuleIsAuth,
      shopAddressList,
    ] = await this.getMiniAppInfoCheckResult();

    const isMiniAppNecessaryCheckOk = this.getMiniAppInfoNecessaryCheckIsOkWithAllResult({
      miniAppAccountCheckInfo,
      weappVersionInfo,
      weappTradeModuleIsAuth,
      shopAddressList,
    });

    const isMiniAppWarningCheckOk = this.getMiniAppInfoWarningCheckIsOkWithAllResult({
      miniAppAccountCheckInfo,
    });

    return this.ctx.json(0, 'success', {
      isMiniAppCheckOk: isMiniAppNecessaryCheckOk && isMiniAppWarningCheckOk,
      isMiniAppNecessaryCheckOk,
      isMiniAppWarningCheckOk,
      miniAppAccountCheckInfo,
      weappVersionInfo,
      weappTradeModuleIsAuth,
      shopAddressList,
    });
  }

  // 是否是个人小程序
  async getIsPersonWeapp() {
    let res = false;
    const { kdtId } = this.ctx;
    try {
      res = await this.FastRegisterTransactionWeappService.isFastRegisterTransactionWeapp({
        kdtId,
        businessType: 1,
      });
    } catch (error) {
      this.ctx.logger.error('获取是否为个人小程序失败', error, { kdtId });
    }

    return res;
  }

  /**
   * 获取小程序动态帖子(五篇)
   */
  public getWeappBBS() {
    const { ctx } = this;
    const { adGroupId } = ctx.getState('channelMeta' as any);
    return this.bbsPostService.listMiniPost({
      materialGroupId: adGroupId,
    });
  }

  getMiniAppInfoNecessaryCheckIsOkWithAllResult(miniAppInfoCheckResult: any) {
    const {
      miniAppAccountCheckInfo,
      weappVersionInfo,
      weappTradeModuleIsAuth,
      shopAddressList,
    } = miniAppInfoCheckResult;
    // 小程序版本不达标
    if (!weappVersionInfo?.isUsableVersion) {
      return false;
    }
    // 小程序主体和店铺主体不一致
    if (!miniAppAccountCheckInfo?.isConsistentPrincipalNameWeappAndShop) {
      return false;
    }
    // 自定义交易组件未授权
    if (!weappTradeModuleIsAuth) {
      return false;
    }
    // 退货地址为空
    if (!shopAddressList || !shopAddressList?.data || shopAddressList.total <= 0) {
      return false;
    }
    return true;
  }

  getMiniAppInfoWarningCheckIsOkWithAllResult(miniAppInfoCheckResult: any) {
    const { miniAppAccountCheckInfo } = miniAppInfoCheckResult;
    // 当前小程序服务类目是否在交易组件允许类目范围内
    if (!miniAppAccountCheckInfo?.isInSupportWeappCategories) {
      return false;
    }
    return true;
  }

  getMiniAppInfoCheckResult() {
    const { kdtId } = this.ctx;
    const getMiniAppAccountInfoPromise = this.getMiniAppAccountInfo().then(
      async miniAppAccountInfo => {
        // 小程序是否为非个人认证
        const isMiniAppOrganizationCert =
          miniAppAccountInfo &&
          miniAppAccountInfo.verifyType > -1 &&
          miniAppAccountInfo.principalName &&
          miniAppAccountInfo.principalName !== '个人';

        const principalInfo = await this.customerUnifiedCertQueryService
          .queryPrincipalMsg({
            sourceId: `${kdtId}`,
            sourceIdType: 'KDT_ID',
            unifiedCertType: 1,
          })
          .then(res => {
            this.ctx.toLog('查询店铺主体认证信息', !!res);
            return res;
          });

        const shopPrincipalName =
          lodash.get(principalInfo, 'merchantOrganizationCertResult.organizationName', '') ||
          lodash.get(principalInfo, 'merchantPersonCertResult.personName', '');
        const weappPrincipalName = lodash.get(miniAppAccountInfo, 'principalName', '');

        // 有赞主体认证名称和小程序主体认证名称是否一致
        const isConsistentPrincipalNameWeappAndShop =
          shopPrincipalName && weappPrincipalName && shopPrincipalName === weappPrincipalName;

        // 是否为非代注册小程序
        const isNotCreateFromProxy = miniAppAccountInfo && miniAppAccountInfo.createdFrom !== 2;

        // 类目信息
        let categories = [];
        if (miniAppAccountInfo && [2, 4, 5, 6].indexOf(miniAppAccountInfo?.createdFrom) !== -1) {
          const categoriesData = await this.weappRegisterProxyService.getCategory({
            kdtId: this.getRootKdtId(),
          });
          categories = lodash.get(categoriesData, 'categories', []);
        } else {
          categories = lodash.get(miniAppAccountInfo, 'wxCategoryInfos', []);
        }

        const isInSupportWeappCategories = await this.isInSupportWeappCategories(categories);

        return {
          isMiniAppOrganizationCert,
          isNotCreateFromProxy,
          miniAppAccountInfo,
          isInSupportWeappCategories,
          isConsistentPrincipalNameWeappAndShop,
          weappPrincipalName,
          shopPrincipalName,
        };
      },
    );
    const checkWeappVersionPromise = this.weappTradeModuleService
      .checkWeappVersion({
        kdtId,
        sceneGroupId: EnumSceneGroupId.WXVIDEO_OR_WXPUBLIC,
      })
      .then(res => {
        return {
          curVersion: res.releasedVersion,
          isUsableVersion: res.checkResult === 1,
        };
      })
      .catch(() => {
        return {
          curVersion: '',
          isUsableVersion: false,
        };
      });
    const getShopAddressListPromise = this.shopAddressOuterService
      .queryShopAddressList({
        kdtId,
        addressTypeValues: [1],
        pageSize: 10,
        pageNum: 1,
      })
      .then(res => {
        this.ctx.toLog('查询店铺退货地址信息', res);
        return res;
      });
    return Promise.all([
      getMiniAppAccountInfoPromise,
      checkWeappVersionPromise,
      this.getWeappTradeModuleIsAuth(),
      getShopAddressListPromise,
    ]);
  }

  getWeappTradeModuleIsAuth() {
    const { ctx } = this;
    const { kdtId } = ctx;
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    return this.channelCoreAccountService
      .queryChannelAuthInfo({
        accountType,
        businessType: 1,
        kdtId,
      })
      .then(res => {
        if (res && res.authInfoDTOS) {
          const weappTradeAuth =
            res.authInfoDTOS.find((item: { funcId: number }) => item.funcId === 85) || {};
          return weappTradeAuth.funcState === 1;
        }
        return false;
      });
  }

  /**
   * 是否在新视频号开通流程白名单范围
   * return boolean
   */
  async isInNewWxvideoOpenFlowWhite() {
    const { ctx } = this;
    const isInNewWxvideoOpenFlow = isInApolloGray(ctx, {
      appId: 'wsc-pc-wxvideo',
      namespace: 'wsc-pc-wxvideo.whitelist',
      key: 'trade-module-v3-wxvideo-open-flow',
    });
    return isInNewWxvideoOpenFlow;
  }

  /**
   * 当前小程序服务类目是否在交易组件允许类目范围内
   * return boolean
   */
  async isInSupportWeappCategories(categories: any[]) {
    const { apolloClient } = this.ctx;
    const supportWeappCategories = await apolloClient.getConfig({
      appId: 'wsc-pc-channel',
      namespace: `wsc-pc-channel.wxvideo`,
      key: `support-weapp-categories`,
    });
    const isExistSupportWeappCategories =
      lodash.intersectionBy(supportWeappCategories || [], categories, (category: any) => {
        return `${category.firstId || ''}_${category.secondId || ''}_${category.thirdId || ''}`;
      }).length > 0;
    return isExistSupportWeappCategories;
  }
}

export = DashboardController;
