import { BlankLink, LinkButton } from '@youzan/react-components';
import { setUrlDomain } from '@youzan/retail-utils';
import { isSingleStore } from '@youzan/utils-shop';
import React, { useState } from 'react';
import {
  Checkbox,
  Form,
  FormCheckboxGroupField,
  FormContext,
  FormControl,
  FormDescription,
  FormError,
  FormNumberInputField,
  FormRadioGroupField,
  FormSelectField,
  Icon,
  Notify,
  Pop,
  Radio,
  Upload,
  Validators,
} from 'zent';
import {
  AllowOpenInvoiceStatus,
  BatchActionType,
  EStoreOperatingStatus,
  EsynchronizationOfBusinessHours,
  GoodsUpdateRuleItem,
  StoreHandleConfig,
  StoreHandleItems,
  YouzanGoodsUpdateRuleItem,
} from '../../config';

import { KdtId } from 'pages/mini-app/tiktok-shelf/goods/constant';
import ajax from 'zan-pc-ajax';
import { CHANNEL_ID, PlanStockOptions } from '../../constant';
import { exportOrgInfo, getUploadToken } from './api';
import './style.scss';
import { IHandleDialogProps } from './types';

export const StoreOperatingStatus = (props) => (
  <FormRadioGroupField
    name={StoreHandleConfig.storeOperatingStatus}
    label={`${StoreHandleItems[StoreHandleConfig.storeOperatingStatus]}：`}
    helpDesc={<p className="tip">设置外部渠道的店铺经营状态，保存后系统会自动更改营业状态。</p>}
    onChange={(value) => { 
      const { onChange } = props;
      onChange && onChange(value);
     }}
  >
    <Radio value={EStoreOperatingStatus.on}>营业</Radio>
    <Radio value={EStoreOperatingStatus.off}>休息</Radio>
  </FormRadioGroupField>
);
export const InventorySynchronizationRatio = (
  <>
    <FormNumberInputField
      name={StoreHandleConfig.StockInventoryRatio}
      label={`${StoreHandleItems[StoreHandleConfig.StockInventoryRatio]}：`}
      required
      props={{
        addonAfter: '%',
        min: 1,
        max: 100,
      }}
      validators={[Validators.required('实物可售库存比例值不能为空')]}
    ></FormNumberInputField>
    <FormSelectField
      name={StoreHandleConfig.PlanStockInventoryRatio}
      label={`${StoreHandleItems[StoreHandleConfig.PlanStockInventoryRatio]}：`}
      props={{
        options: PlanStockOptions,
        width: 200,
      }}
      validators={[Validators.required('计划可售库存比例值不能为空')]}
      required
      helpDesc={
        <div>
          <p className="tip">
            设置外部渠道店铺同步库存的比例，外部渠道店铺的库存=实物可售库存*共享比例 +
            计划可售库存*共享比例，取整数。
          </p>
          <p className="tip">
            例如：设置实物可售库存为80%，计划可售库存为不共享（即共享比例0%），则每次共享到该渠道的库存=实物可售库存*80%
            + 计划可售库存*0%，直至可售库存为0，减少同时下单超卖情况发生。
          </p>
        </div>
      }
    ></FormSelectField>
  </>
);

export function StockRealTimeSync() {
  return (
    <FormRadioGroupField
      name={StoreHandleConfig.stockRealTimeSync}
      label="库存实时同步："
      helpDesc="库存敏感度高（如面包/蛋糕/商超等商家）建议超高要求，日库存充足（如餐饮/咖啡茶饮等商家）选择中等要求"
    >
      <Radio value="50">超高要求</Radio>
      <Radio value="15">中等要求</Radio>
    </FormRadioGroupField>
  );
}

export const SynchronizationOfBusinessHours = props => (
  <FormRadioGroupField
    name={StoreHandleConfig.synchronizationOfBusinessHours}
    label={`${StoreHandleItems[StoreHandleConfig.synchronizationOfBusinessHours]}：`}
    helpDesc={
      <div>
        <p className="tip">
          是否将有赞店铺的营业时间自动同步至外部渠道店铺，可在“
          {isSingleStore ? (
            <>
              设置-
              <BlankLink href="https://store.youzan.com/v4/setting/team/index">通用设置</BlankLink>
            </>
          ) : (
            <>
              店铺-
              <BlankLink href="https://store.youzan.com/v2/retailshop/org-manage">
                组织机构
              </BlankLink>
            </>
          )}
          ”中设置有赞店铺的营业时间。
        </p>
        <p className="tip">目前只支持每天重复的营业时间同步，每周重复设置不支持。</p>
      </div>
    }
    props={{
      disabled: props.disabled,
    }}
  >
    <Radio value={EStoreOperatingStatus.on}>同步</Radio>
    <Radio value={EsynchronizationOfBusinessHours.off}>不同步</Radio>
  </FormRadioGroupField>
);

export const DyServeSetting = () => {
  const [fileList, setFileList] = useState<any>([]);
  const fileInfo = Form.useField(
    'fileInfo',
    {
      fileUrl: '',
      fileName: '',
    },
    [
      value => {
        if (!value.fileUrl) {
          return {
            name: 'required',
            message: '文件不能为空',
          };
        }
        return null;
      },
    ],
  );
  return (
    <FormControl label="接单批量设置：" required>
      <Upload
        // className={formData.file.error && cx('upload--error')}
        accept=".xls,.csv,.xlsx"
        maxSize={2 * 1024 * 1024}
        maxAmount={1}
        fileList={fileList}
        onChange={(files = []) => {
          if (!files.length) {
            fileInfo.patchValue({
              fileUrl: '',
              fileName: '',
            });
            fileInfo.isTouched = true;
            fileInfo.validate();
          }
          setFileList(files);
        }}
        onError={type => {
          if (type === 'overMaxAmount') {
            Notify.error('最多可上传 1 个文件');
          } else if (type === 'overMaxSize') {
            Notify.error('文件大小不能超过 2M');
          }
        }}
        onUpload={async (file, report) => {
          const fileName = file.name;
          const pattern = /^[\u4e00-\u9fa5a-zA-Z0-9._-]+$/;
          if (!pattern.test(fileName)) {
            const msg = '文件名仅支持中英文和数字, 请修改后重新添加';
            setFileList([]);
            Notify.error(msg);
            return Promise.reject(msg);
          }
          const { uptoken: token } = await getUploadToken();
          if (!token) return Promise.reject('上传文件失败');

          const id = Math.random()
            .toString(32)
            .slice(2);
          const data = new FormData();
          data.append('key', `${id}${KdtId}_${fileName.replace(/\s/g, '_')}`);
          data.append('token', token);
          data.append('file', file);
          try {
            report(0);
            const res = await ajax({
              url: 'https://upload.qiniup.com',
              method: 'POST',
              contentType: 'multipart/form-data; charset=UTF-8',
              data,
              onUploadProgress: ({ loaded, total }) => {
                report((loaded / total) * 100);
              },
            });
            fileInfo.patchValue({
              fileUrl: res.attachment_url,
              fileName: res.attachment_file,
            });
            fileInfo.isTouched = true;
            return Promise.resolve();
          } catch (error) {
            return Promise.reject('上传文件失败');
          }
        }}
      />
      <FormError>{fileInfo.error?.message}</FormError>
      <FormDescription>
        <p>
          仅支持格式为.xls,xlsx和.csv的Excel文件,可先
          <LinkButton
            className="dy-channel-export-tips"
            onClick={() => {
              exportOrgInfo({
                searchMap: {
                  channelId: CHANNEL_ID.TIKTOK_SHELF,
                },
                exportType: 5,
              }).then(() => {
                Notify.success('导出任务提交成功, 正在打开导出列表', 2000, () => {
                  window.open(
                    setUrlDomain(
                      '/v2/retailshop/org-manage/export-list?exportType=5&showColsInfo=0&contentType=接单设置',
                      'store',
                    ),
                  );
                });
              });
            }}
          >
            导出现有接单设置
          </LinkButton>
          ，修改现有接单设置导出文件后再上传
        </p>
        <p>目前支持每天重复和每周重复的营业时间同步,自定义日期不支持</p>
      </FormDescription>
    </FormControl>
  );
};

export const OrderInvoiceSwitchField = (
  <FormRadioGroupField
    name={StoreHandleConfig.OrderInvoiceSwitch}
    label={`${StoreHandleItems[StoreHandleConfig.OrderInvoiceSwitch]}：`}
    className="allow-open-invoice-label"
    helpDesc={
      <p className="tip">
        开启后，外卖渠道订单，可以通过扫小票“开票二维码”进行开票；注：请务必保证”有赞商品和外卖渠道商品做好关联，否则也无法成功开票”。
      </p>
    }
  >
    <Radio value={AllowOpenInvoiceStatus.on}>开启</Radio>
    <Radio value={AllowOpenInvoiceStatus.off}>关闭</Radio>
  </FormRadioGroupField>
);

export const shopStatusRelatedChannels = (
  <FormRadioGroupField
    name={StoreHandleConfig.ShopStatusRelatedChannels}
    label={`${StoreHandleItems[StoreHandleConfig.ShopStatusRelatedChannels]}：`}
  >
    <Radio value={EStoreOperatingStatus.on}>开启</Radio>
    <Radio value={EStoreOperatingStatus.off}>关闭</Radio>
  </FormRadioGroupField>
);

export const GoodsUpdateRuleField = ({ isMeiTuanShangou, isJdTakeout, label = null }: {
  isMeiTuanShangou?: boolean;
  isJdTakeout?: boolean;
  label?: string | null;
}) => {
  return (
    <FormCheckboxGroupField
      name={StoreHandleConfig.HqTemplateInfoSync}
      label={label}
      className="allow-open-invoice-label"
      helpDesc={
        <p className="tip">
          默认总部强管控，商品库更新将覆盖分店外卖渠道商品信息；非总部强管控可取消勾选。
        </p>
      }
    >
      {/* <Checkbox value={GoodsUpdateRuleItem.stockNum}>商品库存</Checkbox> */}
      {!isMeiTuanShangou && (
        <Checkbox value={GoodsUpdateRuleItem.packingCharge}>商品打包费</Checkbox>
      )}
      <Checkbox value={GoodsUpdateRuleItem.minimumPurchaseNum}>最小起购量</Checkbox>
      <Checkbox value={GoodsUpdateRuleItem.title}>商品名称</Checkbox>
      {!isSingleStore && (
        <>
          <Checkbox value={GoodsUpdateRuleItem.group}>商品分组</Checkbox>
          <Pop trigger="hover" content="商品与分组的关联关系">
            <Icon
              style={{
                color: '#cacaca',
                transform: 'translate(-22px, -4px)',
              }}
              type="help-circle"
            />
          </Pop>
        </>
      )}
      <Checkbox value={GoodsUpdateRuleItem.price}>价格</Checkbox>
      {!isJdTakeout && <Checkbox value={GoodsUpdateRuleItem.skuDisableStatus}>规格启/禁用</Checkbox>}
      {/* <Checkbox value={GoodsUpdateRuleItem.prop}>属性</Checkbox> */}
    </FormCheckboxGroupField>
  );
};

export const YouzanGoodsUpdateRuleField = ({ isMeiTuanShangou, isMeiTuan }) => {
  return (
    <FormCheckboxGroupField
      name={StoreHandleConfig.OutItemInfoSync}
      className="allow-open-invoice-label"
    >
      <div>
        <div className='custom-label'>库存数量：</div>
        <Checkbox value={YouzanGoodsUpdateRuleItem.stockNum}>商品库存</Checkbox>
      </div>
      <div>
        <div className='custom-label'>基础信息：</div>
          <Checkbox value={YouzanGoodsUpdateRuleItem.title}>商品名称</Checkbox>
          <Checkbox value={YouzanGoodsUpdateRuleItem.category}>商品类目</Checkbox>
          <Checkbox value={YouzanGoodsUpdateRuleItem.prop}>属性{isMeiTuan ? '（不含加价属性）' : ''}
            <Pop trigger="hover" content={isMeiTuan ? "不支持小料自定义，有赞加价属性=美团外卖小料" : '不含加价属性，只对免费属性起作用'}>
              <Icon
                style={{
                  color: '#cacaca',
                  transform: 'translate(0px, -4px)',
                }}
                type="help-circle"
              />
            </Pop>
          </Checkbox>
          <Checkbox value={YouzanGoodsUpdateRuleItem.picture}>商品主图</Checkbox>
          <Checkbox value={YouzanGoodsUpdateRuleItem.group}>商品分组</Checkbox>
          <Pop trigger="hover" content="商品与分组的关联关系">
            <Icon
              style={{
                color: '#cacaca',
                transform: 'translate(-22px, -4px)',
              }}
              type="help-circle"
            />
          </Pop>
          <Checkbox value={YouzanGoodsUpdateRuleItem.sellPoint}>商品描述</Checkbox>
        </div>
      <div>
      <div>
        <div className='custom-label'>价格信息：</div>
          <Checkbox value={YouzanGoodsUpdateRuleItem.price}>价格</Checkbox>
          {!isMeiTuanShangou && (
            <Checkbox value={YouzanGoodsUpdateRuleItem.packingCharge}>商品打包费</Checkbox>
          )}
        </div>
      </div>
      <div>
        <div className='custom-label'>其他信息：</div>
        <Checkbox value={YouzanGoodsUpdateRuleItem.minimumPurchaseNum}>最小起购量</Checkbox>
      </div>
      
      
    </FormCheckboxGroupField>
  );
};

const HandleDialog: React.FC<IHandleDialogProps> = props => {
  const { type, form, hasMeituanChannel } = props;
  let context = <></>;
  if (type === StoreHandleConfig.storeOperatingStatus) {
    context = <StoreOperatingStatus />;
  }
  if (type === StoreHandleConfig.InventorySynchronizationRatio) {
    context = InventorySynchronizationRatio;
  }
  if (type === StoreHandleConfig.synchronizationOfBusinessHours) {
    context = <SynchronizationOfBusinessHours />;
  }
  if (type === StoreHandleConfig.stockRealTimeSync) {
    context = <StockRealTimeSync />;
  }

  if (type === BatchActionType.StockSetting) {
    context = (
      <>
        {hasMeituanChannel && <StockRealTimeSync />}
        {InventorySynchronizationRatio}
      </>
    );
  }
  if (type === StoreHandleConfig.ShopStatusRelatedChannels) {
    context = shopStatusRelatedChannels;
  }

  if (type === BatchActionType.DyServeSetting) {
    context = <DyServeSetting />;
  }

  return (
    <Form layout="horizontal" form={form}>
      <FormContext.Provider value={{ labelStyle: { flexBasis: 115 } }}>
        {context}
      </FormContext.Provider>
    </Form>
  );
};

export default HandleDialog;
