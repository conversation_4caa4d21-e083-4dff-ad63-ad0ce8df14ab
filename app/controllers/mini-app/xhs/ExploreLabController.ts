import { API, Index, Inject, Router } from '@youzan/assets-route-plugin';

import { ShopAbilityUtil } from '@youzan/plugin-shop-ability';
import ShopConfigReadService from '../../../services/api/shopcenter/shopconfig/ShopConfigReadService';
import ShopConfigWriteService from '../../../services/api/shopcenter/shopconfig/ShopConfigWriteService';
import CommonBasicInfoController from '../common/BasicInfoController';

/**
 * 小红书探索实验室业务
 */
@Router('xhs/explore-lab')
class ExploreLabController extends CommonBasicInfoController {
  @Inject()
  public readonly shopConfigWriteService!: ShopConfigWriteService;

  @Inject()
  public readonly shopConfigReadService!: ShopConfigReadService;

  public async init() {
    this.channelName = 'xhs';
    super.init();
  }

  @Index(['', 'xhs-independent'])
  public async getExploreLabHtml() {
    const { ctx } = this;

    // 小红书独立版本判断
    const hasXhsIndependentAbility = await new ShopAbilityUtil(ctx).checkAbilityValid({
      keys: ['xhs_independent_ability'],
      kdtId: ctx.kdtId,
    });
    if (hasXhsIndependentAbility && ctx.url.indexOf('xhs-independent') < 0) {
      this.ctx.redirect('/v4/channel/xhs/explore-lab/xhs-independent');
      return;
    }

    ctx.setState('subtitle', '探索实验室');

    await this.resolveXhsLocalLifeInfo(ctx);

    return ctx.render('mini-app/xhs/explore-lab/index.html');
  }

  // 查询小红书加购开启状态
  @API('GET', 'get-xhs-add-cart-config.json')
  public async getXhsAddCartConfig() {
    const { value } = await this.shopConfigReadService
      .queryShopConfig(this.ctx.kdtId, 'xhs_support_add_cart')
      .catch(() => ({
        value: '0',
      }));

    return this.ctx.json(0, 'ok', value);
  }

  // 设置小红书加购开启状态
  @API('POST', 'set-xhs-add-cart-config.json')
  public async setXhsAddCartConfig() {
    const { value } = this.ctx.request.body;
    const operator = this.getOperatorParams();
    const params = {
      kdtId: this.ctx.kdtId,
      key: 'xhs_support_add_cart',
      value,
      operator: {
        fromApp: operator.fromApp,
        name: operator.operator.nickName,
        id: operator.operator.userId,
        type: 1,
      },
    };
    const res = await this.shopConfigWriteService.setShopConfig(params);
    return this.ctx.json(0, 'ok', res);
  }
}

export = ExploreLabController;
