import { Router, Index, Inject, API, Metadata } from '@youzan/assets-route-plugin';
import CommonBasicInfoController from '../common/BasicInfoController';
import XhsAppAccountService from '../../../services/api/channels/channel/core/XhsAppAccountService';
import ContentPublishPlanApiService from '../../../services/api/channels/channel/core/ContentPublishPlanApiService';
import ContentNoteApiService from '../../../services/api/channels/channel/core/ContentNoteApiService';
import AgentBuildService from '../../../services/api/channels/channel/core/AgentBuildService';
import { ShopAbilityUtil } from '@youzan/plugin-shop-ability';

@Router('xhs-note')
class XhsAppAccountController extends CommonBasicInfoController {
  @Inject()
  private acountService!: XhsAppAccountService;

  @Inject()
  private planService!: ContentPublishPlanApiService;

  @Inject()
  private noteService!: ContentNoteApiService;

  @Inject()
  private agentBuildService!: AgentBuildService;

  public async init() {
    this.channelName = 'xhs';
    super.init();
  }

  @Index(['', 'xhs-independent'])
  @Metadata('INDEX')
  async getIndexHtml() {
    const { ctx } = this;

    const hasXhsIndependentAbility = await new ShopAbilityUtil(ctx).checkAbilityValid({
      keys: ['xhs_independent_ability'],
      kdtId: ctx.kdtId,
    });

    if (hasXhsIndependentAbility && ctx.url.indexOf('xhs-independent') < -1) {
      return ctx.redirect('/v4/xhs-note/xhs-independent');
    }

    const isAgentFinished = await this.checkAgentFinished(ctx);
    if (!isAgentFinished) {
      return ctx.redirect('/v4/jiawo/agent-intro/index');
    }

    const shopInfo = ctx.getState('shopInfo');
    const { kdtId, rootKdtId = kdtId } = shopInfo;
    const [accountList, industryType, accountLimit] = await Promise.all([
      this.queryAccountList({ kdtId, rootKdtId }).catch(() => []),
      this.queryIndustryTypeList().catch(() => []),
      this.getAccountLimit(ctx),
    ]);

    ctx.setGlobal('accountList', accountList);
    ctx.setGlobal('industryType', industryType);
    ctx.setGlobal('accountLimit', accountLimit);

    return ctx.render('mini-app/xhs/note/index.html');
  }

  get kdtIdAndRootKdtId() {
    const kdtId = this.ctx.kdtId;
    const rootKdtId = this.getRootKdtId();

    return { kdtId, rootKdtId };
  }

  injectKdtId(data = {}) {
    const { kdtIdAndRootKdtId } = this;
    return Object.assign({}, data, kdtIdAndRootKdtId);
  }

  async checkAgentFinished(ctx: any) {
    const { kdtId } = ctx;
    const operator = this.getOperatorParams().operator;
    const { userId, nickName } = operator;
    const params = {
      kdtId,
      operator: {
        operatorId: `${userId}`,
        operatorName: nickName,
      },
    };

    return this.agentBuildService.isFinished(params).catch(() => false);
  }

  async getAccountLimit(ctx: any) {
    const { kdtId } = ctx;
    let limit = {} as any;
    try {
      const config = await ctx.apolloClient.getConfig({
        appId: 'wsc-pc-channel',
        namespace: `wsc-pc-channel.whitelist`,
        key: `ai-xhs-account`,
      });

      if (typeof config === 'object') {
        limit = config;
      }
    } catch (error) {}

    return +limit[kdtId] || 10;
  }

  // 查询行业列表
  async queryIndustryTypeList() {
    const result = await this.acountService.queryIndustryTypeList();
    return result;
  }

  // 保存账号
  @API('POST', 'saveAccount')
  async saveAccount() {
    const postData = this.ctx.getPostData();
    const params = this.injectKdtId(postData);
    const result = await this.acountService.saveAccount(params);
    return this.ctx.json(0, 'ok', result);
  }

  // 删除账号
  @API('POST', 'deleteAccount')
  async deleteAccount() {
    const postData = this.ctx.getPostData();
    const params = this.injectKdtId(postData);
    const result = await this.acountService.deleteAccount(params);
    return this.ctx.json(0, 'ok', result);
  }

  async queryAccountList(params: any) {
    const result = await this.acountService.queryAccount(params).catch(() => []);
    return result;
  }

  // 查询账号列表
  @API('GET', 'queryAccount')
  async queryAccount() {
    const queryData = this.ctx.getQueryData();
    const params = this.injectKdtId(queryData);
    const result = await this.queryAccountList(params);
    return this.ctx.json(0, 'ok', result);
  }

  // 查询账号详情
  @API('GET', 'getAccount')
  async getAccount() {
    const queryData = this.ctx.getQueryData();
    const { channel } = queryData;
    const params = this.injectKdtId({ ...queryData, channel: +channel });
    const result = await this.acountService.getAccount(params);
    return this.ctx.json(0, 'ok', result);
  }

  // 发布计划列表查询
  @API('GET', 'pagePlan')
  async pagePlan() {
    const queryData = this.ctx.getQueryData();
    const params = this.injectKdtId(queryData);
    const result = await this.planService.pagePlan(params);
    return this.ctx.json(0, 'ok', result);
  }

  // 计划项列表查询
  @API('GET', 'pagePlanItems')
  async pagePlanItems() {
    const queryData = this.ctx.getQueryData();
    const params = this.injectKdtId(queryData);
    const result = await this.planService.pagePlanItems(params);
    return this.ctx.json(0, 'ok', result);
  }

  // 查询可关联笔记
  @API('GET', 'queryNotePage')
  async queryNotePage() {
    const queryData = this.ctx.getQueryData();
    const params = this.injectKdtId(queryData);
    const result = await this.noteService.queryNotePage(params);
    return this.ctx.json(0, 'ok', result);
  }

  // 关联/重新关联笔记
  @API('POST', 'planItemAssociateNote')
  async planItemAssociateNote() {
    const postData = this.ctx.getPostData();
    const params = this.injectKdtId(postData);
    const result = await this.planService.planItemAssociateNote(params);
    return this.ctx.json(0, 'ok', result);
  }

  // 解除关联笔记
  @API('POST', 'planItemDisassociateNote')
  async planItemDisassociateNote() {
    const postData = this.ctx.getPostData();
    const params = this.injectKdtId(postData);
    const result = await this.planService.planItemDisassociateNote(params);
    return this.ctx.json(0, 'ok', result);
  }

  // 查询草稿计划
  @API('GET', 'queryLatestDraftPlan')
  async queryLatestDraftPlan() {
    const queryData = this.ctx.getQueryData();
    const result = await this.planService.queryLatestDraftPlan(queryData);
    return this.ctx.json(0, 'ok', result);
  }

  // 执行发布计划
  @API('POST', 'startPlan')
  async startPlan() {
    const postData = this.ctx.getPostData();
    const params = this.injectKdtId(postData);
    const result = await this.planService.startPlan(params);
    return this.ctx.json(0, 'ok', result);
  }

  // 停止发布计划
  @API('POST', 'stopPlan')
  async stopPlan() {
    const postData = this.ctx.getPostData();
    const params = this.injectKdtId(postData);
    const result = await this.planService.stopPlan(params);
    return this.ctx.json(0, 'ok', result);
  }

  // 使用发布计划
  @API('POST', 'usePlan')
  async usePlan() {
    const postData = this.ctx.getPostData();
    const params = this.injectKdtId(postData);
    const result = await this.planService.usePlan(params);
    return this.ctx.json(0, 'ok', result);
  }
}

export = XhsAppAccountController;
