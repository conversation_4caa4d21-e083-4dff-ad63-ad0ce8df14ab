import BaseController from '../base/BaseController';
import { API, Router, Inject } from '@youzan/assets-route-plugin';
import ShopConfigReadService from '../../services/api/shopcenter/shopconfig/ShopConfigReadService';
import ShopConfigWriteService from '../../services/api/shopcenter/shopconfig/ShopConfigWriteService';
import TiktokUnderwritingService from '../../services/api/channels/tiktok/TiktokUnderwritingService';

@Router('configs')
class ShopController extends BaseController {
  public async init() {
    await super.init();
  }

  @Inject()
  ShopConfigReadService!: ShopConfigReadService;

  @Inject()
  shopConfigWriteService!: ShopConfigWriteService;

  @Inject()
  tiktokUnderwritingService!: TiktokUnderwritingService;

  @API('GET', 'getShopConfigs.json')
  public async getShopConfigs() {
    const { ctx } = this;
    let { configs = '' } = ctx.getQuery();
    try {
      configs = configs.split(',');
    } catch (error) {
      configs = [];
    }
    const result = await this.ShopConfigReadService.queryShopConfigs(ctx.kdtId, configs);
    ctx.json(0, 'ok', result);
  }

  @API('POST', 'setShopConfig.json')
  public async setShopConfig() {
    const { kdtId } = this.ctx;
    const { params } = this.ctx.request.body;
    const baseOperator = this.getOperatorParams();

    if (Array.isArray(params)) {
      await Promise.all(
        params.map(({ key, value }: { key: string; value: string }) => {
          return this.shopConfigWriteService.setShopConfig({
            kdtId,
            key,
            value,
            operator: {
              fromApp: baseOperator.fromApp,
              name: baseOperator.operator.nickName,
              id: baseOperator.operator.userId,
              type: 1,
            },
          });
        }),
      );
      this.ctx.json(0, 'ok', true);
    } else {
      const { configs, operatorConfig } = params;
      await Promise.all(
        configs.map(({ key, value }: { key: string; value: string }) => {
          return this.shopConfigWriteService.setShopConfig({
            kdtId,
            key,
            value,
            operator: {
              fromApp: operatorConfig.fromApp || baseOperator.fromApp,
              name: operatorConfig.name ?? baseOperator.operator.nickName,
              id: baseOperator.operator.userId,
              type: operatorConfig.type || 1,
            },
          });
        }),
      );
      this.ctx.json(0, 'ok', true);
    }
  }

  @API('POST', 'updateMerchantConf')
  public async updateMerchantConf() {
    const { kdtId } = this.ctx;
    const data = this.ctx.request.body;
    const baseOperator = this.getOperatorParams();

    await this.tiktokUnderwritingService.updateMerchantConf({
      kdtId,
      ...data,
      operator: {
        role: 'seller',
        operatorId: baseOperator.operator.userId,
      },
    });

    this.ctx.json(0, 'ok', true);
  }

  @API('POST', 'updateMerchantPath')
  public async updateMerchantPath() {
    const { kdtId } = this.ctx;
    const { openType } = this.ctx.request.body;
    const baseOperator = this.getOperatorParams();

    await this.tiktokUnderwritingService.updateMerchantPath({
      kdtId,
      openType: +openType,
      operator: {
        role: 'seller',
        operatorId: baseOperator.operator.userId,
      },
    });

    this.ctx.json(0, 'ok', true);
  }

  @API('GET', 'queryMerchantConfigurationText')
  public async queryMerchantConfigurationText() {
    const { kdtId } = this.ctx;

    const res = await this.tiktokUnderwritingService.queryMerchantConfigurationText({
      kdtId,
      textType: 1,
    });

    this.ctx.json(0, 'ok', res);
  }
}

export = ShopController;
