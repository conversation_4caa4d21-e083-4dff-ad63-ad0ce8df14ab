import { Router, Inject, API } from '@youzan/assets-route-plugin';

import CommonBasicInfoController from '../common/BasicInfoController';
import XhsShopOpenService from '../../../services/api/ebiz/video/channels/uc/XhsShopOpenService';
import XiaohongshuChannelConsoleService from '../../../services/api/ebiz/video/channels/item/xhs/XiaohongshuChannelConsoleService';
import ChannelApplyCategoryService from '../../../services/api/ebiz/video/channels/uc/ChannelApplyCategoryService';
import ChannelCategoryService from '../../../services/api/ebiz/video/channels/uc/ChannelCategoryService';

@Router('xhs/shop-open')
class ShopOpenController extends CommonBasicInfoController {
  @Inject()
  private readonly XhsShopOpenService!: XhsShopOpenService;

  @Inject()
  private readonly ChannelApplyCategoryService!: ChannelApplyCategoryService;

  @Inject()
  private readonly ChannelCategoryService!: ChannelCategoryService;

  @Inject()
  private readonly xhsChannelConsoleService!: XiaohongshuChannelConsoleService;

  public async init() {
    this.channelName = 'xhs';
    super.init();
  }

  @API('GET', 'queryTasks.json')
  public async queryTasks() {
    const { ctx } = this;
    const { kdtId } = ctx;

    const params = { kdtId };

    await this.XhsShopOpenService.repairTasks(params).catch(() => {});

    const result = await this.XhsShopOpenService.queryTasks(params);

    await this.ctx.json(0, 'ok', result);
  }

  @API('GET', 'submitSpecialtyAuth.json')
  public async submitSpecialtyAuth() {
    const { ctx } = this;
    const { kdtId, userId } = ctx;

    const params = {
      kdtId,
      operatorId: userId,
    };

    const result = await this.XhsShopOpenService.submitSpecialtyAuth(params);

    await this.ctx.json(0, 'ok', result);
  }

  @API('POST', 'submitAuth.json')
  public async submitAuth() {
    const { ctx } = this;
    const { request } = this.ctx;
    const { xhsUserId = '' } = request.body;
    const { kdtId, userId } = ctx;

    const result = await this.XhsShopOpenService.submitAuth({
      kdtId,
      xhsUserId,
      operatorId: userId,
    });

    await this.ctx.json(0, 'ok', result);
  }

  @API('POST', 'submitAppInfo.json')
  public async submitAppInfo() {
    const { ctx } = this;
    const { request } = this.ctx;
    const {
      appName,
      appId,
      appDesc,
      appAvatar,
      supplementaryMaterials = [],
      appShortName,
    } = request.body;
    const { kdtId } = ctx;

    const result = await this.XhsShopOpenService.submitAppInfo({
      kdtId,
      appName,
      appId,
      appDesc,
      appAvatar,
      supplementaryMaterials,
      appShortName,
    });

    await this.ctx.json(0, 'ok', result);
  }

  @API('POST', 'submitOpenTradeAbility.json')
  public async submitOpenTradeAbility() {
    const { ctx } = this;
    const { request } = this.ctx;
    const {
      cardQualification,
      idType,
      legalPersonIdentifyNo,
      legalPersonIdentifyName,
      validity,
    } = request.body;
    const { kdtId } = ctx;

    const result = await this.XhsShopOpenService.submitOpenTradeAbility({
      kdtId,
      cardQualification,
      idType,
      legalPersonIdentifyNo,
      legalPersonIdentifyName,
      validity,
    });

    await this.ctx.json(0, 'ok', result);
  }

  @API('POST', 'batchSave.json')
  public async batchSave() {
    const { ctx } = this;
    const { request } = this.ctx;
    const { catInfos, channel, mpId } = request.body;
    const { kdtId } = ctx;

    const result = await this.ChannelApplyCategoryService.batchSave({
      kdtId,
      catInfos,
      channel,
      mpId,
    });

    await this.ctx.json(0, 'ok', result);
  }

  @API('GET', 'queryExistCategoryList.json')
  public async queryExistCategoryList() {
    const { ctx } = this;
    const { request } = this.ctx;
    const { catIds, channel, statuses = '' } = request.query;
    const { kdtId } = ctx;

    const result = await this.ChannelApplyCategoryService.queryExistCategoryList({
      kdtId,
      catIds,
      channel,
      statuses: statuses ? statuses.split(',') : [],
    });

    await this.ctx.json(0, 'ok', result);
  }

  @API('POST', 'uploadQualification.json')
  public async uploadQualification() {
    const { ctx } = this;
    const { request } = this.ctx;
    const { catId, channel, mpId, qualificationList } = request.body;
    const { kdtId } = ctx;

    const result = await this.ChannelApplyCategoryService.uploadQualification({
      kdtId,
      catId,
      channel,
      mpId,
      qualificationList,
    });

    await this.ctx.json(0, 'ok', result);
  }

  @API('POST', 'delete.json')
  public async delete() {
    const { ctx } = this;
    const { request } = this.ctx;
    const { catId, channel, mpId } = request.body;
    const { kdtId } = ctx;

    const result = await this.ChannelApplyCategoryService.delete({
      kdtId,
      catId,
      channel,
      mpId,
    });

    await this.ctx.json(0, 'ok', result);
  }

  @API('GET', 'searchCategories.json')
  public async searchCategories() {
    const { ctx } = this;
    const { request } = this.ctx;
    const { parentCatId, channel, mpId, keyword, bizType } = request.query;
    const { kdtId } = ctx;

    const result = await this.ChannelCategoryService.searchCategories({
      kdtId,
      parentCatId,
      keyword,
      channel: +channel,
      mpId,
      bizType,
    });

    await this.ctx.json(0, 'ok', result);
  }

  @API('GET', 'submitQuickOpen.json')
  public async submitQuickOpen() {
    const { kdtId, userId } = this.ctx;
    const result = await this.XhsShopOpenService.submitQuickOpen({ kdtId, operatorId: userId });

    this.ctx.json(0, 'ok', result);
  }

  @API('GET', 'queryQuickOpen.json')
  public async queryQuickOpen() {
    const { kdtId } = this.ctx;
    const result = await this.XhsShopOpenService.queryQuickOpen({ kdtId });

    this.ctx.json(0, 'ok', result);
  }

  @API('GET', 'initShopPage.json')
  public async initShopPage() {
    const { kdtId, request, userId } = this.ctx;
    const { query } = request;
    const result = await this.XhsShopOpenService.initShopPage({
      kdtId,
      operatorId: userId,
      ...query,
    });

    this.ctx.json(0, 'ok', result);
  }

  @API('GET', 'queryShopPage.json')
  public async queryShopPage() {
    const { kdtId } = this.ctx;
    const result = await this.XhsShopOpenService.queryShopPage({ kdtId });

    this.ctx.json(0, 'ok', result);
  }

  @API('GET', 'autoAddItem.json')
  public async autoAddItem() {
    const { kdtId, request, userId } = this.ctx;
    const { query } = request;
    const result = await this.xhsChannelConsoleService.autoAddItem({
      kdtId,
      operatorId: userId,
      ...query,
    });

    this.ctx.json(0, 'ok', result);
  }

  @API('GET', 'queryAutoAddItemResult.json')
  public async queryAutoAddItemResult() {
    const { kdtId, request } = this.ctx;
    const { query } = request;
    const result = await this.xhsChannelConsoleService.queryAutoAddItemResult({
      kdtId,
      ...query,
    });

    this.ctx.json(0, 'ok', result);
  }
}

export = ShopOpenController;
