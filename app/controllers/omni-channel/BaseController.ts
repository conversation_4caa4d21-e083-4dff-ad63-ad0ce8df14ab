import { Inject } from '@youzan/assets-route-plugin';
import _ from 'lodash';
import { checkLiteOnlineStoreManager, checkStoreOnlineStoreManager } from '@youzan/utils-shop';
import BaseController from '../base/BaseController';
import ChannelCoreAccountService from '../../services/api/channels/channel/core/ChannelCoreAccountService';
import StaffServiceV2 from '../../services/api/sam/StaffServiceV2';
import RoleService from '../../services/api/rig/RoleService';
import channelMap from '../../constants/channelMap';
import MpVersionService from '../../services/api/channels/apps/MpVersionService';

type IObject = Record<string, any>;

class OmniChannelBaseController extends BaseController {
  @Inject()
  public readonly channelCoreAccountService!: ChannelCoreAccountService;

  @Inject()
  public readonly staffServiceV2!: StaffServiceV2;

  @Inject()
  public readonly roleService!: RoleService;

  @Inject()
  public readonly mpVersionService!: MpVersionService;

  public get baseInfo() {
    const baseInfo = this.getOperatorParams();
    return {
      id: baseInfo.operator.userId,
      name: baseInfo.operator.nickName,
      // 1 表示操作人是有赞账号
      type: 1,
      fromApp: baseInfo.fromApp,
      entryApp: baseInfo.operator.source,
    };
  }

  public async checkLiteOnlineStoreManager() {
    const { ctx } = this;

    const [staffInfo, roleList] = await Promise.all([
      this.staffServiceV2.getStaff({
        kdtId: ctx.kdtId,
        adminId: ctx.userId,
        biz: 'retail',
      }),
      this.roleService.getRoleList({
        thirdTenantId: `${ctx.kdtId}`,
        thirdUserId: `${ctx.adminId}`,
        namespace: 'np_yz_shop',
        rigSource: 'retail_node',
        filter: {
          bizKeyGroup: 'shop_ability',
        },
      }),
    ]);

    const roleId2extProps: IObject = _.reduce(
      roleList,
      (acc: IObject, cur) => {
        acc[cur.roleId] = cur.extProperties || null;
        return acc;
      },
      {},
    );

    const roles = _.map(staffInfo?.roleList, role => ({
      ...role,
      extProperties: roleId2extProps[role.roleId],
    }));

    return checkLiteOnlineStoreManager(roles) || checkStoreOnlineStoreManager(roles);
  }

  public getVersionInfo(options: IObject) {
    const { kdtId, channelType, channelName } = options;
    const currentChannelMeta = {
      ...(channelMap as any)[channelType as string][channelName],
      channelName,
    };
    // @ts-ignore
    const { accountType, bundleId, businessType = 1 } = currentChannelMeta;

    const params = {
      accountType,
      businessType,
      kdtId,
    };
    if (bundleId) {
      // @ts-ignore
      params.bundleId = bundleId;
    }
    return this.mpVersionService.getMpVersion(params);
  }
}

export default OmniChannelBaseController;
