import { Router, Index, Metadata } from '@youzan/assets-route-plugin';
import CommonBasicInfoController from '../common/BasicInfoController';

@Router('xhs/category-setting')
class CategorySettingController extends CommonBasicInfoController {
  public async init() {
    this.channelName = 'xhs';
    super.init();
  }

  @Index('')
  @Metadata('INDEX')
  async getCategorySettingHtml() {
    const { ctx } = this;

    const res = await this.HongShuMiniAppService.getAccountBasicInfo({
      kdtId: ctx.kdtId,
      businessType: 1,
      accountType: 16,
    });

    ctx.setState('subtitle', '类目设置');

    if (res?.appId && res?.mpId) {
      return ctx.render('mini-app/xhs/category-setting/index.html');
    }
    return ctx.redirect('/v4/channel/xhs/dashboard');
  }
}

export = CategorySettingController;
