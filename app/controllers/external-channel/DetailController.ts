import { Router, Index, API, Inject } from '@youzan/assets-route-plugin';
import { checkRetailChainStore, checkRetailSingleStore } from '@youzan/utils-shop';
import { ShopAbilityUtil } from '@youzan/plugin-shop-ability';
import BaseController from './BaseController';
import BBSPostService from '../../services/api/ebiz/mall/gemini/BBSPostService';
import SalesChannelMgrService from '../../services/api/shopcenter/shopfront/SalesChannelMgrService';
import AbilityReadService from '../../services/api/shopcenter/shopprod/api/AbilityReadService';
import ShopThirdChannelService from '../../services/api/channels/omni-channel/ShopThirdChannelService';
import ShopCenterService from '../../services/api/channels/omni-channel/ShopCenterService';
import ThirdPartyChannelShopWriteService from '../../services/api/channels/omni-channel/ThirdPartyChannelShopWriteService';
import ChannelTokenService from '../../services/api/channels/channel/core/ChannelTokenService';
import DashboardService from '../../services/api/channels/channel/core/DashboardService'; 
import CouponService from '../../services/api/channels/omni-channel/CouponService';
import ShopAbilityService from '../../services/api/channels/omni-channel/ShopAbilityService';

@Router(':channelName/detail')
class DashboardController extends BaseController {
  @Inject()
  private readonly bbsPostService!: BBSPostService;

  @Inject()
  public readonly salesChannelMgrService!: SalesChannelMgrService;

  @Inject()
  private readonly abilityReadService!: AbilityReadService;

  @Inject()
  private readonly shopThirdChannelService!: ShopThirdChannelService;

  @Inject()
  private readonly thirdPartyChannelShopWriteService!: ThirdPartyChannelShopWriteService;

  @Inject()
  public readonly shopCenterService!: ShopCenterService;

  @Inject()
  private channelTokenService!: ChannelTokenService;

  @Inject()
  private readonly dashboardService!: DashboardService;

  @Inject() 
  private readonly couponService!: CouponService;

  @Inject()
  public readonly shopAbilityService!: ShopAbilityService;

  // 判断是否是新流程北极星（休闲娱乐类目）
  async getIsNewPolarStar() {
    const rootKdtId = this.getRootKdtId();
    const relaxedChannelShopList = await this.couponService
      .isMtRelaxedChanelShop({
        rootKdtId,
        adminId: this.ctx.userId,
        retailSource: 'wsc-pc-channel',
        platform: 1,
      })
      .catch(err => {
        this.ctx.logger.error('判断是否是新流程北极星（休闲娱乐类目）失败', err);
        return {};
      });

    return Boolean(relaxedChannelShopList[rootKdtId]);
  }

  /**
   * 详情页
   */
  @Index(['', 'xhs-independent'])
  async getIndexHtml() {
    const { ctx } = this;
    const { kdtId } = ctx;
    // 小红书独立版本判断
    const hasXhsIndependentAbility = await new ShopAbilityUtil(ctx).checkAbilityValid({
      keys: ['xhs_independent_ability'],
      kdtId: ctx.kdtId,
    });
    if (hasXhsIndependentAbility && ctx.url.indexOf('xhs-independent') < 0) {
      this.ctx.redirect('/v4/channel/xhs-channel/detail/xhs-independent');
      return;
    }
    const shopMetaInfo = ctx.getState('shopMetaInfo');
    if (checkRetailChainStore(shopMetaInfo)) {
      const isSupportMultiChannel = await this.salesChannelMgrService.isHitWhiteList(kdtId);
      ctx.setGlobal('isSupportMultiChannel', isSupportMultiChannel);
    }

    if (this.channelName === 'tiktok-sxt') {
      const dySxtAbilityInfo = await this.abilityReadService
        .queryShopAbilityInfo(kdtId, 'douyin_wish_group_buy_ability')
        .catch(() => ({ abilityStatus: 0 }));
      ctx.setGlobal('isBindTiktokSxt', dySxtAbilityInfo?.abilityStatus === 1);

      return ctx.render('external/tiktok-sxt.html');
    }
    if (this.channelName === 'jd-takeout') {
      const result = await this.dashboardService.myAgents({
        kdtId,
      }).catch(() => []);
      // 有赞应用市场插件形式上架的外卖通智能体code
      const code = 'yzAppstore_10005561'; 
      const taskOut = result?.find(item => item.code === code) || {};
      ctx.setGlobal('agentId', taskOut?.id || '');
      return ctx.render('external/jd-takeout.html');
    }

    if (this.channelName === 'meituan-channel') {
      // 北极星白名单
      this.isInWhiteListByApollo('channel_meituan_dp_bjx', {
        useRootKdtId: true,
        globalKey: 'isSupportBindMeiTuanBjx',
      });
      const isRetailSingleStore = checkRetailSingleStore(shopMetaInfo);
      const [
        isSupportMultiChannel,
        isNewPolarStar,
      ] = await Promise.all([
        isRetailSingleStore
        ? Promise.resolve(false)
        : this.salesChannelMgrService.isHitWhiteList(kdtId),
        this.getIsNewPolarStar()
      ]);
      ctx.setGlobal('isSupportMultiChannel', isSupportMultiChannel);
      ctx.setGlobal('isNewPolarStar', isNewPolarStar);

      if (isRetailSingleStore === false) {
        const { valid } = await this.shopAbilityService.queryHqAbility(
          ctx.kdtId,
          'lite_online_store_manage_ability',
        );
        ctx.setGlobal('liteShopAbility', valid);
      }
    }

    return ctx.render('external/index.html');
  }

  /**
   * 获取小程序动态帖子(五篇)
   */
  @API('GET', 'getChannelBBSPostJson.json')
  public async getChannelBBSPostJson() {
    const result = await this.getChannelBBS();
    return this.ctx.json(0, 'ok', result || []);
  }

  /**
   * 查询外卖初始化进度
   */
  @API('GET', 'queryWaimaiInitStep.json')
  public async queryWaimaiInitStep() {
    const { ctx } = this;
    const { kdtId, request } = ctx;
    const { channelId } = request.query;
    const result = await this.shopThirdChannelService.queryWaimaiInitStep(kdtId, channelId);
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 更新店铺信息
   */
  @API('POST', 'updateWaimaiInitStep.json')
  public async updateWaimaiInitStep() {
    const { ctx } = this;
    const { kdtId, userId } = ctx;
    this.ctx.logger.info(`更新店铺信息：${userId} ${JSON.stringify(ctx.request.body)}`);
    const result = await this.shopThirdChannelService.updateWaimaiInitStep({
      kdtId,
      adminId: userId,
      ...ctx.request.body,
    });
    return this.ctx.json(0, 'ok', result || {});
  }

  @API('GET', 'getChannelToken.json')
  public async getChannelToken() {
    const { ctx } = this;
    const { businessType = 1, accountType } = ctx.request.query;
    this.ctx.logger.info(`获取京东渠道token：${ctx.kdtId} ${JSON.stringify(ctx.request.query)}`);
    const result = await this.channelTokenService.getChannelToken({
      kdtId: ctx.kdtId,
      accountType,
      businessType,
    });
    ctx.json(0, 'ok', result);
  }

  /**
   * 根据总部或者单店kdtid，查询外部分店店铺列表
   */
  @API('GET', 'queryUnbindOutShops.json')
  public async queryUnbindOutShops() {
    const { ctx } = this;
    const { kdtId, request } = ctx;
    const { channelId } = request.query;
    const result = await this.shopThirdChannelService.queryUnbindOutShops(kdtId, channelId);
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 智能匹配外部店铺映射
   */
  @API('GET', 'queryAutoMappingShopRelation.json')
  public async queryAutoMappingShopRelation() {
    const { ctx } = this;
    const { kdtId, request } = ctx;
    const { channelId, unBindKdtIds } = request.query;
    const params = {
      kdtId,
      channelId,
      unBindKdtIds: JSON.parse(unBindKdtIds || '[]'),
    };
    const result = await this.shopThirdChannelService.queryAutoMappingShopRelation(params);
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 更新店铺信息
   */
  @API('POST', 'batchBindThirdChannelShop.json')
  public async batchBindThirdChannelShop() {
    const { ctx } = this;
    const { kdtId } = ctx;
    this.ctx.logger.info(`同步批量绑定店铺&三方渠道店铺：${JSON.stringify(ctx.request.body)}`);
    const result = await this.thirdPartyChannelShopWriteService.batchBindThirdChannelShop({
      loginKdtId: kdtId,
      ...ctx.request.body,
    });
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 解绑外部店铺
   */
  @API('POST', 'thirdChannelUnbind.json')
  public async thirdChannelUnbind() {
    const { ctx } = this;
    const { userId } = ctx;
    const { channelId, kdtId } = ctx.request.body;
    this.ctx.logger.info(`解绑外部店铺：${JSON.stringify(ctx.request.body)}`);
    const result = await this.thirdPartyChannelShopWriteService.thirdChannelUnbind({
      channelId,
      operateId: userId,
      kdtId,
    });
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 获取开关状态接口
   */
  @API('GET', 'getProcessSettingConfig.json')
  public async getProcessSettingConfig() {
    const { kdtId } = this.ctx;
    const data = await this.shopCenterService.queryShopAbilityInfo(kdtId);
    return this.ctx.json(0, 'success', data);
  }

  /**
   * 获取小程序动态帖子(五篇)
   */
  public getChannelBBS() {
    const { ctx } = this;
    const { adGroupId } = ctx.getState('channelMeta' as any);
    return this.bbsPostService.listMiniPost({
      materialGroupId: adGroupId,
    });
  }
}

export = DashboardController;
