import { Router, API } from '@youzan/assets-route-plugin';
import { get } from 'lodash';
import BaseController from './BaseController';

@Router('alipay/settings')
class SettingsController extends BaseController {
  /**
   * 校验支付代销是否完成
   */
  @API('GET', 'get-pay-state.json')
  public async isPass() {
    const { ctx } = this;
    try {
      await this.checkPayState();
    } catch (error) {
      return ctx.json(0, 'ok', { payState: false });
    }

    ctx.json(0, 'ok', { payState: true });
  }

  /**
   * 代运营授权进度查询
   */
  @API('GET', 'get-operation-auth-process.json')
  public async getAuthProcess() {
    const { kdtId, userId } = this.ctx;
    const rootKdtId = get(this.ctx.getState('shopInfo'), 'rootKdtId', kdtId);
    const processResult = await this.alipayAntShopService.getOperationAuthProcess({
      rootKdtId,
      adminId: userId,
    });
    await this.ctx.json(0, 'ok', processResult);
  }

  /**
   * 获取授权码和已绑定/推荐支付宝账号列表
   */
  @API('GET', 'get-operation-auth-code.json')
  public async getAuthCode() {
    const { kdtId, userId } = this.ctx;
    const rootKdtId = get(this.ctx.getState('shopInfo'), 'rootKdtId', kdtId);
    try {
      const authCode = await this.alipayAntShopService.getOperationAuthCode({
        rootKdtId,
        adminId: userId,
      });
      await this.ctx.json(0, 'ok', authCode);
    } catch (err) {
      this.ctx.json(err.code, err?.extra?.response?.errorData?.erorr_detail_msg, err);
    }
  }
}

export = SettingsController;
