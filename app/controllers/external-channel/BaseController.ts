import _ from 'lodash';
import BaseController from '../base/BaseController';

class ExternalBaseController extends BaseController {
  public get baseInfo() {
    const baseInfo = this.getOperatorParams();
    return {
      id: baseInfo.operator.userId,
      name: baseInfo.operator.nickName,
      // 1 表示操作人是有赞账号
      type: 1,
      fromApp: baseInfo.fromApp,
      entryApp: baseInfo.operator.source,
    };
  }
}

export default ExternalBaseController;
