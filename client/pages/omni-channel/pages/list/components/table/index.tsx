import * as React from 'react';
import { useState, useRef } from 'react';
import {
  Sweetalert,
  Alert,
  Form,
  FormStrategy,
  Grid,
  IGridColumn,
  Notify,
  Pop,
  Icon,
  Input,
  Dialog,
  Button,
  ValidateOption,
} from 'zent';
import { isNil, get, omit, uniqBy } from 'lodash';
import { useHistory } from 'react-router-dom';
import formatDate from '@youzan/utils/date/formatDate';
import { LinkButton, Divider, BlankLink, PopEllipsisText } from '@youzan/react-components';
import redirect from '@youzan/utils/url/redirect';
import { useFetchTable } from '@youzan/hooks';
import {
  StoreHandleConfig,
  batchActionTypeOptions,
  tiktokBatchActionTypeOptions,
  BatchActionType,
} from 'pages/omni-channel/config';
import { ChannelCode, IList } from 'pages/omni-channel/config.type';
import TableBatchCpn, { checkSelected } from 'pages/omni-channel/component/table-batch-cpn';
import UnBindBtn from 'pages/omni-channel/component/un-bind';
import PopSelect from 'pages/omni-channel/component/pop-select';
import HandleDialog from 'pages/omni-channel/component/handle-dialog';
import openTikTokServeSettingDialog from '../open-tiktok-serve-setting-dialog/index';
import {
  CHANNEL_TYPE,
  CHANNEL_ID,
  PlanAllocationStatus,
  AppIdMap,
} from 'pages/omni-channel/constant';

import Promotion from '../promotion';
import BatchImportDialog from '../batch-import-dialog';
import BatchAuthDialog from '../batch-auth-dialog';
import Business from './business';

import * as api from './api';
import { isHqStore, isSingleStore } from '@youzan/utils-shop';
import { getRenderTextByBeModel } from '../open-tiktok-serve-setting-dialog/utils';
import { IsOpenFusion } from 'pages/mini-app/tiktok-shelf/goods/constant';
import ColumnTips from './column-tips';

const { isBindTiktok } = _global;

export enum StatusType {
  unSubscribe = 0,
  unBind = 1,
  alBind = 2,
}

export enum BIND_STATUS {
  REMOVE_BIND = 0, // 解除授权（后端的特殊逻辑，前端不感知该状态）
  BIND = 1,
  FAILED = 2,
  NOTBIND = 3,
  BINDING = 4,
}

export const ChannelStatusMap = {
  [BIND_STATUS.BIND]: '授权成功',
  [BIND_STATUS.FAILED]: '授权失败',
  [BIND_STATUS.NOTBIND]: '待授权',
  [BIND_STATUS.BINDING]: '授权中',
};

export const getBatchTypeOptionsByChannelId = (channelId: number, channelType, canAuth) => {
  if (channelId === CHANNEL_ID.ELEME) {
    return batchActionTypeOptions.filter(item => item.key === BatchActionType.StockSetting);
  }
  if (channelType === CHANNEL_TYPE.TIKTOK_WAIMAITONG) {
    if (!canAuth) {
      return tiktokBatchActionTypeOptions.filter(item => item.key !== BatchActionType.batchAuth);
    }
    return tiktokBatchActionTypeOptions;
  }
  return batchActionTypeOptions;
};

export const renderSelect = (setting, channelId: number, channelType, canAuth) => {
  const items = getBatchTypeOptionsByChannelId(channelId, channelType, canAuth);
  if (items.length === 0) {
    return <></>;
  }
  return (
    <div className="select">
      <PopSelect
        label="批量设置"
        items={items}
        popoverClass="batch-release-popover"
        className={'batch-release'}
        onChange={setting}
      />
    </div>
  );
};

interface ITableProps {
  channelId: number;
  searchKdtId: number;
  channelType: number;
  channelStatus?: number;
  relatedChannel: string;
}

const getHasMeitunChannel = selectedList => {
  return selectedList.some(item =>
    [ChannelCode.meiTuan, ChannelCode.meiTuanShangou].includes(item.accountType),
  );
};

const save = async ({ key, selectedList, form, reloadAndRest }) => {
  const formValues = form.getValue();
  const hasMeituanChannel = getHasMeitunChannel(selectedList);
  if (key === BatchActionType.DyServeSetting) {
    await form.validate(ValidateOption.IncludeUntouched);

    if (!formValues.fileInfo?.fileUrl) {
      Notify.error('请上传文件');
      return false;
    }
    api
      .importUpdateChannelShopConfigs({
        ...formValues.fileInfo,
        channelId: CHANNEL_ID.TIKTOK_SHELF,
      })
      .then(() => {
        Notify.success('设置成功');
        reloadAndRest?.();
      });
    return;
  }
  /**
   * 前置校验
   */
  if (
    [
      BatchActionType.StoreOperatingStatus,
      BatchActionType.SynchronizationOfBusinessHours,
      BatchActionType.ShopStatusRelatedChannels,
    ].includes(key)
  ) {
    if (isNil(formValues[key])) {
      Notify.error('请完善批量设置内容');
      return false;
    }
  } else if (
    (isNil(formValues[StoreHandleConfig.stockRealTimeSync]) && hasMeituanChannel) ||
    isNil(formValues[StoreHandleConfig.StockInventoryRatio]) ||
    formValues[StoreHandleConfig.StockInventoryRatio] === '' ||
    isNil(formValues[StoreHandleConfig.PlanStockInventoryRatio]) ||
    formValues[StoreHandleConfig.PlanStockInventoryRatio] === ''
  ) {
    Notify.error('请完善批量设置内容');
    return false;
  }

  if (key === BatchActionType.ShopStatusRelatedChannels) {
    try {
      api.multiSetShopConfigs({
        shopConfigList: selectedList.map(item => {
          const shopStatusRelatedChannels = JSON.parse(item.shopStatusRelatedChannels);

          let newVal = item.shopStatusRelatedChannels;

          if (formValues[key] && !shopStatusRelatedChannels.includes('douyin')) {
            newVal = JSON.stringify([...shopStatusRelatedChannels, 'douyin']);
          }

          if (!formValues[key] && shopStatusRelatedChannels.includes('douyin')) {
            newVal = JSON.stringify(shopStatusRelatedChannels.filter(item => item !== 'douyin'));
          }

          return {
            kdtId: item.kdtId,
            key: 'shop_status_related_channels',
            value: newVal,
          };
        }),
      });
      Notify.success('设置成功');
      reloadAndRest();
    } catch (error) {}
    return;
  }

  const list = selectedList.map(item => {
    let need = {};
    if (key === BatchActionType.StoreOperatingStatus) {
      need = {
        operateStatus: Number(formValues[key]),
      };
    }
    if (key === BatchActionType.SynchronizationOfBusinessHours) {
      need = {
        syncBusinessHours: Number(formValues[key]),
      };
    }
    if (key === BatchActionType.StockSetting) {
      need = {
        extConfigs: {
          stockSynPercent: formValues[StoreHandleConfig.StockInventoryRatio],
          ...(item.accountType === ChannelCode.meiTuan
            ? {
                stockThreshold: formValues[StoreHandleConfig.stockRealTimeSync],
              }
            : null),
        },
      };
    }

    return {
      kdtId: item.kdtId,
      channelId: item.channelId,
      ...need,
    };
  }) as any;
  try {
    api.setStoreChannels(
      selectedList.map(item => {
        return {
          storeId: item.kdtId,
          channelId: item.channelId,
          stockAllocationPercent: formValues[StoreHandleConfig.StockInventoryRatio],
          planAllocationPercent:
            formValues[StoreHandleConfig.PlanStockInventoryRatio] ===
            PlanAllocationStatus.SameWithStock
              ? formValues[StoreHandleConfig.StockInventoryRatio]
              : formValues[StoreHandleConfig.PlanStockInventoryRatio],
        };
      }),
    );
    api.setStores(list);
    Notify.success('设置成功');
  } catch (error) {}
};

export const batchSetting = ({ key, selectedList, form, reloadAndRest }) => {
  // 导入不需要校验是否勾选
  if (key === BatchActionType.batchImport) {
    BatchImportDialog();
    return;
  }
  if (!checkSelected(selectedList)) {
    return;
  }
  if (key === BatchActionType.ShopStatusRelatedChannels) {
    if (selectedList.some(item => item.bindStatus !== BIND_STATUS.BIND)) {
      Notify.error('存在未授权成功的店铺，无法进行操作');
      return;
    }
  }
  if (key === BatchActionType.batchAuth) {
    if (
      selectedList.some(item => [BIND_STATUS.BIND, BIND_STATUS.BINDING].includes(item.bindStatus))
    ) {
      Notify.error('存在已授权/授权中的店铺，无法进行授权。请调整选择后，重新发起授权');
      return;
    }
    if (selectedList.length > 50) {
      Notify.error('批量授权一次最多可选50个店铺，请调整后进行批量授权');
      return;
    }
    BatchAuthDialog({
      selected: selectedList,
      reload: reloadAndRest,
      toImport: () => {
        BatchImportDialog();
      },
    });
    return;
  }
  const hasMeituanChannel = getHasMeitunChannel(selectedList);
  Sweetalert.confirm({
    title: '批量设置',
    content: (
      <>
        {[
          StoreHandleConfig.storeOperatingStatus,
          StoreHandleConfig.synchronizationOfBusinessHours,
        ].includes(key) && (
          <Alert type="warning" style={{ marginBottom: 15 }}>
            支持美团渠道设置，其他渠道不生效
          </Alert>
        )}
        {/* {[BatchActionType.StockSetting].includes(key) && (
          <Alert type="warning" style={{ marginBottom: 15 }}>
            不支持抖音点单渠道设置
          </Alert>
        )}
        {[BatchActionType.DyServeSetting].includes(key) && (
          <Alert type="warning" style={{ marginBottom: 15 }}>
            支持抖音渠道设置，其他渠道不生效
          </Alert>
        )} */}
        <HandleDialog hasMeituanChannel={hasMeituanChannel} type={key} form={form}></HandleDialog>
      </>
    ),
    closeBtn: true,
    onConfirm: () => save({ key, selectedList, form, reloadAndRest }),
    onCancel: () => {},
  });
};

const NO_BATCH_KEYS = [CHANNEL_TYPE.KAQUAN, CHANNEL_TYPE.COUPON, CHANNEL_TYPE.TABLE_BOOKING];

const Table = React.forwardRef<
  {
    reload: () => void;
  },
  ITableProps
>((props, ref) => {
  const {
    channelId: propsChannelId,
    searchKdtId,
    channelType,
    relatedChannel,
    channelStatus,
  } = props;
  const [selectedRowKeys, setSelectedRowKeys] = React.useState<string[]>([]);
  const [selectedList, setSelectedList] = React.useState<IList[]>([]);
  const history = useHistory();
  const [businessVal, setBusinessVal] = React.useState('');
  const [businessVisible, setBusinessVisible] = useState(false);
  const [isUpdateLoading, setIsUpdateLoading] = useState(false);
  const [canAuth, setCanAuth] = useState(false);
  const kdtIdRef = useRef('');

  /**
   * 抖音小程序
   */
  const isTiktok = channelType === CHANNEL_TYPE.TIKTOK_WAIMAITONG;

  const params = React.useMemo(() => {
    const searchParams: any = {
      channelId: propsChannelId,
      searchKdtId,
      channelType,
      channelStatus,
    };
    if (relatedChannel && relatedChannel !== 'all') {
      searchParams.relatedChannel = relatedChannel;
    }
    return searchParams;
  }, [relatedChannel, propsChannelId, searchKdtId, channelType, channelStatus]);

  React.useEffect(() => {
    setSelectedRowKeys([]);
    setSelectedList([]);
  }, [propsChannelId, searchKdtId, channelType]);

  React.useEffect(() => {
    if (isTiktok) {
      api
        .queryThirdChannelOverviewInfo({ channel: CHANNEL_ID.TIKTOK_SHELF })
        .then(channelOverviewInfo => {
          setCanAuth(channelOverviewInfo?.[0]?.status !== StatusType.unSubscribe);
        })
        .catch(() => {
          Notify.error('获取抖音小程序渠道详情失败');
        });
    }
  }, [isTiktok, propsChannelId]);

  const { items, tableProps, reloadAndRest } = useFetchTable<IList>(
    (params: any) => {
      if (params.channelType === CHANNEL_TYPE.TIKTOK_WAIMAITONG) {
        let tiktokParams = {
          ...params,
          channelType: 1,
          channelId: 401,
          status: params.channelStatus === 0 ? -1 : params.channelStatus,
        };
        tiktokParams = omit(tiktokParams, ['channelStatus']);
        return api.getWaiMaiTongList(tiktokParams);
      }
      params = omit(params, ['channelStatus']);
      if (params.channelType === CHANNEL_TYPE.WAIMAITONG) {
        return api.getWaiMaiTongList(params);
      }
      if (params.channelType === CHANNEL_TYPE.COUPON) {
        return api.getWaiMaiTongList({
          ...params,
          channelId: 401,
        });
      }
      return api.getBoundThirdChannelShops(params);
    },

    {
      params,
      noLoadingDuration: 0,
    },
  );

  React.useImperativeHandle(ref, () => {
    return {
      reload: reloadAndRest,
    };
  });

  const form = Form.useForm(FormStrategy.View);

  const handleUpdateBusinessNumber = () => {
    const kdtId = kdtIdRef.current;
    setIsUpdateLoading(true);
    api
      .updateBusinessNumber({
        kdtId,
        mchId: businessVal,
      })
      .then(() => {
        Notify.success('操作成功');
        setBusinessVal('');
        setBusinessVisible(false);
      })
      .catch(() => {
        Notify.error('操作失败');
      })
      .finally(() => {
        setIsUpdateLoading(false);
      });
  };

  const handleShowUpdateBusiness = ({ kdtId }) => {
    api
      .fetchBusinessNumber({
        businessType: channelType === CHANNEL_TYPE.COUPON ? 8 : 7,
        kdtId,
      })
      .then((res = {}) => {
        const { mchId } = res;
        setBusinessVal(mchId);
        kdtIdRef.current = kdtId;
        setBusinessVisible(true);
      })
      .catch(() => {
        Notify.error('获取商户号失败');
      });
  };

  const handleBusinessInputChange = e => {
    setBusinessVal(e.target.value);
  };

  const handleBusinessClose = () => {
    setBusinessVal('');
    kdtIdRef.current = '';
    setBusinessVisible(false);
  };

  const handleLoad = () => {
    reloadAndRest();
    setSelectedRowKeys([]);
    setSelectedList([]);
  };

  const handleAuth = item => {
    BatchAuthDialog({ selected: [item], reload: reloadAndRest });
  };

  // @ts-ignore
  const columns: Array<IGridColumn & { hidden?: boolean }> = [
    {
      title: '有赞店铺',
      name: 'shopName',
      width: 350,
    },
    {
      title: '渠道店铺',
      name: 'outerShopName',
      width: 200,
      bodyRender: data => {
        return data.outerShopName || '-';
      },
    },
    {
      title: 'POI id',
      name: 'outShopId',
      hidden: !isTiktok,
      bodyRender: data => {
        return data.outShopId || '-';
      },
    },
    {
      title: '渠道经营状态',
      name: 'shopStatusRelatedChannels',
      width: 150,
      bodyRender: ({ shopStatusRelatedChannels = '', bindStatus }) => {
        if (bindStatus === BIND_STATUS.BIND) {
          try {
            const channel = JSON.parse(shopStatusRelatedChannels);

            if (channel.includes('douyin')) {
              return '开启';
            }
            return '关闭';
          } catch (error) {
            return '关闭';
          }
        }
        return '-';
      },
      hidden: !isTiktok,
    },
    {
      title: '授权状态',
      width: 150,
      name: 'bindStatus',
      hidden: !isTiktok,
      bodyRender: ({ bindStatus, bindMsg }) => {
        return (
          <div>
            {get(ChannelStatusMap, bindStatus)}
            {bindStatus === BIND_STATUS.BIND && (
              <Icon type="check-circle" className="icon-success-msg" />
            )}
            {bindStatus === BIND_STATUS.FAILED && (
              <Pop trigger="hover" content={bindMsg}>
                <Icon type="info-circle" className="icon-warn-msg" />
              </Pop>
            )}
          </div>
        );
      },
    },
    {
      title: '销售渠道',
      width: 240,
      name: 'channelName',
      hidden: isTiktok,
    },
    {
      title: '授权时间',
      width: 180,
      name: 'bindTime',
      bodyRender: data => {
        return data.bindTime ? formatDate(new Date(data.bindTime), 'YYYY-MM-DD HH:mm') : '';
      },
    },
    {
      title: '接单时间',
      width: 160,
      bodyRender({ extConfigs }) {
        try {
          return (
            <PopEllipsisText
              width={160}
              style={{
                transform: 'translateY(1px)',
              }}
              text={getRenderTextByBeModel(JSON.parse(extConfigs.deliveryTimeConfig))}
            ></PopEllipsisText>
          );
        } catch (error) {
          return '-';
        }
      },
      hidden: !(isTiktok && IsOpenFusion),
    },
    {
      title: (
        <span>
          商户号
          <Pop trigger="hover" content="仅抖音渠道门店分账才需要" position="auto-top-right">
            <Icon type="help-circle" style={{ color: '#C8C9CC' }} />
          </Pop>
        </span>
      ),
      width: 180,
      name: 'mchId',
      bodyRender: data => {
        return data.mchId || '-';
      },
      hidden: !isTiktok,
    },
    {
      title: (
        <span>
          操作
          {channelType === CHANNEL_TYPE.KAQUAN && (<ColumnTips />)}
        </span>
      ),
      name: 'handle',
      width: 300,
      textAlign: 'right',
      fixed: 'right',
      bodyRender(data) {
        const {
          channelId,
          kdtId,
          accountType,
          onlineShopOpen,
          wmAbilityIsValid,
          bindStatus,
          shopStatusRelatedChannels,
        } = data;

        const isWaimaiValid = [
          ChannelCode.ele,
          ChannelCode.meiTuan,
          ChannelCode.meiTuanShangou,
        ].includes(accountType)
          ? wmAbilityIsValid
          : true;

        const setting = isWaimaiValid ? (
          <LinkButton
            onClick={() => {
              history.push(
                `/list/edit?kdtId=${kdtId}&channelId=${channelId}&&accountType=${accountType}`,
              );
            }}
          >
            渠道设置
          </LinkButton>
        ) : (
          <Pop
            trigger="hover"
            content={
              <p>
                插件到期，请先
                <BlankLink
                  href={`/v4/subscribe/appmarket/appdesc/board?id=${AppIdMap.get(accountType)}`}
                >
                  订购
                </BlankLink>
              </p>
            }
          >
            <LinkButton disabled>渠道设置</LinkButton>
          </Pop>
        );

        const unbind = (
          <UnBindBtn
            accountType={accountType}
            kdtId={kdtId}
            position="top-right"
            channelType={channelType}
            reloadAndRest={reloadAndRest}
            channelId={channelId}
          />
        );

        const manage = (
          <LinkButton
            onClick={() => {
              redirect('/v2/goods/external-channel#/');
            }}
          >
            商品管理
          </LinkButton>
        );

        const business = (
          <Business
            kdtId={kdtId}
            channelId={propsChannelId}
            channelType={channelType}
            shopStatusRelatedChannels={shopStatusRelatedChannels}
            reloadAndRest={reloadAndRest}
          />
        );

        const promotion = onlineShopOpen && channelId === CHANNEL_ID.TIKTOK && <Promotion />;

        let items = [manage, setting, unbind];

        if (channelType === CHANNEL_TYPE.KAQUAN) {
          items = [promotion, unbind];
          // 小红书渠道不支持推广和解除绑定
          if (channelId === CHANNEL_ID.XIAOHONGSHU) {
            items = [];
          }
        }
        // 小程序渠道先不展示操作
        if (channelId === CHANNEL_ID.TIKTOK_SHELF) {
          items = [];
        }
        if (isTiktok && channelId === CHANNEL_ID.TIKTOK_SHELF) {
          if (
            (isHqStore || isSingleStore) &&
            [BIND_STATUS.NOTBIND, BIND_STATUS.FAILED].includes(bindStatus)
          ) {
            if (canAuth) {
              items.push(
                <>
                  <LinkButton onClick={() => handleAuth(data)}>
                    {bindStatus === BIND_STATUS.FAILED ? '重新授权' : '授权'}
                  </LinkButton>
                </>,
              );
            } else {
              items.push(
                <Pop trigger="hover" content="需订购抖音小程序" position="auto-top-right">
                  <LinkButton disabled>授权</LinkButton>
                </Pop>,
              );
            }
          }
          // 已授权才展示商户号
          if (bindStatus === BIND_STATUS.BIND) {
            items.push(
              business,
              <>
                <LinkButton onClick={() => handleShowUpdateBusiness(data)}>商户号</LinkButton>
              </>,
            );
            if (IsOpenFusion) {
              items.push(
                <>
                  <LinkButton
                    onClick={() =>
                      openTikTokServeSettingDialog({
                        data,
                        reload: reloadAndRest,
                      })
                    }
                  >
                    接单设置
                  </LinkButton>
                </>,
              );
            }
          }
        }
        if (channelId === CHANNEL_ID.MEITUAN_TABLE_BOOKING) {
          items = [unbind];
        }
        return <Divider items={items.filter(node => Boolean(node))} />;
      },
    },
  ].filter(({ hidden }) => !hidden);

  const showColumns = columns.filter(item => !item?.hidden);

  return (
    <>
      <Grid<IList>
        {...tableProps}
        columns={showColumns}
        emptyLabel={
          isTiktok && !isBindTiktok
            ? '未授权抖音小程序，请先完成抖音小程序授权流程。'
            : '没有更多数据了'
        }
        datasets={isTiktok && !isBindTiktok ? [] : items}
        selection={
          NO_BATCH_KEYS.includes(channelType)
            ? undefined
            : {
                selectedRowKeys,
                onSelect: (selectedRowKeys, selectedRows) => {
                  setSelectedRowKeys(selectedRowKeys);
                  const allList = selectedList.concat(selectedRows);
                  const newItems = uniqBy(allList, 'id').filter((item: any) =>
                    selectedRowKeys.includes(item?.id),
                  );
                  setSelectedList(newItems);
                },
                getCheckboxProps: ({ accountType, wmAbilityIsValid }) => ({
                  disabled: [
                    ChannelCode.ele,
                    ChannelCode.meiTuan,
                    ChannelCode.meiTuanShangou,
                  ].includes(accountType)
                    ? !wmAbilityIsValid
                    : false,
                }),
              }
        }
        batchRender={
          NO_BATCH_KEYS.includes(channelType) || (isTiktok && !isHqStore)
            ? undefined
            : data => (
                <TableBatchCpn
                  keys={[]}
                  onSelect={() => {}}
                  items={[
                    renderSelect(
                      key => batchSetting({ key, selectedList, form, reloadAndRest: handleLoad }),
                      propsChannelId,
                      channelType,
                      canAuth,
                    ),
                  ]}
                  selected={data}
                />
              )
        }
        scroll={{ x: columns.reduce((sum, { width }: any) => sum + (width || 150), 0) }}
      />
      <Dialog
        title="商户号"
        visible={businessVisible}
        onClose={handleBusinessClose}
        footer={
          <>
            <Button onClick={handleBusinessClose}>关闭</Button>
            <Button
              loading={isUpdateLoading}
              type="primary"
              onClick={() => handleUpdateBusinessNumber()}
            >
              确定
            </Button>
          </>
        }
      >
        <Input
          value={businessVal}
          onChange={handleBusinessInputChange}
          placeholder="请输入商户号"
        />
      </Dialog>
    </>
  );
});

export default Table;
