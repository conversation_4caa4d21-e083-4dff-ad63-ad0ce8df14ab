import { Router, Index, API, Inject } from '@youzan/assets-route-plugin';

import CommonBasicInfoController from '../common/BasicInfoController';
import XiaohongshuChannelConsoleService from '../../../services/api/ebiz/video/channels/item/xhs/XiaohongshuChannelConsoleService';
import KsAppletLiveItemService from '../../../services/api/ebiz/video/channels/ks/KsAppletLiveItemService';
import ShopConfigReadService from '../../../services/api/shopcenter/shopconfig/ShopConfigReadService';

const fromApp = 'wsc-pc-channel';
@Router('ks/live-goods')
class GoodsController extends CommonBasicInfoController {
  @Inject()
  private readonly XiaohongshuChannelConsoleService!: XiaohongshuChannelConsoleService;

  @Inject()
  private readonly KsAppletLiveItemService!: KsAppletLiveItemService;

  @Inject()
  private readonly ShopConfigReadService!: ShopConfigReadService;

  public async init() {
    this.channelName = 'ks';
    super.init();
  }

  @Index('')
  public async goodsSortHtml() {
    await this.checkIsBind();
    const { ctx } = this;
    ctx.setState('subtitle', '直播商品管理');
    return ctx.render('mini-app/ks/live-goods/index.html');
  }

  @API('GET', 'get-goods-list.json')
  public async getGoodsList() {
    const { ctx } = this;
    const { kdtId, userId } = ctx;
    const { pageSize, page } = ctx.request.query;
    const res = await this.KsAppletLiveItemService.listOfPage({
      kdtId,
      fromApp,
      pageSize,
      page,
      operatorId: userId,
    });
    return ctx.json(0, 'ok', res);
  }

  @API('POST', 'set-goods-list.json')
  public async setGoodsList() {
    const { ctx } = this;
    const {
      kdtId,
      request: {
        body: { items, itemId, fromSeqNo, toSeqNo },
      },
      userId,
    } = ctx;
    const query: any = {
      kdtId,
      fromApp,
      operatorId: userId,
      items,
      itemId,
      fromSeqNo,
      toSeqNo,
    };
    const res = await this.KsAppletLiveItemService.batchUpdateSeqNo(query);
    return ctx.json(0, 'ok', res);
  }

  @API('GET', 'get-ks-live-goods-list.json')
  public async getKsGoodsList() {
    const { ctx } = this;
    const { kdtId, request, userId } = ctx;
    const { pageSize, page, title } = request.query;
    const res = await this.KsAppletLiveItemService.listPageOfSelector({
      kdtId,
      fromApp,
      operatorId: userId,
      channel: 3,
      pageSize,
      page,
      keyword: title,
    });

    return ctx.json(0, 'ok', res);
  }

  @API('POST', 'delete-goods.json')
  public async deleteGoods() {
    const { ctx } = this;
    const {
      kdtId,
      userId,
      request: {
        body: { itemId },
      },
    } = ctx;
    const query: any = {
      kdtId,
      itemId,
      operatorId: userId,
      fromApp,
    };
    const res = await this.KsAppletLiveItemService.delete(query);
    return ctx.json(0, 'ok', res);
  }

  @API('POST', 'speech-goods.json')
  public async speechGoods() {
    const { ctx } = this;
    const {
      kdtId,
      userId,
      request: {
        body: { itemId },
      },
    } = ctx;
    const query: any = {
      kdtId,
      itemId,
      operatorId: userId,
      fromApp,
    };
    const res = await this.KsAppletLiveItemService.speech(query);
    return ctx.json(0, 'ok', res);
  }

  @API('POST', 'unSpeech-goods.json')
  public async unSpeechGoods() {
    const { ctx } = this;
    const {
      kdtId,
      userId,
      request: {
        body: { itemId },
      },
    } = ctx;
    const query: any = {
      kdtId,
      itemId,
      operatorId: userId,
      fromApp,
    };
    const res = await this.KsAppletLiveItemService.unSpeech(query);
    return ctx.json(0, 'ok', res);
  }

  @API('POST', 'batch-select-goods.json')
  public async batchSelect() {
    const { ctx } = this;
    const {
      kdtId,
      userId,
      request: {
        body: { itemIds },
      },
    } = ctx;
    const query: any = {
      kdtId,
      itemIds,
      operatorId: userId,
      fromApp,
    };
    const res = await this.KsAppletLiveItemService.batchSelect(query);
    return ctx.json(0, 'ok', res);
  }

  @API('GET', 'get-shop-tags.json')
  public async getShopTags() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const {
      keys = [
        'principal_cert_type',
        'brand_cert_type',
        'shop_operate_duration_years',
        'shop_operate_duration_tag_switch',
      ],
    } = ctx.request.query;
    const res = await this.ShopConfigReadService.queryShopConfigs(kdtId, keys);
    return ctx.json(0, 'ok', res);
  }
}

export = GoodsController;
