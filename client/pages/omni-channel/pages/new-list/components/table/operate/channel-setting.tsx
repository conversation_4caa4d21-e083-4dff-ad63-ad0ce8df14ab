import * as React from 'react';
import { useHistory } from 'react-router-dom';
import { Pop } from 'zent';
import { ChannelCode } from 'pages/omni-channel/config.type';
import { LinkButton, BlankLink } from '@youzan/react-components';
import { AppIdMap } from 'pages/omni-channel/constant';

const ChannelSetting: React.FC<{
  accountType: number;
  wmAbilityIsValid: boolean;
  kdtId: number;
  channelId: number;
}> = ({ accountType, wmAbilityIsValid, kdtId, channelId }) => {
  const history = useHistory();
  const isWaimaiValid = [ChannelCode.ele, ChannelCode.meiTuan, ChannelCode.meiTuanShangou].includes(
    accountType,
  )
    ? wmAbilityIsValid
    : true;

  if (isWaimaiValid) {
    return (
      <LinkButton
        onClick={() => {
          history.push(
            `/list/edit?kdtId=${kdtId}&channelId=${channelId}&&accountType=${accountType}`,
          );
        }}
      >
        渠道设置
      </LinkButton>
    );
  }
  return (
    <Pop
      trigger="hover"
      content={
        <p>
          插件到期，请先
          <BlankLink href={`/v4/subscribe/appmarket/appdesc/board?id=${AppIdMap.get(accountType)}`}>
            订购
          </BlankLink>
        </p>
      }
    >
      <LinkButton disabled>渠道设置</LinkButton>
    </Pop>
  );
};

export default ChannelSetting;