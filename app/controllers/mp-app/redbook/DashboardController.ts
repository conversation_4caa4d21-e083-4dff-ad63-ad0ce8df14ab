import { Router, Index, API, Metadata } from '@youzan/assets-route-plugin';
import BaseController from '../BaseController';

@Router('redbook')
class DashboardController extends BaseController {
  public async init() {
    this.channelName = 'redbook';
    super.init();
  }

  /**
   * 概况页
   */
  @Index(['dashboard'])
  @Metadata('INDEX')
  async getIndexHtml() {
    const { ctx } = this;
    ctx.setState('subtitle', '概况');

    const isBind = await this.getBindInfo();
    ctx.setGlobal('isBind', isBind);
    await ctx.render('mp-app/redbook/dashboard.html');
  }

  /**
   * 获取apollo公告配置
   */
  @API('GET', 'apollo-announcement.json')
  public async getApolloAnnouncementJson() {
    const result = await this.getApolloAnnouncement();
    await this.ctx.json(0, 'ok', result);
  }
}

export = DashboardController;
