import { Router, API } from '@youzan/assets-route-plugin';
import BaseController from '../BaseController';

/**
 * 通用授权
 */
@Router(':channelName/auth')
class AuthController extends BaseController {
  /**
   * 获取公众号绑定情况
   */
  @API('GET', 'bind-info.json')
  public async queryMpBindInfoByKdtId() {
    const result = await this.getBindInfo();
    await this.ctx.json(0, 'success', result);
  }
}

export = AuthController;
