import { Router, API, Inject } from '@youzan/assets-route-plugin';
import CommonBasicInfoController from '../common/AuditController';
import AlipayAccountService from '../../../services/api/channels/channel/core/AlipayAccountService';
import lodash from 'lodash';

@Router('alipay/audit')
class BasicInfoController extends CommonBasicInfoController {
  @Inject()
  private readonly alipayAccountService!: AlipayAccountService;

  public async init() {
    this.channelName = 'alipay';
    super.init();
  }

  /**
   * 获取支付宝待提审账号信息
   */
  @API('GET', 'get-account-info.json')
  public async getAccountInfoJson() {
    const { ctx } = this;
    const { businessType = 1 } = this.ctx.query;
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');
    const accountInfo =
      (await this.alipayAccountService.getAlipayAuditAccountInfo({
        kdtId: ctx.kdtId,
        accountType,
        businessType,
      })) || {};
    await this.ctx.json(0, 'ok', {
      ...accountInfo,
      outDoorPicUrl: accountInfo.outDoorPicUrl ? [accountInfo.outDoorPicUrl] : [],
    });
  }

  /**
   * 保存支付宝待提审账号信息
   */
  @API('POST', 'save-account-info.json')
  public async postAccountInfoJson() {
    const { ctx } = this;
    const { businessType = 1, outDoorPicUrl = '' } = ctx.request.body;
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');
    const postData = lodash.pick(ctx.request.body, [
      'appLogo',
      'appDesc',
      'appSlogan',
      'licenseNo',
      'licensePicUrl',
      'licenseValidDate',
      'licenseName',
      'miniCategoryIds',
      'specialLicensePicUrl',
      'servicePhone',
    ]);
    // @ts-ignore
    const accountInfo = await this.alipayAccountService.saveAlipayAuditAccountInfo({
      ...postData,
      outDoorPicUrl: Array.isArray(outDoorPicUrl) ? outDoorPicUrl[0] || '' : '',
      accountType,
      businessType,
      kdtId: ctx.kdtId,
    });
    await this.ctx.json(0, 'ok', accountInfo);
  }
}

export = BasicInfoController;
