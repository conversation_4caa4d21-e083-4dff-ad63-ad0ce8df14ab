import { Index, Router } from '@youzan/assets-route-plugin';
import BaseController from '../BaseController';

/**
 * 前置条件验证
 */
@Router('wechat-video/premise-check')
class PremiseCheckController extends BaseController {
  public async init() {
    this.channelName = 'wechat-video';
    super.init();
  }

  @Index('')
  async getIndexHtml() {
    const { ctx } = this;
    ctx.setState('subtitle', '视频号');
    await ctx.render('video-app/wechat-video/premise-check/index.html');
  }
}

export = PremiseCheckController;
