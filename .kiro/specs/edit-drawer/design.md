# 设计文档

## 概述

edit-drawer 组件将是一个抽屉式覆盖层，用于替代当前渠道设置的页面导航。它将重用编辑页面（`client/pages/omni-channel/pages/edit/index.tsx`）中现有的表单逻辑、组件和模式，同时以抽屉界面的形式呈现。

**注意：** 抽屉组件只包含需求中明确定义的**三个核心模块**：
1. **店铺模块** - 店铺经营状态和营业时间同步
2. **商品同步规则模块** - 更新范围、销售信息、基础信息、价格信息、库存同步规则、自动发布设置、总部更新规则
3. **外卖开票设置模块** - 外卖订单开票设置

另外还包含一个条件显示的辅助模块：
- **库存共享比例模块** - 仅在非多渠道环境下显示

## 架构

### 组件结构
```
EditDrawer (使用 Zent Drawer 组件)
├── Drawer.Header (标题，关闭按钮)
├── Drawer.Body (带有 FormContext 的表单)
│   ├── 店铺模块 (重用 StoreOperatingStatus, SynchronizationOfBusinessHours)
│   ├── 商品同步规则模块 
│   │   ├── 更新范围字段 (ItemSyncRangeField)
│   │   ├── 销售信息部分 (ProductSyncRulesField - 销售信息)
│   │   ├── 基础信息部分 (ProductSyncRulesField - 基础信息，条件显示)
│   │   ├── 价格信息部分 (ProductSyncRulesField - 价格信息，条件显示)
│   │   ├── 库存同步规则 (StockSyncRulesField，条件显示)
│   │   ├── 自动发布设置 (AutoPublishSyncField)
│   │   └── 总部更新规则 (GoodsUpdateRuleField，条件显示)
│   ├── 库存共享比例模块 (重用 InventorySynchronizationRatio，条件显示)
│   └── 外卖开票设置模块 (重用 OrderInvoiceSwitchField)
└── Drawer.Footer (保存/取消按钮)
```

### 集成点
- **父组件**: 抽屉将从全渠道列表页面触发
- **表单管理**: 将使用 `Form.useForm(FormStrategy.View)`，与编辑页面完全相同
- **API 层**: 将重用来自 `./api` 的现有 API 函数（与编辑页面相同）
- **状态管理**: 本地组件状态与表单验证，遵循编辑页面模式
- **模式控制**: 根据 mode 属性控制表单字段的可编辑性和按钮显示
- **跳转替换**: 替换现有的页面跳转逻辑为抽屉打开逻辑

## 组件和接口

### 主组件接口
```typescript
interface EditDrawerProps {
  visible: boolean;
  onClose: () => void;
  kdtId: string | number;
  channelId: string | number;
  accountType: string | number;
  onSuccess?: () => void;
  mode?: 'edit' | 'preview'; // 新增：支持编辑和预览模式
}
```

### 必要的导入和常量
```typescript
import React, { useEffect, useState } from 'react';
import {
  BasicBuilder,
  BasicModel,
  BlockHeader,
  BlockLoading,
  Button,
  Form,
  FormContext,
  FormStrategy,
  Notify,
  Drawer,
  FormRadioGroupField,
  FormCheckboxGroupField,
  Radio,
  Checkbox,
  Pop,
  Icon,
  Validators,
} from 'zent';

import { isRetailSingleStore, SALE_CHANNEL } from '@youzan/utils-shop';
import { find, mapValues, omit } from 'lodash';
import queryString from 'query-string';

import {
  GoodsUpdateRuleField,
  InventorySynchronizationRatio,
  OrderInvoiceSwitchField,
  StockRealTimeSync,
  StoreOperatingStatus,
  SynchronizationOfBusinessHours,
  YouzanGoodsUpdateRuleField,
} from '../../component/handle-dialog';

import {
  AllowOpenInvoiceStatus,
  EsynchronizationOfBusinessHours,
  GoodsUpdateRuleItem,
  StoreHandleConfig,
  StoreHandleItems,
  YouzanGoodsUpdateRuleItem,
  EStoreOperatingStatus,
} from '../../config';

import { ChannelCode } from '../../config.type';
import { PlanAllocationStatus, PlanStockOptions } from '../../constant';
import api from './api';

const { isSupportMultiChannel } = _global;
```

### 商品同步规则的新枚举
遵循 `config.ts` 中的现有模式，我们需要添加新的枚举：

```typescript
// 添加到 StoreHandleConfig
export const StoreHandleConfig = {
  // ... 现有字段
  itemSyncRange: 'itemSyncRange',
  autoPublishSync: 'autoPublishSync',
};

// 添加到 StoreHandleItems
export const StoreHandleItems = {
  // ... 现有项目
  [StoreHandleConfig.itemSyncRange]: '更新范围',
  [StoreHandleConfig.autoPublishSync]: '商品库发布外卖商品时',
};

// 新字段的新枚举
export enum EItemSyncRange {
  all = '0',           // 全部信息
  partial = '1',       // 指定部分信息
  bindOnly = '2',      // 仅绑定商品，不更新信息
}

export enum EAutoPublishSync {
  manual = '0',        // 手动同步创建到外卖平台
  auto = '1',          // 自动发布创建到外卖平台
}

// 扩展现有的 YouzanGoodsUpdateRuleItem 枚举
export enum YouzanGoodsUpdateRuleItem {
  // ... 现有项目
  // 为抽屉添加新项目
  barcode = 'barcode',
  skuName = 'skuName',
  takeUpOrDown = 'takeUpOrDown',
}
```

### 表单数据结构
基于编辑页面，表单将使用相同的字段名称和结构：
```typescript
interface EditDrawerFormData {
  // 店铺模块（重用编辑页面的现有字段）
  [StoreHandleConfig.storeOperatingStatus]: EStoreOperatingStatus;
  [StoreHandleConfig.synchronizationOfBusinessHours]: EsynchronizationOfBusinessHours;
  
  // 商品同步规则模块（使用枚举的新字段）
  [StoreHandleConfig.itemSyncRange]: EItemSyncRange;
  [StoreHandleConfig.autoPublishSync]: EAutoPublishSync;
  
  // 重用编辑页面的现有同步字段
  [StoreHandleConfig.stockRealTimeSync]: string; // '15' | '50'
  [StoreHandleConfig.OrderInvoiceSwitch]: AllowOpenInvoiceStatus;
  [StoreHandleConfig.HqTemplateInfoSync]: string[]; // 总部更新规则
  [StoreHandleConfig.OutItemInfoSync]: string[]; // 商品同步复选框（包含新字段）
  
  // 库存共享比例字段
  [StoreHandleConfig.StockInventoryRatio]: number; // 实物可售库存比例 1-100
  [StoreHandleConfig.PlanStockInventoryRatio]: PlanAllocationStatus | { key: PlanAllocationStatus; text: string }; // 计划可售库存
}
```

### 从编辑页面重用的组件
抽屉将重用 `component/handle-dialog/index.tsx` 中的这些现有组件：
- `StoreOperatingStatus` - 店铺经营状态
- `SynchronizationOfBusinessHours` - 营业时间同步  
- `InventorySynchronizationRatio` - 库存共享比例（包含实物可售库存和计划可售库存）
- `GoodsUpdateRuleField` - 总部更新规则（条件显示）
- `OrderInvoiceSwitchField` - 外卖开票设置

### 基于需求的新组件

#### ItemSyncRangeField
遵循编辑页面中现有单选字段的模式：
```typescript
const ItemSyncRangeField = ({ disabled = false }) => (
  <FormRadioGroupField
    name={StoreHandleConfig.itemSyncRange}
    label={`${StoreHandleItems[StoreHandleConfig.itemSyncRange]}：`}
    required
    helpDesc={<p className="tip">更新范围仅对已经绑定的外卖平台商品生效</p>}
    props={{ disabled }}
  >
    <Radio value={EItemSyncRange.partial}>指定部分信息</Radio>
    <Radio value={EItemSyncRange.all}>全部信息</Radio>
    <Radio value={EItemSyncRange.bindOnly}>仅绑定商品，不更新信息</Radio>
  </FormRadioGroupField>
);
```

#### 字段定义常量
```typescript
// 提取字段定义，避免重复
const BASIC_INFO_FIELDS = [
  YouzanGoodsUpdateRuleItem.title,
  YouzanGoodsUpdateRuleItem.barcode,
  YouzanGoodsUpdateRuleItem.skuName,
  YouzanGoodsUpdateRuleItem.category,
  YouzanGoodsUpdateRuleItem.prop,
  YouzanGoodsUpdateRuleItem.picture,
  YouzanGoodsUpdateRuleItem.group,
  YouzanGoodsUpdateRuleItem.sellPoint,
  YouzanGoodsUpdateRuleItem.minimumPurchaseNum,
];

const PRICE_INFO_FIELDS = [
  YouzanGoodsUpdateRuleItem.price,
  YouzanGoodsUpdateRuleItem.packingCharge,
];

const ALL_BASIC_AND_PRICE_FIELDS = [...BASIC_INFO_FIELDS, ...PRICE_INFO_FIELDS];
```

#### ProductSyncRulesField
基于 `YouzanGoodsUpdateRuleField` 模式，但具有自定义结构并使用适当的枚举，包含条件显示逻辑和自动切换逻辑：
```typescript
const ProductSyncRulesField = ({ isMeiTuanShangou, isMeiTuan, disabled = false }) => {
  const form = Form.useFormContext();
  const formValue = form.getValue();
  const itemSyncRange = formValue[StoreHandleConfig.itemSyncRange];
  const outItemInfoSync = formValue[StoreHandleConfig.OutItemInfoSync] || [];
  
  // 检查基础信息和价格信息是否有选中的字段
  const hasBasicInfoSelected = BASIC_INFO_FIELDS.some(field => outItemInfoSync.includes(field));
  const hasPriceInfoSelected = PRICE_INFO_FIELDS.some(field => outItemInfoSync.includes(field));
  const hasBasicOrPriceSelected = hasBasicInfoSelected || hasPriceInfoSelected;
  
  // 自动切换逻辑：当全部信息模式下，基础信息和价格信息都没选中时，自动切换到指定部分信息
  React.useEffect(() => {
    const isAllInfoMode = itemSyncRange === EItemSyncRange.all;
    if (isAllInfoMode && !hasBasicOrPriceSelected && !disabled) {
      form.patchValue({
        [StoreHandleConfig.itemSyncRange]: EItemSyncRange.partial,
      });
    }
  }, [itemSyncRange, hasBasicOrPriceSelected, disabled, form]);
  
  // 根据更新范围决定显示哪些部分
  const isAllInfoMode = itemSyncRange === EItemSyncRange.all;
  const showSalesInfo = itemSyncRange !== EItemSyncRange.bindOnly;
  const showBasicInfo = isAllInfoMode;
  const showPriceInfo = isAllInfoMode;
  
  // 验证提示：全部信息模式下，基础信息和价格信息至少选中一个
  const showValidationError = isAllInfoMode && !hasBasicOrPriceSelected;
  
  return (
    <div>
      <FormCheckboxGroupField
        name={StoreHandleConfig.OutItemInfoSync}
        className="allow-open-invoice-label"
        props={{ disabled }}
      >
      {/* 销售信息部分 - 当不是"仅绑定商品"时显示 */}
      {showSalesInfo && (
        <div>
          <div className='custom-label'>销售信息：</div>
          <Checkbox value={YouzanGoodsUpdateRuleItem.takeUpOrDown}>销售状态（上下架）</Checkbox>
          <Checkbox value={YouzanGoodsUpdateRuleItem.stockNum}>库存</Checkbox>
          <div className="tip" style={{ color: '#999', fontSize: '12px', marginTop: '4px' }}>
            仅饿了么外卖和京东外卖渠道，支持同步销售状态
          </div>
        </div>
      )}
      
      {/* 基础信息部分 - 仅当选择"全部信息"时显示 */}
      {showBasicInfo && (
        <div>
          <div className='custom-label'>基础信息：</div>
          <Checkbox value={YouzanGoodsUpdateRuleItem.title}>商品名称</Checkbox>
          <Checkbox value={YouzanGoodsUpdateRuleItem.skuName}>规格名称</Checkbox>
          <Checkbox value={YouzanGoodsUpdateRuleItem.barcode}>商品/规格条码</Checkbox>
          <Checkbox value={YouzanGoodsUpdateRuleItem.category}>商品类目</Checkbox>
          <Checkbox value={YouzanGoodsUpdateRuleItem.prop}>属性{isMeiTuan ? '（不含加价属性）' : ''}
            <Pop trigger="hover" content={isMeiTuan ? "不支持小料自定义，有赞加价属性=美团外卖小料" : '不含加价属性，只对免费属性起作用'}>
              <Icon
                style={{
                  color: '#cacaca',
                  transform: 'translate(0px, -4px)',
                }}
                type="help-circle"
              />
            </Pop>
          </Checkbox>
          <Checkbox value={YouzanGoodsUpdateRuleItem.picture}>商品主图</Checkbox>
          <Checkbox value={YouzanGoodsUpdateRuleItem.group}>商品分组</Checkbox>
          <Pop trigger="hover" content="商品与分组的关联关系">
            <Icon
              style={{
                color: '#cacaca',
                transform: 'translate(-22px, -4px)',
              }}
              type="help-circle"
            />
          </Pop>
          <Checkbox value={YouzanGoodsUpdateRuleItem.sellPoint}>商品描述</Checkbox>
          <Checkbox value={YouzanGoodsUpdateRuleItem.minimumPurchaseNum}>最小起购量</Checkbox>
        </div>
      )}
      
      {/* 价格信息部分 - 仅当选择"全部信息"时显示 */}
      {showPriceInfo && (
        <div>
          <div className='custom-label'>价格信息：</div>
          <Checkbox value={YouzanGoodsUpdateRuleItem.price}>价格</Checkbox>
          {!isMeiTuanShangou && (
            <Checkbox value={YouzanGoodsUpdateRuleItem.packingCharge}>商品打包费</Checkbox>
          )}
        </div>
      )}
      </FormCheckboxGroupField>
      
      {/* 验证错误提示 */}
      {showValidationError && (
        <div style={{ color: '#f5222d', fontSize: '12px', marginTop: '8px' }}>
          基础信息和价格信息至少选中一个选项
        </div>
      )}
    </div>
  );
};
```

#### StockSyncRulesField
现有 `StockRealTimeSync` 的修改版本，带有更新的文本和条件显示逻辑：
```typescript
const StockSyncRulesField = ({ disabled = false }) => {
  const form = Form.useFormContext();
  const formValue = form.getValue();
  const outItemInfoSync = formValue[StoreHandleConfig.OutItemInfoSync] || [];
  const stockNumSelected = outItemInfoSync.includes(YouzanGoodsUpdateRuleItem.stockNum);
  const itemSyncRange = formValue[StoreHandleConfig.itemSyncRange];
  
  // 显示条件：
  // 1. 必须选中商品库存复选框
  // 2. 更新范围不能是"仅绑定商品，不更新信息"（因为此时销售信息部分被隐藏）
  const shouldShow = stockNumSelected && itemSyncRange !== EItemSyncRange.bindOnly;
  
  if (!shouldShow) return null;
  
  return (
    <FormRadioGroupField
      name={StoreHandleConfig.stockRealTimeSync}
      label="库存同步规则："
      required
      props={{ disabled }}
    >
      <Radio value="15">
        当商品增加库存，或可售库存≤15个时，实时同步
        <div className="tip" style={{ color: '#999', fontSize: '12px' }}>
          适合日库存固定/有限商家（如面包/商超便利等业态）
        </div>
      </Radio>
      <Radio value="50">
        当商品增加库存，或可售库存≤50个时，实时同步
        <div className="tip" style={{ color: '#999', fontSize: '12px' }}>
          适合日库存充足/无限商家（如正餐西餐、咖啡茶饮、仅生日蛋糕等业态）
        </div>
      </Radio>
    </FormRadioGroupField>
  );
};
```

#### AutoPublishSyncField
遵循与其他单选字段相同的模式：
```typescript
const AutoPublishSyncField = ({ disabled = false }) => (
  <FormRadioGroupField
    name={StoreHandleConfig.autoPublishSync}
    label={`${StoreHandleItems[StoreHandleConfig.autoPublishSync]}：`}
    required
    props={{ disabled }}
  >
    <Radio value={EAutoPublishSync.auto}>自动发布创建到外卖平台</Radio>
    <Radio value={EAutoPublishSync.manual}>手动同步创建到外卖平台</Radio>
  </FormRadioGroupField>
);
```

### 预览模式实现

#### 模式控制逻辑
```typescript
const EditDrawer: React.FC<EditDrawerProps> = ({ 
  visible, 
  onClose, 
  kdtId, 
  channelId, 
  accountType, 
  onSuccess,
  mode = 'edit' 
}) => {
  const isPreviewMode = mode === 'preview';
  const [loading, setLoading] = useState(false);
  
  // 表单配置
  const form = Form.useForm(FormStrategy.View);
  
  // 在预览模式下禁用所有表单字段
  const fieldProps = {
    disabled: isPreviewMode || loading,
  };
  
  // 重用编辑页面的条件显示逻辑
  const showFormElement = [
    ChannelCode.meiTuan,
    ChannelCode.meiTuanShangou,
    ChannelCode.jdTakeout,
  ].includes(Number(accountType));

  const canShowSetting = Number(channelId) !== SALE_CHANNEL.E_LE_ME || !isSupportMultiChannel;
  
  const isMeiTuanShangou = Number(accountType) === ChannelCode.meiTuanShangou;
  const isMeiTuan = Number(accountType) === ChannelCode.meiTuan;
  const isJdTakeout = Number(accountType) === ChannelCode.jdTakeout;
  
  // 数据加载
  useEffect(() => {
    if (visible) {
      getStoreProperty();
    }
  }, [visible, kdtId, channelId]);
  
  // 特殊处理逻辑
  const handleStoreOperatingStateChange = (value) => {
    if (isJdTakeout && value === EStoreOperatingStatus.off) {
      const { openDialog, closeDialog } = Dialog;
      const id = 'jd_dialog';
      openDialog({
        dialogId: id,
        title: '提示',
        children: <div>修改营业时间会影响所有京东分店</div>,
        footer: (
          <>
            <Button
              onClick={() => {
                form.patchValue({
                  [StoreHandleConfig.storeOperatingStatus]: EStoreOperatingStatus.on,
                });
                closeDialog(id);
              }}
            >
              取消
            </Button>
            <Button type="primary" onClick={() => closeDialog(id)}>
              确定
            </Button>
          </>
        ),
      });
    }
  };
  
  const handleSubmit = React.useCallback(() => form.submit(), [form]);
  
  return (
    <Drawer
      visible={visible}
      onClose={onClose}
      title={isPreviewMode ? '查看渠道设置' : '编辑渠道设置'}
      placement="right"
      width={800}
    >
      <Drawer.Body>
        <BlockLoading loading={loading}>
          <Form layout="horizontal" form={form} onSubmit={onSubmit}>
            <FormContext.Provider value={{ labelStyle: { flexBasis: 115 } }}>
            {/* 店铺模块 - 遵循编辑页面的条件显示逻辑 */}
            {canShowSetting && showFormElement && (
              <>
                <BlockHeader type="minimum" title="店铺" />
                <div style={{ paddingLeft: '12px' }}>
                  <StoreOperatingStatus 
                    {...fieldProps} 
                    onChange={!isPreviewMode ? handleStoreOperatingStateChange : undefined}
                  />
                  <SynchronizationOfBusinessHours
                    {...fieldProps}
                    disabled={
                      isPreviewMode ||
                      (isMeiTuanShangou &&
                        form.getValue()[StoreHandleConfig.synchronizationOfBusinessHours] ===
                          EsynchronizationOfBusinessHours.off)
                    }
                  />
                </div>
              </>
            )}
            
            {/* 商品同步规则模块 */}
            <BlockHeader type="minimum" title="商品同步规则" />
            <div style={{ paddingLeft: '12px' }}>
              <ItemSyncRangeField {...fieldProps} />
              <ProductSyncRulesField {...fieldProps} isMeiTuanShangou={isMeiTuanShangou} isMeiTuan={isMeiTuan} />
              <StockSyncRulesField {...fieldProps} />
              <AutoPublishSyncField {...fieldProps} />
              
              {/* 总部更新规则 - 作为商品同步规则的最后一个字段 */}
              {!(isJdTakeout && isRetailSingleStore) && (
                <GoodsUpdateRuleField 
                  {...fieldProps} 
                  isMeiTuanShangou={isMeiTuanShangou} 
                  isJdTakeout={isJdTakeout} 
                />
              )}
            </div>
            
            {/* 库存共享比例模块 - 仅在非多渠道时显示 */}
            {canShowSetting && !isSupportMultiChannel && (
              <>
                <BlockHeader type="minimum" title="库存共享比例" />
                <div style={{ paddingLeft: '12px' }}>
                  <InventorySynchronizationRatio {...fieldProps} />
                </div>
              </>
            )}
            

            
            {/* 外卖开票设置模块 */}
            <BlockHeader type="minimum" title="外卖开票设置" />
            <div style={{ paddingLeft: '12px' }}>
              <OrderInvoiceSwitchField {...fieldProps} />
            </div>
          </FormContext.Provider>
        </Form>
        </BlockLoading>
      </Drawer.Body>
      
      <Drawer.Footer>
        <div style={{ textAlign: 'right' }}>
          <Button onClick={onClose} style={{ marginRight: 8 }}>
            {isPreviewMode ? '关闭' : '取消'}
          </Button>
          {!isPreviewMode && (
            <Button type="primary" onClick={handleSubmit} loading={loading}>
              保存
            </Button>
          )}
        </div>
      </Drawer.Footer>
    </Drawer>
  );
};
```

## 数据模型

### API 集成
该组件将重用编辑页面中完全相同的 API 函数和模式：
- `api.getStoreProperty(kdtId, channelId)` - 用于初始数据加载
- `api.updateChannelShop()` - 用于保存更改
- `api.updateChannelStockAllocation()` - 用于库存分配更新（非多渠道时）

### 数据转换
遵循编辑页面的完全相同模式：

```typescript
// 重用编辑页面的现有初始化函数
const initExtSyncConfigs = (extConfigs) => {
  // 与编辑页面相同的实现
  if (!extConfigs) {
    return {
      hqTemplateInfoSync: [],
      outItemInfoSync: [],
    };
  }
  // ... 其余实现与编辑页面相同
};

// 重用编辑页面的现有格式化函数
const formatExtSyncConfigs4Submit = (data, allKeys) => {
  // 与编辑页面相同的实现
  // 此函数会遍历所有可能的字段，如果字段在数组中则设置为1，否则设置为0
  const submitValue = ExtSyncConfigKeys.reduce((prev, syncKey) => {
    const item = data[syncKey];
    if (!item) {
      return prev;
    }
    // 对每个字段，检查是否在选中数组中，是则为1，否则为0
    prev[syncKey] = (Object.values(allKeys[syncKey]) as string[]).reduce((prev, value) => {
      prev[value] = item.includes(value) ? 1 : 0; // 关键逻辑：不在数组中的字段自动设置为0
      return prev;
    }, {});
    return prev;
  }, {});
  return {
    ...mapValues(submitValue, value => JSON.stringify(value)),
    [StoreHandleConfig.OutItemTypeSync]: 2,
  };
};

// 遵循编辑页面模式初始化表单数据
const getStoreProperty = async () => {
  const [data] = await Promise.all([
    api.getStoreProperty(kdtId, channelId),
  ]);
  
  const initializeValue = {
    [StoreHandleConfig.storeOperatingStatus]: data.openStatus,
    [StoreHandleConfig.synchronizationOfBusinessHours]: data.syncBusinessHours,
    [StoreHandleConfig.stockRealTimeSync]: `${+data.extConfigs.stockThreshold <= 15 ? 15 : 50}`,
    [StoreHandleConfig.OrderInvoiceSwitch]: data.extConfigs.allowOpenInvoice ?? AllowOpenInvoiceStatus.on, // 根据需求默认启用
    
    // 使用枚举的商品同步规则新字段
    [StoreHandleConfig.itemSyncRange]: data.extConfigs.itemSyncRange || EItemSyncRange.all,
    [StoreHandleConfig.autoPublishSync]: data.extConfigs.autoPublishSync || EAutoPublishSync.manual,
    
    // 重用现有同步配置初始化
    ...initExtSyncConfigs(data.extConfigs),
  };
  
  // 库存共享比例初始化（仅非多渠道时）
  if (!isSupportMultiChannel) {
    const percentList = await api.queryChannelStockAllocation({
      storeId: kdtId as number,
      channelId: channelId as number,
    });
    
    const stockData = find(percentList, percentItem => percentItem.channelId === Number(channelId));
    if (stockData) {
      initializeValue[StoreHandleConfig.StockInventoryRatio] = stockData.stockAllocationPercent;
      const planValue = stockData.planAllocationPercent
        ? PlanAllocationStatus.SameWithStock
        : PlanAllocationStatus.NotShare;
      initializeValue[StoreHandleConfig.PlanStockInventoryRatio] = PlanStockOptions.find(
        item => item.key === planValue,
      );
    }
  }
  
  form.initialize(initializeValue);
};

// 遵循编辑页面模式提交数据
const onSubmit = async () => {
  const formValue = form.getValue();
  const {
    [StoreHandleConfig.storeOperatingStatus]: operateStatus,
    [StoreHandleConfig.synchronizationOfBusinessHours]: syncBusinessHours,
    [StoreHandleConfig.stockRealTimeSync]: stockThreshold,
    [StoreHandleConfig.OrderInvoiceSwitch]: allowOpenInvoice,
    [StoreHandleConfig.itemSyncRange]: itemSyncRange,
    [StoreHandleConfig.autoPublishSync]: autoPublishSync,
    [StoreHandleConfig.StockInventoryRatio]: stockAllocationPercent,
    [StoreHandleConfig.PlanStockInventoryRatio]: planAllocationPercent,
  } = formValue;

  // 根据更新范围处理字段清零逻辑
  let processedFormValue = { ...formValue };
  
  if (itemSyncRange === EItemSyncRange.partial) {
    // 指定部分信息：将所有基础信息和价格信息字段设置为0
    // 通过从数组中移除这些字段，formatExtSyncConfigs4Submit函数会自动将它们设置为0
    const outItemInfoSync = processedFormValue[StoreHandleConfig.OutItemInfoSync] || [];
    const filteredSync = outItemInfoSync.filter(field => !ALL_BASIC_AND_PRICE_FIELDS.includes(field));
    processedFormValue[StoreHandleConfig.OutItemInfoSync] = filteredSync;
    
  } else if (itemSyncRange === EItemSyncRange.bindOnly) {
    // 仅绑定商品，不更新信息：将所有销售信息、基础信息和价格信息字段设置为0
    // 通过清空数组，formatExtSyncConfigs4Submit函数会自动将所有字段设置为0
    processedFormValue[StoreHandleConfig.OutItemInfoSync] = [];
  }

  await Promise.all([
    api.updateChannelShop({
      syncBusinessHours,
      operateStatus,
      kdtId: Number(kdtId),
      channelId,
      extConfigs: {
        stockThreshold,
        allowOpenInvoice,
        // 新的商品同步规则字段
        itemSyncRange,
        autoPublishSync,
        // 库存共享比例（仅非多渠道时）
        stockSynPercent: !isSupportMultiChannel ? String(stockAllocationPercent) : undefined,
        // 使用相同格式化函数的现有同步配置，使用处理后的表单值
        ...formatExtSyncConfigs4Submit(processedFormValue, {
          [StoreHandleConfig.HqTemplateInfoSync]: GoodsUpdateRuleItem,
          [StoreHandleConfig.OutItemInfoSync]: YouzanGoodsUpdateRuleItem,
        }),
      },
    }),
    
    // 库存共享比例更新（仅非多渠道时）
    !isSupportMultiChannel && api.updateChannelStockAllocation({
      stockAllocationPercent,
      planAllocationPercent:
        typeof planAllocationPercent === 'object' && planAllocationPercent.key === PlanAllocationStatus.SameWithStock
          ? stockAllocationPercent
          : (typeof planAllocationPercent === 'object' ? planAllocationPercent.key : planAllocationPercent),
      channelId: Array.isArray(channelId) ? Number(channelId[0]) : Number(channelId),
      storeId: Array.isArray(kdtId) ? Number(kdtId[0]) : Number(kdtId),
    }),
  ].filter(Boolean));
};
```

## 错误处理

### 验证规则
```typescript
const validationRules = {
  [StoreHandleConfig.itemSyncRange]: [Validators.required('更新范围不能为空')],
  [StoreHandleConfig.stockRealTimeSync]: [
    (value, formData) => {
      const outItemInfoSync = formData[StoreHandleConfig.OutItemInfoSync] || [];
      const stockNumSelected = outItemInfoSync.includes(YouzanGoodsUpdateRuleItem.stockNum);
      const itemSyncRange = formData[StoreHandleConfig.itemSyncRange];
      
      // 仅当库存复选框选中且销售信息部分可见时才需要验证
      const shouldValidate = stockNumSelected && itemSyncRange !== EItemSyncRange.bindOnly;
      
      if (shouldValidate && !value) {
        return { name: 'required', message: '库存同步规则不能为空' };
      }
      return null;
    }
  ],
  [StoreHandleConfig.autoPublishSync]: [Validators.required('发布设置不能为空')],
  [StoreHandleConfig.OutItemInfoSync]: [
    (value, formData) => {
      const itemSyncRange = formData[StoreHandleConfig.itemSyncRange];
      const outItemInfoSync = value || [];
      
      // 当选择"全部信息"时，基础信息和价格信息至少选中一个
      const isAllInfoMode = itemSyncRange === EItemSyncRange.all;
      if (isAllInfoMode) {
        const hasBasicInfoSelected = BASIC_INFO_FIELDS.some(field => outItemInfoSync.includes(field));
        const hasPriceInfoSelected = PRICE_INFO_FIELDS.some(field => outItemInfoSync.includes(field));
        
        if (!hasBasicInfoSelected && !hasPriceInfoSelected) {
          return { name: 'required', message: '基础信息和价格信息至少选中一个选项' };
        }
      }
      
      return null;
    }
  ],
};
```

### 错误状态
- **加载错误**: 显示带有重试选项的错误消息
- **验证错误**: 显示字段级验证消息
- **保存错误**: 显示带有错误详情的通知
- **网络错误**: 显示重试机制

### 错误恢复
```typescript
const handleSaveError = (error: any) => {
  if (error.code === 'NETWORK_ERROR') {
    Notify.error('网络错误，请检查网络连接后重试');
  } else if (error.code === 'VALIDATION_ERROR') {
    // Field validation errors are handled by form
  } else {
    Notify.error(error.message || '保存失败，请重试');
  }
};
```

## 测试策略

### 单元测试
1. **组件渲染**: 测试所有表单部分是否正确渲染
2. **表单验证**: 测试必填字段的验证规则
3. **数据转换**: 测试 API 数据格式化函数
4. **条件逻辑**: 测试基于选择的字段可见性

### 集成测试
1. **API 集成**: 测试数据加载和保存
2. **表单提交**: 测试完整的表单提交流程
3. **错误处理**: 测试错误场景和恢复

### 用户交互测试
1. **抽屉打开/关闭**: 测试抽屉可见性状态
2. **表单交互**: 测试复选框/单选框交互
3. **条件字段**: 测试基于 stockNum 选择的 stockThreshold 可见性
4. **保存/取消**: 测试保存和取消操作

### 测试数据设置
```typescript
const mockApiData = {
  openStatus: EStoreOperatingStatus.on,
  syncBusinessHours: EsynchronizationOfBusinessHours.on,
  extConfigs: {
    itemSyncRange: '1',
    takeUpOrDown: 1,
    stockNum: 1,
    stockThreshold: '15',
    autoPublishSync: '1',
    allowOpenInvoice: AllowOpenInvoiceStatus.on,
    hqTemplateInfoSync: '{"group":1,"title":1}',
    outItemInfoSync: '{"stockNum":1,"title":1}',
  },
};
```

### 性能考虑
- **懒加载**: 仅在打开时加载抽屉内容
- **表单优化**: 对不经常更改的表单部分使用 React.memo
- **API 缓存**: 缓存初始数据以避免重复 API 调用
- **包大小**: 仅导入必要的 Zent 组件

### 可访问性
- **键盘导航**: 确保所有表单元素都可以通过键盘访问
- **屏幕阅读器支持**: 适当的 ARIA 标签和描述
- **焦点管理**: 在抽屉打开/关闭时管理焦点
- **颜色对比**: 确保所有文本元素有足够的对比度

## 跳转逻辑替换方案

### 识别现有跳转逻辑
通过代码搜索，找到以下需要替换的具体位置：

#### 1. 主列表页面跳转逻辑
**文件**: `client/pages/omni-channel/pages/list/components/table/index.tsx`
**位置**: 第619-623行
```typescript
<LinkButton
  onClick={() => {
    history.push(
      `/list/edit?kdtId=${kdtId}&channelId=${channelId}&&accountType=${accountType}`,
    );
  }}
>
  渠道设置
</LinkButton>
```

#### 2. 新列表页面渠道设置组件
**文件**: `client/pages/omni-channel/pages/new-list/components/table/operate/channel-setting.tsx`
**位置**: 第25-28行
```typescript
<LinkButton
  onClick={() => {
    history.push(
      `/list/edit?kdtId=${kdtId}&channelId=${channelId}&&accountType=${accountType}`,
    );
  }}
>
  渠道设置
</LinkButton>
```

#### 3. 京东外卖页面的渠道设置按钮
**文件**: `client/pages/omni-channel/pages/new-list/jd-takeout/index.tsx`
**位置**: 第104-105行
```typescript
<Button type="primary" onClick={() => {
  window.open(`/v4/channel/omni/omni-channel/auth-list#/list/edit?kdtId=${_global.kdtId}&channelId=${CHANNEL_ID.JD_WAIMAI}&accountType=${CHANNEL_ID.JD_WAIMAI}`, '_blank');
}}>渠道设置</Button>
```

### 替换为抽屉打开逻辑

#### 1. 替换主列表页面 (table/index.tsx)
```typescript
// 原代码替换前：
<LinkButton
  onClick={() => {
    history.push(
      `/list/edit?kdtId=${kdtId}&channelId=${channelId}&&accountType=${accountType}`,
    );
  }}
>
  渠道设置
</LinkButton>

// 替换后：
<LinkButton
  onClick={() => {
    handleOpenEditDrawer(kdtId, channelId, accountType);
  }}
>
  渠道设置
</LinkButton>

// 在父组件中添加状态管理和处理函数：
const [editDrawerVisible, setEditDrawerVisible] = useState(false);
const [editDrawerProps, setEditDrawerProps] = useState({});

const handleOpenEditDrawer = (kdtId, channelId, accountType) => {
  setEditDrawerProps({ kdtId, channelId, accountType, mode: 'edit' });
  setEditDrawerVisible(true);
};

// 添加抽屉组件：
<EditDrawer
  visible={editDrawerVisible}
  onClose={() => setEditDrawerVisible(false)}
  onSuccess={() => {
    setEditDrawerVisible(false);
    // 刷新表格数据
    tableRef.current?.reload();
  }}
  {...editDrawerProps}
/>
```

#### 2. 替换渠道设置组件 (channel-setting.tsx)
```typescript
// 原代码替换前：
const ChannelSetting: React.FC<{
  accountType: number;
  wmAbilityIsValid: boolean;
  kdtId: number;
  channelId: number;
}> = ({ accountType, wmAbilityIsValid, kdtId, channelId }) => {
  const history = useHistory();
  // ... 原逻辑
  return (
    <LinkButton
      onClick={() => {
        history.push(
          `/list/edit?kdtId=${kdtId}&channelId=${channelId}&&accountType=${accountType}`,
        );
      }}
    >
      渠道设置
    </LinkButton>
  );
};

// 替换后：
const ChannelSetting: React.FC<{
  accountType: number;
  wmAbilityIsValid: boolean;
  kdtId: number;
  channelId: number;
  onOpenEditDrawer: (kdtId: number, channelId: number, accountType: number) => void;
}> = ({ accountType, wmAbilityIsValid, kdtId, channelId, onOpenEditDrawer }) => {
  // 移除 useHistory
  // ... 其他逻辑保持不变
  return (
    <LinkButton
      onClick={() => {
        onOpenEditDrawer(kdtId, channelId, accountType);
      }}
    >
      渠道设置
    </LinkButton>
  );
};
```

#### 3. 替换京东外卖页面 (jd-takeout/index.tsx)
```typescript
// 原代码替换前：
<Button type="primary" onClick={() => {
  window.open(`/v4/channel/omni/omni-channel/auth-list#/list/edit?kdtId=${_global.kdtId}&channelId=${CHANNEL_ID.JD_WAIMAI}&accountType=${CHANNEL_ID.JD_WAIMAI}`, '_blank');
}}>渠道设置</Button>

// 替换后：
<Button type="primary" onClick={() => {
  handleOpenEditDrawer(_global.kdtId, CHANNEL_ID.JD_WAIMAI, CHANNEL_ID.JD_WAIMAI);
}}>渠道设置</Button>

// 在组件中添加状态管理：
const [editDrawerVisible, setEditDrawerVisible] = useState(false);
const [editDrawerProps, setEditDrawerProps] = useState({});

const handleOpenEditDrawer = (kdtId, channelId, accountType) => {
  setEditDrawerProps({ kdtId, channelId, accountType, mode: 'edit' });
  setEditDrawerVisible(true);
};

// 添加抽屉组件：
<EditDrawer
  visible={editDrawerVisible}
  onClose={() => setEditDrawerVisible(false)}
  onSuccess={() => {
    setEditDrawerVisible(false);
    // 刷新表格数据
    tableRef.current?.reload();
  }}
  {...editDrawerProps}
/>
```

### 数据刷新机制
```typescript
const handleDrawerSuccess = () => {
  setDrawerVisible(false);
  
  // 根据不同的父组件实现不同的刷新逻辑
  if (typeof onRefresh === 'function') {
    onRefresh();
  } else if (typeof refreshData === 'function') {
    refreshData();
  } else {
    // 重新加载当前页面数据
    window.location.reload();
  }
};
```