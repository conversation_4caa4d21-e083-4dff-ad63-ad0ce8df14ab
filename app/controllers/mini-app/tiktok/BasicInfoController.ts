import { Router, API, Inject, Index } from '@youzan/assets-route-plugin';
import CommonBasicInfoController from '../common/BasicInfoController';
import TiktokAccountInfoService from '../../../services/api/channels/tiktok/TiktokAccountInfoService';

@Router('tiktok/basic-info')
class BasicInfoController extends CommonBasicInfoController {
  @Inject()
  private readonly tikTokAccountInfoService!: TiktokAccountInfoService;

  public async init() {
    this.channelName = 'tiktok';
    super.init();
  }

  @Index('')
  public async getBaseInfoHtml() {
    await this.checkIsBind();
    const { ctx } = this;
    ctx.setState('subtitle', '基础信息');
    return ctx.render('mini-app/common/basic-info/index.html');
  }

  /**
   * 获取抖音小程序信息
   */
  @API('GET', 'account-info.json')
  public async getAccountInfoJson() {
    const accountInfo = await this.getAccountInfo();
    await this.ctx.json(
      0,
      'ok',
      accountInfo
        ? {
            name: accountInfo.name,
            avatar: accountInfo.icon,
            description: accountInfo.desc,
            category: accountInfo.categoryInfo,
            appId: accountInfo.appId,
            registerSource: accountInfo.createdFrom,
            categoryInfoList: accountInfo.categoryInfoList,
          }
        : {},
    );
  }

  protected async getAccountInfo() {
    const { kdtId } = this.ctx;
    const result = await this.tikTokAccountInfoService.getBasicInfo({ kdtId });
    return result;
  }
}

export = BasicInfoController;
