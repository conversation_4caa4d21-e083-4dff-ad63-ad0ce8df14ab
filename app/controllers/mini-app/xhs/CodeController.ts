import { Router, Inject, Index } from '@youzan/assets-route-plugin';
import args from '@youzan/utils/url/args';

import CommonBasicInfoController from '../common/BasicInfoController';
import QrcodeService from '../../../services/api/channels/apps/QrcodeService';
import ChannelAuthBindService from '../../../services/api/channels/channel/core/ChannelAuthBindService';

@Router('xhs/code')
class CodeController extends CommonBasicInfoController {
  @Inject()
  private QrcodeService!: QrcodeService;

  @Inject()
  private channelAuthBindService!: ChannelAuthBindService;

  public async init() {
    this.channelName = 'xhs';
    super.init();
  }

  @Index('url')
  public async getCodeUrl() {
    const { ctx } = this;
    const { kdtId, userId, request, accountId, sid } = ctx;
    const { businessType = 1, size = 200 } = request.query;

    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');
    this.validator.required(accountType, 'accountType 不能为空');

    // authType: 1/首次授权 2/重新授权
    const authType = 1;

    const params = {
      accountType,
      // businessType: 1/商城业务 2/品宣
      businessType,
      kdtId,
      userId,
      authType,
      redirectUri: args.add('https://www.youzan.com/v4/channel/xhs/auth/callback', {
        kdtId,
        accountId,
        sid,
        authType,
        businessType,
        accountType,
      }),
    };

    const result = await this.channelAuthBindService.getAuthPageUrl(params);
    const res = await this.QrcodeService.getQrcode(result.url, { size });
    ctx.type = 'image/png';
    ctx.body = res;
  }
}

export = CodeController;
