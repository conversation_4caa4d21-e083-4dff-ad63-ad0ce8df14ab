import { Index, Router, Inject } from '@youzan/assets-route-plugin';
import BaseController from '../BaseController';
import MeituanWaimaiCallbackService from '../../../services/api/retail/open/MeituanWaimaiCallbackService';

/**
 * 通用授权
 */
@Router('meituan/callback')
class AuthController extends BaseController {
  @Inject()
  private meituanWaimaiCallbackService!: MeituanWaimaiCallbackService;

  async callback(type: number) {
    const { ctx } = this;

    const typeStr = type === 1 ? '绑定' : '解绑';
    ctx.setState({ typeStr });

    try {
      ctx.logger.info(`访问callback，入参: ${JSON.stringify(ctx.query)}`, '', {});
    } catch (e) {}

    try {
      const result = await this.meituanWaimaiCallbackService.mtRelaxationChannelShopBind({
        ...ctx.query,
        type,
      });
      ctx.logger.info(
        `美团回调成功 -- 入参 - ${JSON.stringify(ctx.query)} -- 结果 - ${JSON.stringify(result)}`,
      );
      return ctx.render('omni-channel/bind-success.html');
    } catch (e) {
      ctx.logger.error(
        `美团回调失败 -- 失败原因 - ${JSON.stringify(e)} -- 入参 - ${JSON.stringify(ctx.query)}`,
      );
      // @ts-ignore
      ctx.setState({ status: '失败', message: e?.msg || e?.message || '请稍后重试' });
    }

    return ctx.render('mini-app/meituan/callback.html');
  }

  @Index('bind')
  async getBindHtml() {
    await this.callback(1);
  }

  @Index('unbind')
  async getUnBindHtml() {
    await this.callback(2);
  }
}

export = AuthController;
