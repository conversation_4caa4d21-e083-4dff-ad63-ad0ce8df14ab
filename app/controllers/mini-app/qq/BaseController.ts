import MiniAppBaseController from '../BaseController';
import PermiseCheckError from '../../../exceptions/PremiseCheckError';
import { Inject } from '@youzan/assets-route-plugin';
import ChannelCoreAccountService from '../../../services/api/channels/channel/core/ChannelCoreAccountService';

class IndexController extends MiniAppBaseController {
  @Inject()
  public readonly channelCoreAccountService!: ChannelCoreAccountService;

  public async init() {
    this.channelName = 'qq';
    super.init();
  }

  protected async checkIsBind() {
    const bindInfo = await this.getBindInfo();
    if (!bindInfo) {
      throw new PermiseCheckError(12345, 'QQ 小程序未授权');
    } else {
      return bindInfo;
    }
  }
}

export default IndexController;
