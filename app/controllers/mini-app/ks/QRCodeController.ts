import { Router, API, Inject } from '@youzan/assets-route-plugin';

import BaseController from './BaseController';
import KsAppletQrCodeService from '../../../services/api/ebiz/video/channels/uc/KsAppletQrCodeService';
import KuaishouMiniAppService from '../../../services/api/channels/channel/core/KuaishouMiniAppService';

@Router('ks')
class QRCodeController extends BaseController {
  @Inject()
  private readonly KsAppletQrCodeService!: KsAppletQrCodeService;

  @Inject()
  private readonly KuaishouMiniAppService!: KuaishouMiniAppService;

  /**
   * 获取小程序二维码
   */
  @API('GET', 'ks-qr-code.json')
  public async getKsQrCode() {
    const { request, kdtId } = this.ctx;
    const { path = '', scene = '' } = request.query;
    const accountInfo = await this.KuaishouMiniAppService.getBasicInfo({ kdtId });
    const result = await this.KsAppletQrCodeService.getQrCode({
      appId: accountInfo.appId,
      path: path || '/pages/home/<USER>/index',
      scene,
    });
    result.qrCodeUrl =
      typeof result.qrCode === 'string' ? `data:image/png;base64,${result.qrCode}` : result.qrCode;
    await this.ctx.json(0, 'ok', result);
  }
}

export = QRCodeController;
