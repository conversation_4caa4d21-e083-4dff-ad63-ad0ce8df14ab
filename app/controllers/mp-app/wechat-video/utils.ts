const RETAIL_ROLE_MAP = {
  0: 'retail',
  1: 'retail_chain_hq_prod',
  2: 'retail_chain_offline_prod',
};

const EDU_ROLE_MAP = {
  0: 'wsc', // 教育单店和wsc一致
  1: 'edu_chain_hq_prod',
  2: 'edu_chain_sub_shop_prod',
};

const WSC_ROLE_MAP = {
  0: 'wsc',
  1: 'wsc_chain_hq_prod',
  2: 'wsc_chain_sub_shop_prod',
};

const isWSC = (shopType: number) => {
  return shopType === 0 || shopType === 2 || shopType === 9;
};

const isStore = (shopType: number) => {
  return shopType === 7 || shopType === 10;
};

const isYZEdu = (shopType: number, shopTopic: number) => {
  return isWSC(shopType) && shopTopic === 1;
};

const isSupplier = (shopType: number) => {
  return shopType === 14;
};

export default {
  STORE: 7,
  isWSC,
  isStore,
  isYZEdu,
  isSupplier,
  getProdCode(shopType: number, shopRole = 0, shopTopic: number) {
    if (isYZEdu(shopType, shopTopic)) {
      return (EDU_ROLE_MAP as any)[shopRole as any];
    }
    if (isStore(shopType)) {
      return (RETAIL_ROLE_MAP as any)[shopRole as any];
    }
    if (isWSC(shopType)) {
      return (WSC_ROLE_MAP as any)[shopRole as any];
    }
  },
};
