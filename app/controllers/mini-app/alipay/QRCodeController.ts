import { Router, API, Inject } from '@youzan/assets-route-plugin';
import BaseController from './BaseController';
import WeappQRCodeService from '../../../services/api/channels/apps/WeappQRCodeService';
import channelMap from '../../../constants/miniAppChannelMap';

@Router('alipay')
class QRCodeController extends BaseController {
  @Inject()
  private readonly weappQRCodeService!: WeappQRCodeService;

  private requestAlipayQrCode() {
    const { kdtId, request } = this.ctx;
    const { businessType = 1, page, describe, scene } = request.query;
    this.validator.required(page, 'page 不能为空');
    this.validator.required(describe, 'describe 不能为空');
    this.validator.required(scene, 'scene 不能为空');
    return this.weappQRCodeService.getAlipayQrCode({
      businessType,
      accountType: channelMap.alipay.accountType,
      kdtId,
      page,
      describe,
      scene,
    });
  }

  /**
   * 获取小程序二维码 - 对外提供
   */
  @API('GET', 'qr-code.json')
  public async getAlipayQrCode() {
    try {
      await this.checkIsBind();
      await this.checkPayState();
    } catch (e) {
      if (e && e.errorContent) {
        return this.ctx.json(e.errorContent.code, e.errorContent.msg);
      }
      throw e;
    }

    const result = await this.requestAlipayQrCode();
    return this.ctx.json(0, 'ok', result);
  }

  /**
   * 获取小程序二维码 - 对内使用
   */
  @API('GET', 'qr-code-internally.json')
  public async getAlipayQrCodeInternally() {
    const result = await this.requestAlipayQrCode();
    await this.ctx.json(0, 'ok', result);
  }
}

export = QRCodeController;
