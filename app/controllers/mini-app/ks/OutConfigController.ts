import { Router, API, Inject } from '@youzan/assets-route-plugin';

import BaseController from './BaseController';
import KsAppletOutConfigService from '../../../services/api/ebiz/video/channels/ks/KsAppletOutConfigService';

@Router('ks/config')
class OutConfigController extends BaseController {
  @Inject()
  private readonly KsAppletOutConfigService!: KsAppletOutConfigService;

  /**
   * 查询快手小程序外部配置设置状态
   */
  @API('GET', 'out-config.json')
  public async getOutConfig() {
    const { kdtId } = this.ctx;
    const result = await this.KsAppletOutConfigService.getOutConfigSetUpStatus({
      kdtId,
    });
    await this.ctx.json(0, 'ok', result);
  }
}

export = OutConfigController;
