import { Router, Index, API, Inject } from '@youzan/assets-route-plugin';
import { checkUnifiedHqStore } from '@youzan/utils-shop';

import CommonBasicInfoController from '../common/BasicInfoController';
import CategoryBackService from '../../../services/api/mall/item/CategoryBackService';
import ShopThirdChannelService, {
  IPoiSearchRequest,
} from '../../../services/api/channels/omni-channel/ShopThirdChannelService';
import ThirdPartyChannelShopReadService from '../../../services/api/channels/omni-channel/ThirdPartyChannelShopReadService';
import ChannelCoreAccountService from '../../../services/api/channels/channel/core/ChannelCoreAccountService';
import ItemSearchService from '../../../services/api/ic/search/ItemSearchService';
import { isNumber } from 'lodash';

const TiktokChannel = 401;

@Router('tiktok-coupon/goods')
class GoodsController extends CommonBasicInfoController {
  @Inject()
  public readonly categoryBackService!: CategoryBackService;

  @Inject()
  public readonly shopThirdChannelService!: ShopThirdChannelService;

  @Inject()
  public readonly thirdPartyChannelShopReadService!: ThirdPartyChannelShopReadService;

  @Inject()
  public readonly channelCoreAccountService!: ChannelCoreAccountService;

  @Inject()
  public readonly itemSearchService!: ItemSearchService;

  public async init() {
    this.channelName = 'tiktok-coupon';
    super.init();
  }

  @Index('')
  public async getGoodsHtml() {
    const { ctx } = this;
    const { kdtId, userId } = this.ctx;

    // 判断抖音小程序授权
    let isBindTiktokCoupon = false;
    try {
      // 授权账号
      const accountRes = await this.shopThirdChannelService.getShopChannelAccount({
        channelAccountType: 1,
        channelId: TiktokChannel,
        kdtId,
      });
      const channelRes = await this.channelCoreAccountService.queryMpBindInfoByKdtId({
        businessType: 8,
        accountType: 21,
        externalId: kdtId,
      });
      // 授权渠道
      if (Array.isArray(accountRes)) {
        isBindTiktokCoupon =
          accountRes.some(({ channelId }) => channelId === TiktokChannel) && channelRes?.mpId;
      }
    } catch (error) {
      this.ctx.logger.error('获取抖音卡券小程序授权信息报错：', error, {
        kdtId,
      });
    }
    ctx.setGlobal('isBindTiktokCoupon', isBindTiktokCoupon);

    // 判断poi绑定店铺>0
    let isBindPoiShop = false;
    try {
      const shopMetaInfo = ctx.getState('shopMetaInfo');
      const isUnifiedHqStore = checkUnifiedHqStore(shopMetaInfo);
      if (isUnifiedHqStore) {
        // 连锁L场景下需要判断是否绑定了poi店铺
        const res = await this.thirdPartyChannelShopReadService.queryChannelShops({
          adminId: userId,
          channelIds: [TiktokChannel],
          kdtId,
          pageSize: 1,
          pageNum: 1,
        });
        if (isNumber(res?.total) && res.data) {
          isBindPoiShop = res.total > 0;
        }
      } else {
        // 单店场景下会自动绑定poi店铺
        const res = await this.shopThirdChannelService.queryDouYinPoi({
          kdtId,
          pageSize: 10,
          pageNum: 1,
        });
        if (isNumber(res?.total)) {
          isBindPoiShop = res.total > 0;
        }
      }
    } catch (error) {
      this.ctx.logger.error('获取抖音卡券小程序绑定POI店铺失败：', error, {
        kdtId,
      });
    }
    ctx.setGlobal('isBindPoiShop', isBindPoiShop);

    ctx.setState('subtitle', '商品管理');
    return ctx.render('mini-app/tiktok-coupon/goods/index.html');
  }

  @API('GET', 'query-douyin-poi.json')
  public async queryDouyinPoi() {
    const { kdtId, request } = this.ctx;
    const { page, pageSize, poiName = '', poiIds = '' } = request.query;
    const params: IPoiSearchRequest = {
      kdtId,
      pageSize: Number(pageSize) || 10,
      pageNum: Number(page) || 1,
      poiName,
    };
    if (poiIds) {
      params.poiIds = JSON.parse(poiIds);
    }
    const res = await this.shopThirdChannelService.queryDouYinPoi(params);
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 查询渠道类目树状
   */
  @API('GET', 'out-category-tree.json')
  public async findOutCategoryTreeByChannel() {
    const { kdtId, request } = this.ctx;
    const { channel } = request.query;
    const res = await this.categoryBackService.findOutCategoryTreeByChannel({
      rootCategoryIds: [100302603, 21000000, 5000000, 4000000],
      kdtId,
      channel,
    });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 微商城单店选品列表
   */
  @API('GET', 'item-search-list.json')
  public async itemSearchList() {
    const { kdtId, request } = this.ctx;
    const { page, pageSize, title, groupChannel } = request.query;
    const res = await this.itemSearchService.list({
      itemSearchListOption: {
        withItemMark: true,
        containsComponentsPicture: true,
      },
      fromApp: 'wsc-pc-channel',
      kdtId,
      isDisplay: 1,
      soldStatuses: [1, 3],
      includeBizCodes: ['010000000051'],
      page: Number(page) || 1,
      pageSize: Number(pageSize) || 20,
      secondGroupIds: groupChannel ? [Number(groupChannel)] : null,
      title,
      channel: 0,
      offset: 0,
      limit: 10,
    });
    return this.ctx.json(0, 'success', res);
  }
}

export = GoodsController;
