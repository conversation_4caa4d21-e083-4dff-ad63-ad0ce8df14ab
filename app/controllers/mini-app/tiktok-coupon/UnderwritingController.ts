import { Router, API, Inject } from '@youzan/assets-route-plugin';
import MiniAppBaseController from '../BaseController';
import TiktokUnderwritingService from '../../../services/api/channels/tiktok/TiktokUnderwritingService';
import ShopThirdChannelService from '../../../services/api/channels/omni-channel/ShopThirdChannelService';
import ShopConfigReadService from '../../../services/api/shopcenter/shopconfig/ShopConfigReadService';
import ShopConfigWriteService from '../../../services/api/shopcenter/shopconfig/ShopConfigWriteService';
import channelMap from '../../../constants/miniAppChannelMap';

const TIKTOK_IM_ACCOUNT = 'tiktok_coupon_im_account';

@Router('tiktok-coupon/underwriting')
class UnderwritingController extends MiniAppBaseController {
  @Inject()
  private readonly tikTokUnderwritingService!: TiktokUnderwritingService;

  @Inject()
  private readonly shopThirdChannelService!: ShopThirdChannelService;

  @Inject()
  private readonly shopConfigReadService!: ShopConfigReadService;

  @Inject()
  private readonly shopConfigWriteService!: ShopConfigWriteService;

  public async init() {
    this.channelName = 'tiktok-coupon';
    super.init();
  }

  /**
   * 抖音查询商户号和进件信息
   */
  @API('GET', 'query-underwriting-status.json')
  public async queryUnderwritingStatus() {
    const { kdtId, userId } = this.ctx;
    const result = await this.tikTokUnderwritingService.queryMchStatus({
      kdtId,
      adminId: userId,
    });
    await this.ctx.json(0, 'ok', result);
  }

  /**
   * 抖音查询商户号和进件信息
   */
  @API('GET', 'query-underwriting-channel-status.json')
  public async queryUnderwritingChannelStatus() {
    const { kdtId, userId } = this.ctx;
    const rootKdtId = this.getRootKdtId();
    const channelConfig = channelMap['tiktok-coupon'];
    const result = await this.tikTokUnderwritingService.queryMchStatus({
      kdtId,
      adminId: userId,
      businessType: channelConfig.businessType,
    });
    const channelInfo = await this.shopThirdChannelService.getShopChannelAccount({
      kdtId: rootKdtId,
      channelAccountType: channelConfig.channelAccountType,
      channelId: channelConfig.channelId,
    });
    if (channelInfo && channelInfo[0]) {
      result.channelAccountValue = channelInfo[0]?.channelAccountValue ?? '';
    }
    await this.ctx.json(0, 'ok', result);
  }

  /**
   * 抖音提交商户号
   */
  @API('POST', 'submit-merchant.json')
  public async submitMerchant() {
    const { kdtId, userId } = this.ctx;
    const { mchId = 1, channelAccountValue } = this.ctx.request.body;
    const channelConfig = channelMap['tiktok-coupon'];
    try {
      await this.shopThirdChannelService.updateShopChannelAccount({
        channelAccountValue,
        channelAccountType: channelConfig.channelAccountType,
        channelId: channelConfig.channelId,
        loginKdtId: kdtId,
        kdtId,
      });
      // 设置成功状态
    } catch (error) {
      // status 1 来客授权失败
      return this.ctx.json(0, 'ok', { status: 1, msg: (error as { msg: string }).msg });
    }
    try {
      await this.tikTokUnderwritingService.commitMch({
        mchId,
        kdtId,
        adminId: userId,
        businessType: channelConfig.businessType,
      });
    } catch (error) {
      // status 2 商户号授权失败
      return this.ctx.json(0, 'ok', {
        status: 2,
        msg: (error as { msg: string }).msg,
        channelAccountValue,
      });
    }

    // status 4 商户号授权成功
    await this.ctx.json(0, 'ok', { status: 4, mchId, channelAccountValue });
  }

  /**
   * 抖音修改商户号
   */
  @API('POST', 'update-merchant.json')
  public async updateMerchant() {
    const { kdtId, userId } = this.ctx;
    const { mchId = 1 } = this.ctx.request.body;
    try {
      await this.tikTokUnderwritingService.modifyMch({
        mchId,
        kdtId,
        adminId: userId,
        businessType: 8,
      });
    } catch (error) {
      // status 3 商户号更新失败
      return this.ctx.json(0, 'ok', { status: 3, msg: (error as { msg: string }).msg });
    }
    // status 4 商户号更新成功
    await this.ctx.json(0, 'ok', { status: 4, mchId });
  }

  /**
   * 获取抖音卡券小程序客服抖音号
   */
  @API('GET', 'query-im-account.json')
  public async queryImAccount() {
    const { kdtId } = this.ctx;
    const result = await this.shopConfigReadService.queryShopConfig(kdtId, TIKTOK_IM_ACCOUNT);
    let account = '';
    try {
      const list = JSON.parse(result.value || '[]');
      account = list[0] || '';
    } catch (err) {
      this.ctx.logger.error('解析抖音卡券小程序客服抖音号失败', err, {
        result,
      });
    }
    await this.ctx.json(0, 'ok', account);
  }

  /**
   * 设置抖音卡券小程序客服抖音号
   */
  @API('POST', 'set-im-account.json')
  public async setImAccount() {
    const { kdtId } = this.ctx;
    const { accountInfo } = this.ctx.request.body;
    const baseOperator = this.getOperatorParams();
    const value = JSON.stringify([accountInfo]);
    const result = await this.shopConfigWriteService.setShopConfig({
      kdtId,
      key: TIKTOK_IM_ACCOUNT,
      value,
      operator: {
        fromApp: baseOperator.fromApp,
        name: baseOperator.operator.nickName,
        id: baseOperator.operator.userId,
        type: 1,
      },
    });
    await this.ctx.json(0, 'ok', result);
  }
}

export = UnderwritingController;
