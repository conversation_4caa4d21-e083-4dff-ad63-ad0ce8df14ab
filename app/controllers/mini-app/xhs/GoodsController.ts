import { Router, API, Inject, Index } from '@youzan/assets-route-plugin';

import CommonBasicInfoController from '../common/BasicInfoController';
import XiaohongshuChannelConsoleService from '../../../services/api/ebiz/video/channels/item/xhs/XiaohongshuChannelConsoleService';
import ItemQueryService from '../../../services/api/mall/item/ItemQueryService';
import XiaohongshuCategoryService from '../../../services/api/xhs/XiaohongshuCategoryService';
import { comQueryArrayData } from '../../../utils/queryUtils';
import { ShopAbilityUtil } from '@youzan/plugin-shop-ability';

@Router('xhs/goods')
class GoodsController extends CommonBasicInfoController {
  @Inject()
  private readonly XiaohongshuChannelConsoleService!: XiaohongshuChannelConsoleService;

  @Inject()
  private readonly ItemQueryService!: ItemQueryService;

  @Inject()
  private readonly xiaohongshuCategoryService!: XiaohongshuCategoryService;

  public async init() {
    this.channelName = 'xhs';
    super.init();
  }

  @Index(['', 'xhs-independent'])
  public async getGoodsHtml() {
    // await this.checkIsBind();
    const { ctx } = this;

    // 小红书独立版本判断
    const hasXhsIndependentAbility = await new ShopAbilityUtil(ctx).checkAbilityValid({
      keys: ['xhs_independent_ability'],
      kdtId: ctx.kdtId,
    });
    if (hasXhsIndependentAbility && ctx.url.indexOf('xhs-independent') < 0) {
      this.ctx.redirect('/v4/channel/xhs/goods/xhs-independent');
      return;
    }

    ctx.setState('subtitle', '商品管理');

    await this.resolveXhsLocalLifeInfo(ctx);

    let enableXhsSearch = false;

    try {
      const config = await ctx.apolloClient.getConfig({
        appId: 'wsc-pc-channel',
        namespace: `wsc-pc-channel.whitelist`,
        key: `enable_use_xhs_search`,
      });

      ctx.setGlobal('enableXhsSearchConfig', config);
      ctx.setGlobal('enableXhsSearchCtx.kdtId', ctx.kdtId);

      enableXhsSearch = this.parseWhiteList(config, ctx.kdtId);
    } catch (error) {}

    ctx.setGlobal('enableXhsSearch', enableXhsSearch);

    try {
      const config = await ctx.apolloClient.getConfig({
        appId: 'wsc-pc-channel',
        namespace: `wsc-pc-channel.whitelist`,
        key: `xhs_payfirst_version`,
      });

      ctx.setGlobal('xhsPayFirstVer', config);
    } catch (error) {}

    return ctx.render('mini-app/xhs/goods/index.html');
  }

  @API('GET', 'get-xhs-goods-list.json')
  public async getXhsGoodsList() {
    const { ctx } = this;
    const { kdtId, request } = ctx;
    const {
      auditStatus,
      channelCanSale,
      endCreateTime,
      pageSize,
      page,
      startCreateTime,
      title,
      bizType,
    } = request.query;
    const res = await this.XiaohongshuChannelConsoleService.listChannelPaged({
      auditStatus,
      channelCanSale,
      endCreateTime,
      pageSize,
      page,
      startCreateTime,
      title,
      kdtId,
      bizType,
    });
    return ctx.json(0, 'ok', res);
  }

  @API('GET', 'search-goods.json')
  public async searchGoods() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { pageSize, page, title, itemGroupIds, itemTypes, bizType } = this.ctx.getQuery();
    const params = {
      kdtId,
      pageSize,
      page,
      title,
      itemGroupIds: comQueryArrayData(itemGroupIds),
      itemTypes: comQueryArrayData(itemTypes),
      bizType,
    };
    const res = await this.XiaohongshuChannelConsoleService.searchGoods(params);

    return ctx.json(0, 'ok', res);
  }

  @API('GET', 'get-count-empty-category-goods.json')
  public async getCountEmptyCategoryGoods() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const res = await this.ItemQueryService.listItemsPagedNoCategory({
      kdtId,
    });
    return ctx.json(0, 'ok', res.count || 0);
  }

  @API('POST', 'batch-remove-item.json')
  public async batchRemoveItem() {
    const { ctx } = this;
    const {
      kdtId,
      request: {
        body: { itemId, bizType },
      },
    } = ctx;
    const param = {
      kdtId,
      itemId,
      bizType,
    };
    const res = await this.XiaohongshuChannelConsoleService.removeItem(param);
    return ctx.json(0, 'ok', res);
  }

  @API('POST', 'refresh-submit-audit.json')
  public async refreshSubmitAudit() {
    const { ctx } = this;
    const {
      kdtId,
      request: {
        body: { itemIds, bizType },
      },
    } = ctx;
    const res = await this.XiaohongshuChannelConsoleService.submitAudit({
      kdtId,
      itemIds,
      bizType,
    });
    return ctx.json(0, 'ok', res);
  }

  @API('POST', 'batch-add-item.json')
  public async batchAddItem() {
    const { kdtId, request } = this.ctx;
    const { itemIds, channelItems, bizType } = request.body;

    const param = {
      kdtId,
      itemIds,
      channelItems,
      bizType,
    };

    const res = await this.XiaohongshuChannelConsoleService.batchAddItem(param);
    return this.ctx.json(0, 'ok', res);
  }

  @API('GET', 'get-channel-item-list.json')
  public async getChannelItemList() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { itemIds, bizType } = this.ctx.getQuery();

    const res = await this.XiaohongshuChannelConsoleService.listChannelItem({
      kdtId,
      itemIds: comQueryArrayData(itemIds),
      bizType,
    });
    return ctx.json(0, 'ok', res);
  }

  @API('POST', 'save-channel-item.json')
  public async saveChannelItem() {
    const { kdtId, request } = this.ctx;
    const { itemId, shortTitle, pictures, bizType, outCategoryId, poiIds, payFirst } = request.body;
    const param = {
      kdtId,
      itemId,
      shortTitle,
      pictures,
      bizType,
      outCategoryId,
      poiIds,
      payFirst,
    };

    const res = await this.XiaohongshuChannelConsoleService.saveChannelItem(param);
    return this.ctx.json(0, 'ok', res);
  }

  @API('GET', 'get-items-num.json')
  public async getItemsNum() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { bizType } = this.ctx.getQuery();
    const res = await this.XiaohongshuChannelConsoleService.getItemsNum({
      kdtId,
      bizType,
    });
    return ctx.json(0, 'ok', res);
  }

  @API('GET', 'get-category-tree.json')
  public async getCategoryTree() {
    const { kdtId } = this.ctx;
    const res = await this.xiaohongshuCategoryService.queryOutCategory(kdtId);
    return this.ctx.json(0, 'ok', res);
  }
}

export = GoodsController;
