import { Router, Index, API, Inject } from '@youzan/assets-route-plugin';
import pick from 'lodash/pick';

import CommonBasicInfoController from '../common/BasicInfoController';
import XhsActiveService from '../../../services/api/ebiz/video/channels/xhs/XhsActiveService';
import ActivityManageService from '../../../services/api/ump/voucher/activity/ActivityManageService';
import { ShopAbilityUtil } from '@youzan/plugin-shop-ability';

@Router('xhs/fans')
class FansController extends CommonBasicInfoController {
  @Inject()
  public readonly XhsActiveService!: XhsActiveService;

  @Inject()
  public readonly ActivityManageService!: ActivityManageService;

  public async init() {
    this.channelName = 'xhs';
    super.init();
  }

  @Index(['list', 'edit-gift', 'xhs-independent-list', 'xhs-independent-edit-gift'])
  public async getGoodsHtml() {
    const { ctx } = this;
    // 小红书独立版本判断
    const hasXhsIndependentAbility = await new ShopAbilityUtil(ctx).checkAbilityValid({
      keys: ['xhs_independent_ability'],
      kdtId: ctx.kdtId,
    });
    if (hasXhsIndependentAbility && ctx.url.indexOf('xhs-independent') < 0) {
      const currentPath = ctx.url.split('/').pop() || '';
      const redirectPath = `/v4/channel/xhs/fans${
        currentPath ? `/xhs-independent-${currentPath}` : ''
      }`;
      ctx.redirect(redirectPath);

      return;
    }
    ctx.setState('subtitle', '专业号涨粉');
    return ctx.render('mini-app/xhs/fans/index.html');
  }

  // 分页查询活动信息和统计信息 (B端优惠券优惠码活动列表)
  @API('GET', 'search-ump-activity.json')
  public async searchUmpActivity() {
    const { ctx } = this;
    const { kdtId, request } = ctx;
    const params = pick(request.query, [
      'activityTypeGroup',
      'activityDisplayType',
      'title',
      'pageNo',
      'pageSize',
    ]);
    const { items, paginator } = await this.XhsActiveService.searchUmpActivity({
      ...params,
      kdtId,
    });
    const list = await this.appendCouponSign({ items });
    return ctx.json(0, 'ok', {
      items: list,
      paginator,
    });
  }

  // 创建小红书关注涨粉活动
  @API('POST', 'create-gift-activity.json')
  public async createGiftActivity() {
    const { ctx } = this;
    const postData = pick(ctx.request.body, [
      'umpActivityId',
      'promoterType',
      'umpActivityAlias',
      'activityName',
      'startTime',
      'umpActivityType',
      'endTime',
      'activityType',
    ]);
    const res = await this.XhsActiveService.createXhsActivity({
      ...postData,
      kdtId: ctx.kdtId,
      operator: String(ctx.userId),
    });
    return ctx.json(0, 'ok', res);
  }

  // 更新小红书关注涨粉活动
  @API('POST', 'update-gift-activity.json')
  public async updateGiftActivity() {
    const { ctx } = this;
    const postData = pick(ctx.request.body, [
      'xhsActivityId',
      'umpActivityId',
      'promoterType',
      'umpActivityAlias',
      'activityName',
      'startTime',
      'umpActivityType',
      'endTime',
      'activityType',
    ]);
    const res = await this.XhsActiveService.updateXhsActivity({
      ...postData,
      kdtId: ctx.kdtId,
      operator: String(ctx.userId),
    });
    return ctx.json(0, 'ok', res);
  }

  // 失效小红书关注涨粉活动
  @API('POST', 'invalid-gift-activity.json')
  public async invalidGiftActivity() {
    const { ctx } = this;
    const postData = pick(ctx.request.body, ['xhsActivityId']);
    const res = await this.XhsActiveService.invalidXhsActivity({
      ...postData,
      kdtId: ctx.kdtId,
      operator: String(ctx.userId),
    });
    return ctx.json(0, 'ok', res);
  }

  // 删除小红书关注涨粉活动
  @API('POST', 'delete-gift-activity.json')
  public async deleteGiftActivity() {
    const { ctx } = this;
    const postData = pick(ctx.request.body, ['xhsActivityId']);
    const res = await this.XhsActiveService.deleteXhsActivity({
      ...postData,
      kdtId: ctx.kdtId,
      operator: String(ctx.userId),
    });
    return ctx.json(0, 'ok', res);
  }

  // 分页查询活动信息 (B端小红书涨粉活动列表)
  @API('GET', 'search-xhs-activity.json')
  public async searchXhsActivity() {
    const { ctx } = this;
    const { kdtId, request } = ctx;
    const params = pick(request.query, [
      'activityName',
      'activityStatus',
      'activityType',
      'pageNo',
      'pageSize',
    ]);
    const res = await this.XhsActiveService.searchXhsActivity({
      ...params,
      kdtId,
    });

    return ctx.json(0, 'ok', res);
  }

  // 根据活动id查询小红书关注涨粉活动
  @API('GET', 'get-gift-activity.json')
  public async getGiftActivity() {
    const { ctx } = this;
    const { kdtId, request } = ctx;
    const params = pick(request.query, ['xhsActivityId']);
    const { umpActivityDTO, ...rest } = await this.XhsActiveService.getXhsActivity({
      ...params,
      kdtId,
    });
    const list = await this.appendCouponSign({ items: [umpActivityDTO] });
    return ctx.json(0, 'ok', {
      ...rest,
      umpActivityDTO: list[0],
    });
  }

  // 拼接 coupon 的 sign 字段，sign 用来提高安全性
  async appendCouponSign({
    items = [],
    key = 'activityId',
  }: {
    items: any[];
    key?: string;
  }): Promise<any[]> {
    const { ctx } = this;
    const res = [];
    if (items.length < 1) return [];
    const activityIds = items.map(i => i[key]);
    try {
      const result = await this.ActivityManageService.listActivitySigns({
        kdtId: ctx.kdtId,
        operatorId: ctx.userId,
        activityIds,
      });
      const activityIdMap = result.reduce((memo: any, item: any) => {
        memo[item.activityId] = item;

        return memo;
      }, {});

      for (const item of items) {
        const { sign } = activityIdMap[item[key]];
        res.push({
          ...(item as any),
          sign,
        });
      }
    } catch (error) {
      this.ctx.logger.warn(`拼接优惠券sign报错：${error}`);
    }
    return res;
  }
}

export = FansController;
