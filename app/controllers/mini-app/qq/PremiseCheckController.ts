import { Router, API } from '@youzan/assets-route-plugin';
import BaseController from './BaseController';

/**
 * 前置条件验证
 */
@Router('qq/premise-check')
class PremiseCheckController extends BaseController {
  /**
   * 校验小程序是否完全能用 - 对外提供
   */
  @API('GET', 'is-pass.json')
  public async isPass() {
    const { ctx } = this;
    try {
      await this.checkIsBind();
    } catch (error) {
      return ctx.json(0, 'ok', { premiseCheck: false });
    }

    return ctx.json(0, 'ok', { premiseCheck: true });
  }
}

export = PremiseCheckController;
