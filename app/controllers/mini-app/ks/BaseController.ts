import MiniAppBaseController from '../BaseController';
import PermiseCheckError from '../../../exceptions/PremiseCheckError';

class IndexController extends MiniAppBaseController {
  public async init() {
    this.channelName = 'ks';
    super.init();
  }

  protected async checkIsBind() {
    const bindInfo = await this.getBindInfo();
    if (!bindInfo) {
      throw new PermiseCheckError(12345, '快手小程序未授权');
    } else {
      return bindInfo;
    }
  }
}

export default IndexController;
