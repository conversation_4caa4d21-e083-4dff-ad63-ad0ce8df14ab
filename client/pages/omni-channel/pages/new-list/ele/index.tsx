import * as React from 'react';
import { RouteComponentProps, useHistory } from 'react-router-dom';
import { Divider } from '@youzan/react-components';
import { getCommonColumns } from 'pages/omni-channel/pages/new-list/components/table/columns';
import Table from 'pages/omni-channel/pages/new-list/components/table';
import * as api from 'pages/omni-channel/pages/list/components/table/api';
import { ORG_CATEGORY } from '@youzan/utils-shop';
import { CHANNEL_ID, CHANNEL_TYPE } from 'pages/omni-channel/constant';
import Filter from 'pages/omni-channel/pages/new-list/components/filter';
import { getExportParams } from 'pages/omni-channel/pages/new-list/components/filter/exprot';
import { useQuery } from 'pages/omni-channel/hooks/use-query';
import { CHANNEL_TYPE as NEW_CHANNEL_TYPE } from 'pages/omni-channel/pages/new-list/constants';
import ChannelSetting from 'pages/omni-channel/pages/new-list/components/table/operate/channel-setting';
import GoodsManage from 'pages/omni-channel/pages/new-list/components/table/operate/goods-manage';
import UnBindBtn from 'pages/omni-channel/component/un-bind';

const getColumns = ({ tableRef }) => {
  return getCommonColumns().map(column => {
    if (column.name === 'handle') {
      return {
        ...column,
        bodyRender: data => {
          const { channelId, kdtId, accountType, wmAbilityIsValid } = data;
          const items = [
            <GoodsManage />,
            <ChannelSetting
              channelId={channelId}
              kdtId={kdtId}
              accountType={accountType}
              wmAbilityIsValid={wmAbilityIsValid}
            />,
            <UnBindBtn
              accountType={accountType}
              kdtId={kdtId}
              position="top-right"
              channelType={CHANNEL_TYPE.WAIMAITONG}
              reloadAndRest={tableRef.current?.reload}
              channelId={channelId}
            />,
          ];
          return <Divider items={items.filter(node => Boolean(node))} />;
        },
      };
    }
    return column;
  });
};

export const List: React.FC<RouteComponentProps> = () => {
  const history = useHistory();
  const tableRef = React.useRef<any>();
  const query = useQuery();

  const [searchParams, setSearchParams] = React.useState({
    channelId: CHANNEL_ID.ELEME,
    searchKdtId: query.searchKdtId || '',
    channelType: NEW_CHANNEL_TYPE.ELEME,
  });

  // 搜索接口真实采用的字段和参数
  const adapterSearchParams = React.useMemo(() => {
    return {
      ...searchParams,
      channelType: CHANNEL_TYPE.WAIMAITONG,
    };
  }, [searchParams]);

  const handleSearch = values => {
    let newSearchParams = {
      ...searchParams,
      ...values,
    };
    setSearchParams(newSearchParams);
    history.replace(`?${new URLSearchParams(newSearchParams).toString()}`);
  };

  return (
    <div className="v-ele-auth-list">
      <Filter
        storeParams={{ orgCategorys: JSON.stringify([ORG_CATEGORY.STORE]) }}
        exportParams={getExportParams(adapterSearchParams)}
        showExport
        useBatch
        onSearch={handleSearch}
      />
      <Table
        ref={tableRef}
        useBatch
        columns={getColumns({ tableRef })}
        searchParams={adapterSearchParams}
        channelType={CHANNEL_TYPE.WAIMAITONG}
        channelId={CHANNEL_ID.ELEME}
        request={params => api.getWaiMaiTongList(params)}
      />
    </div>
  );
};

export default List;
