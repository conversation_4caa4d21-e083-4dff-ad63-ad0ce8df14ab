import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON><PERSON>er,
  BlockLoading,
  Button,
  Form,
  FormContext,
  FormStrategy,
  Notify,
  Dialog,
  FormRadioGroupField,
  FormCheckboxGroupField,
  FormNumberInputField,
  FormSelectField,
  Radio,
  Checkbox,
  Pop,
  Icon,
  Drawer,
  Validators,
} from 'zent';

// Import styles for the edit drawer
import './edit-drawer.scss';

import { isRetailSingleStore, SALE_CHANNEL } from '@youzan/utils-shop';
import { debounce, find, mapValues, omit } from 'lodash';

import {
  GoodsUpdateRuleField,
  StoreOperatingStatus,
  SynchronizationOfBusinessHours,
} from '../../component/handle-dialog';

import {
  AllowOpenInvoiceStatus,
  EsynchronizationOfBusinessHours,
  GoodsUpdateRuleItem,
  StoreHandleConfig,
  StoreHandleItems,
  YouzanGoodsUpdateRuleItem,
  EStoreOperatingStatus,
  EItemSyncRange,
  EAutoPublishSync,
} from '../../config';

import { ChannelCode } from '../../config.type';
import { PlanAllocationStatus, PlanStockOptions } from '../../constant';
import api from './api';

const { isSupportMultiChannel } = _global;

// Constants for sync configuration keys
const ExtSyncConfigKeys = [
  StoreHandleConfig.HqTemplateInfoSync,
  StoreHandleConfig.OutItemInfoSync,
] as const;

// Field definitions to avoid repetition
const BASIC_INFO_FIELDS = [
  YouzanGoodsUpdateRuleItem.title,
  YouzanGoodsUpdateRuleItem.barcode,
  YouzanGoodsUpdateRuleItem.skuName,
  YouzanGoodsUpdateRuleItem.category,
  YouzanGoodsUpdateRuleItem.prop,
  YouzanGoodsUpdateRuleItem.picture,
  YouzanGoodsUpdateRuleItem.group,
  YouzanGoodsUpdateRuleItem.sellPoint,
  YouzanGoodsUpdateRuleItem.minimumPurchaseNum,
];

const PRICE_INFO_FIELDS = [
  YouzanGoodsUpdateRuleItem.price,
  YouzanGoodsUpdateRuleItem.packingCharge,
];

const ALL_BASIC_AND_PRICE_FIELDS = [...BASIC_INFO_FIELDS, ...PRICE_INFO_FIELDS];

// Helper functions from edit page - Task 13: Implement initExtSyncConfigs function (same as edit page)
function initExtSyncConfigs(extConfigs: {
  hqTemplateInfoSync: string;
  outItemInfoSync: string;
  outItemTypeSync: number;
}): {
  hqTemplateInfoSync: string[];
  outItemInfoSync: string[];
} {
  if (!extConfigs) {
    return {
      hqTemplateInfoSync: [],
      outItemInfoSync: [],
    };
  }

  const initialValue = ExtSyncConfigKeys.reduce((prev, syncKey) => {
    const info = extConfigs[syncKey] ? JSON.parse(extConfigs[syncKey]) : {};
    prev[syncKey] = Object.keys(info).filter(key => info[key]);
    return prev;
  }, {}) as {
    hqTemplateInfoSync: string[];
    outItemInfoSync: string[];
  };

  const initOutItemInfoSync = (origin: string[]): string[] => {
    const num = Number(extConfigs.outItemTypeSync);
    // 0 代表不选择主图
    if (num === 0) {
      return Object.values(omit(YouzanGoodsUpdateRuleItem, 'picture'));
    }
    // 1 代表全选
    if (num === 1) {
      return Object.values(YouzanGoodsUpdateRuleItem);
    }

    // 2 代表新流程，兼容老的逻辑，这个 outItemInfoSync 的枚举已经废弃不用了
    if (num === 2) {
      return origin;
    }
    return Object.values(YouzanGoodsUpdateRuleItem);
  };

  return {
    ...initialValue,
    outItemInfoSync: initOutItemInfoSync(initialValue.outItemInfoSync),
  };
}

function formatExtSyncConfigs4Submit(
  data: {
    hqTemplateInfoSync: string[];
    outItemInfoSync: string[];
  },
  allKeys: Record<string, Record<string, string>>,
): {
  hqTemplateInfoSync?: string;
  outItemInfoSync?: string;
} {
  const submitValue = ExtSyncConfigKeys.reduce<{
    hqTemplateInfoSync?: Record<string, number>;
    outItemInfoSync?: Record<string, number>;
  }>((prev, syncKey) => {
    const item = data[syncKey];
    if (!item) {
      return prev;
    }
    prev[syncKey] = (Object.values(allKeys[syncKey]) as string[]).reduce((prev, value) => {
      prev[value] = item.includes(value) ? 1 : 0;
      return prev;
    }, {});
    return prev;
  }, {});
  return {
    ...mapValues(submitValue, value => {
      return JSON.stringify(value);
    }),
    [StoreHandleConfig.OutItemTypeSync]: 2,
  };
}

// ItemSyncRangeField component with validation (requirement 5.3)
const ItemSyncRangeField: React.FC<{ disabled?: boolean }> = ({ disabled = false }) => (
  <FormRadioGroupField
    name={StoreHandleConfig.itemSyncRange}
    label={`${StoreHandleItems[StoreHandleConfig.itemSyncRange]}：`}
    required
    helpDesc={<p className="tip">更新范围仅对已经绑定的外卖平台商品生效</p>}
    props={{ disabled }}
    validators={[Validators.required('更新范围不能为空')]}
  >
    <Radio value={EItemSyncRange.partial}>指定部分信息</Radio>
    <Radio value={EItemSyncRange.all}>全部信息</Radio>
    <Radio value={EItemSyncRange.bindOnly}>仅绑定商品，不更新信息</Radio>
  </FormRadioGroupField>
);

// StockSyncRulesField component with updated text and conditional validation
const StockSyncRulesField: React.FC<{ disabled?: boolean, form: any }> = ({ disabled = false }) => {
  const outItemInfoSync = (Form.useFieldValue(StoreHandleConfig.OutItemInfoSync) as string[]) || [];
  const itemSyncRange = Form.useFieldValue(StoreHandleConfig.itemSyncRange) as string;
  
  const stockNumSelected = outItemInfoSync.includes(YouzanGoodsUpdateRuleItem.stockNum);
  const showSalesInfo = itemSyncRange !== EItemSyncRange.bindOnly;
  const showStockSyncRules = stockNumSelected && showSalesInfo;

  if (!showStockSyncRules) {
    return null;
  }
  return (
    <FormRadioGroupField
      name={StoreHandleConfig.stockRealTimeSync}
      label="库存同步规则："
      required
      props={{ disabled }}
      defaultValue="15"
    >
      <Radio value="15">
        当商品增加库存，或可售库存≤15个时，实时同步
        <div className="tip">
          适合日库存固定/有限商家（如面包/商超便利等业态）
        </div>
      </Radio>
      <Radio value="50">
        当商品增加库存，或可售库存≤50个时，实时同步
        <div className="tip">
          适合日库存充足/无限商家（如正餐西餐、咖啡茶饮、仅生日蛋糕等业态）
        </div>
      </Radio>
    </FormRadioGroupField>
  );
};

// AutoPublishSyncField component with validation (requirement 5.3)
const AutoPublishSyncField: React.FC<{ disabled?: boolean }> = ({ disabled = false }) => (
  <FormRadioGroupField
    name={StoreHandleConfig.itemAutoPublishSync}
    label={`${StoreHandleItems[StoreHandleConfig.itemAutoPublishSync]}：`}
    required
    props={{ disabled }}
    validators={[Validators.required('发布设置不能为空')]}
  >
    <Radio value={EAutoPublishSync.auto}>自动发布创建到外卖平台</Radio>
    <Radio value={EAutoPublishSync.manual}>手动同步创建到外卖平台</Radio>
  </FormRadioGroupField>
);

// Custom OrderInvoiceSwitchField component for drawer with disabled support
const DrawerOrderInvoiceSwitchField: React.FC<{ disabled?: boolean }> = ({ disabled = false }) => (
  <FormRadioGroupField
    name={StoreHandleConfig.OrderInvoiceSwitch}
    label={`${StoreHandleItems[StoreHandleConfig.OrderInvoiceSwitch]}：`}
    className="allow-open-invoice-label"
    helpDesc={
      <p className="tip">
        开启后，外卖渠道订单，可以通过扫小票"开票二维码"进行开票；注：请务必保证"有赞商品和外卖渠道商品做好关联，否则也无法成功开票"。
      </p>
    }
    props={{ disabled }}
  >
    <Radio value={AllowOpenInvoiceStatus.on}>开启</Radio>
    <Radio value={AllowOpenInvoiceStatus.off}>关闭</Radio>
  </FormRadioGroupField>
);

// Custom InventorySynchronizationRatio component for drawer with disabled support and comprehensive validation
const DrawerInventorySynchronizationRatio: React.FC<{ disabled?: boolean }> = ({ disabled = false }) => (
  <>
    <FormNumberInputField
      name={StoreHandleConfig.StockInventoryRatio}
      label={`${StoreHandleItems[StoreHandleConfig.StockInventoryRatio]}：`}
      required
      props={{
        addonAfter: '%',
        min: 1,
        max: 100,
        disabled,
      }}
      validators={[
        Validators.required('实物可售库存比例值不能为空'),
        (value: number | null) => {
          if (value !== null && (value < 1 || value > 100)) {
            return { name: 'range', message: '实物可售库存比例必须在1-100%之间' };
          }
          return null;
        }
      ]}
    />
    <FormSelectField
      name={StoreHandleConfig.PlanStockInventoryRatio}
      label={`${StoreHandleItems[StoreHandleConfig.PlanStockInventoryRatio]}：`}
      props={{
        options: PlanStockOptions,
        width: 200,
        disabled,
      }}
      validators={[Validators.required('计划可售库存比例值不能为空')]}
      required
      helpDesc={
        <div>
          <p className="tip">
            设置外部渠道店铺同步库存的比例，外部渠道店铺的库存=实物可售库存*共享比例 +
            计划可售库存*共享比例，取整数。
          </p>
          <p className="tip">
            例如：设置实物可售库存为80%，计划可售库存比例为不共享（即共享比例0%），则每次共享到该渠道的库存=实物可售库存*80%
            + 计划可售库存*0%，直至可售库存为0，减少同时下单超卖情况发生。
          </p>
        </div>
      }
    />
  </>
);

// ProductSyncRulesField component - redesigned with flat structure like "更新范围"
const ProductSyncRulesField: React.FC<{ 
  isMeiTuanShangou?: boolean;
  isMeiTuan?: boolean;
  disabled?: boolean;
  form: any;
}> = ({ 
  isMeiTuan = false, 
  disabled = false,
  form,
}) => {
  const onCheckboxChange =debounce(() => {
    const currentValue = form.getValue()[StoreHandleConfig.OutItemInfoSync];
      const hasBasicOrPriceSelected = ALL_BASIC_AND_PRICE_FIELDS.some(field => currentValue.includes(field));
  
    if (!hasBasicOrPriceSelected) {
      form.patchValue({
        [StoreHandleConfig.itemSyncRange]: EItemSyncRange.partial,
      });
    }
  }, 16);

  const itemSyncRange = Form.useFieldValue('itemSyncRange');
  
  const showSalesInfo = itemSyncRange !== EItemSyncRange.bindOnly;
  const showBasicInfo = itemSyncRange === EItemSyncRange.all;
  const showPriceInfo = itemSyncRange === EItemSyncRange.all;


  return (
    <FormCheckboxGroupField
      name={StoreHandleConfig.OutItemInfoSync}
      label={null}
      props={{ disabled }}
      className="product-sync-rules-field"
      validators={[function(value: string[]) {
        if (!showSalesInfo) {
          return null;
        }
        if (showBasicInfo && !value.some(field => ALL_BASIC_AND_PRICE_FIELDS.includes(field as YouzanGoodsUpdateRuleItem))) {
          return { name: 'required', message: '必选' };
        }
        if (!showBasicInfo && !value.some(field => [YouzanGoodsUpdateRuleItem.takeUpOrDown, YouzanGoodsUpdateRuleItem.stockNum].includes(field as YouzanGoodsUpdateRuleItem))) {
          return { name: 'required', message: '必选' };
        }
        return null;
      }]}
    >
      {showSalesInfo && (
        <div>
          <div className="checkbox-group-container">
            <div className="checkbox-group-label">销售信息：</div>
            <div className="checkbox-group-list">
              <Checkbox disabled={disabled} value={YouzanGoodsUpdateRuleItem.takeUpOrDown}>销售状态（上下架）</Checkbox>
              <Checkbox disabled={disabled} value={YouzanGoodsUpdateRuleItem.stockNum}>库存</Checkbox>
            </div>
          </div>
          <p className="tip" style={{marginLeft: 168}}>
            仅饿了么外卖和京东外卖渠道，支持同步销售状态
          </p>
        </div>
      )}
      
      {showBasicInfo && (
        <div className="checkbox-group-container">
          <div className="checkbox-group-label">基础信息：</div>
          <div className="checkbox-group-list">
            <div onClick={onCheckboxChange}>
              <Checkbox disabled={disabled} value={YouzanGoodsUpdateRuleItem.title}>商品名称</Checkbox>
            </div>
            <div onClick={onCheckboxChange}>
              <Checkbox disabled={disabled} value={YouzanGoodsUpdateRuleItem.skuName}>规格名称</Checkbox>
            </div>
            <div onClick={onCheckboxChange}>
              <Checkbox disabled={disabled} value={YouzanGoodsUpdateRuleItem.barcode}>商品/规格条码</Checkbox>
            </div>
            <div onClick={onCheckboxChange}>
              <Checkbox disabled={disabled} value={YouzanGoodsUpdateRuleItem.category}>商品类目</Checkbox>
            </div>
            <div onClick={onCheckboxChange}>
              <Checkbox disabled={disabled} value={YouzanGoodsUpdateRuleItem.prop}>
                属性{isMeiTuan ? '（不含加价属性）' : ''}
                <Pop trigger="hover" content={isMeiTuan ? "不支持小料自定义，有赞加价属性=美团外卖小料" : '不含加价属性，只对免费属性起作用'}>
                  <Icon
                    className="help-icon"
                    type="help-circle"
                  />
                </Pop>
              </Checkbox>
            </div>
            <div onClick={onCheckboxChange}>
              <Checkbox disabled={disabled} value={YouzanGoodsUpdateRuleItem.picture}>商品主图</Checkbox>
            </div>
            <div onClick={onCheckboxChange}>
              <Checkbox disabled={disabled} value={YouzanGoodsUpdateRuleItem.group}>
                商品分组
                <Pop trigger="hover" content="商品与分组的关联关系">
                  <Icon
                    className="help-icon"
                    type="help-circle"
                  />
                </Pop>
              </Checkbox>
            </div>
            <div onClick={onCheckboxChange}>
              <Checkbox disabled={disabled} value={YouzanGoodsUpdateRuleItem.sellPoint}>商品描述</Checkbox>
            </div>
            <div onClick={onCheckboxChange}>
              <Checkbox disabled={disabled} value={YouzanGoodsUpdateRuleItem.minimumPurchaseNum}>最小起购量</Checkbox>
            </div>
          </div>
        </div>
      )}
      
      {showPriceInfo && (
        <div className="checkbox-group-container">
          <div className="checkbox-group-label">价格信息：</div>
          <div className="checkbox-group-list">
            <div onClick={onCheckboxChange}>
              <Checkbox disabled={disabled} value={YouzanGoodsUpdateRuleItem.price}>价格</Checkbox>
            </div>
            <div onClick={onCheckboxChange}>
              <Checkbox disabled={disabled} value={YouzanGoodsUpdateRuleItem.packingCharge}>商品打包费</Checkbox>
            </div>
          </div>
        </div>
      )}
    </FormCheckboxGroupField>
  );
};

// Component interface
interface EditDrawerProps {
  visible: boolean;
  onClose: () => void;
  kdtId?: string | number;
  channelId?: string | number;
  accountType?: string | number;
  onSuccess?: () => void;
  mode?: 'edit' | 'preview'; // Support for edit and preview modes
}

/**
 * Task 16: Preview Mode Implementation Complete
 * 
 * Preview mode functionality implemented with the following features:
 * - Mode control logic: isPreviewMode = mode === 'preview' (requirement 6.1)
 * - All form fields disabled in preview mode via fieldProps.disabled (requirement 6.2)
 * - Drawer title changes: "查看渠道设置" vs "编辑渠道设置" (requirement 6.4)
 * - Footer buttons: "关闭" button in preview mode, "保存" button hidden (requirement 6.3)
 * - Conditional display logic works normally in preview mode (requirement 6.5)
 * - All form interactions (onChange handlers) are disabled in preview mode
 */
const EditDrawer: React.FC<EditDrawerProps> = ({
  visible,
  onClose,
  kdtId,
  channelId,
  accountType,
  onSuccess,
  mode = 'edit'
}) => {
  // Early return if required props are missing when drawer is visible
  if (visible && (!kdtId || !channelId || !accountType)) {
    console.warn('EditDrawer: Missing required props when visible', { kdtId, channelId, accountType });
    return null;
  }

  // Form management using Form.useForm(FormStrategy.View)
  // Task 15: Form validation rules are defined at field level
  const form = Form.useForm(FormStrategy.View);

  // Loading state management
  const [loading, setLoading] = useState(false);

  // Task 16: Preview mode implementation
  // Mode control - preview mode disables all form fields (requirement 6.1, 6.2)
  const isPreviewMode = mode === 'preview';

  // Field props for disabling in preview mode or during loading (requirement 6.2)
  const fieldProps = {
    disabled: isPreviewMode || loading,
    form,
  };

  // Reuse edit page conditional display logic
  const showFormElement = [
    ChannelCode.meiTuan,
    ChannelCode.meiTuanShangou,
    ChannelCode.jdTakeout,
  ].includes(Number(accountType));

  const canShowSetting = Number(channelId!) !== SALE_CHANNEL.E_LE_ME || !isSupportMultiChannel;

  const isMeiTuanShangou = Number(accountType!) === ChannelCode.meiTuanShangou;
  const isMeiTuan = Number(accountType!) === ChannelCode.meiTuan;
  const isJdTakeout = Number(accountType!) === ChannelCode.jdTakeout;

  /**
   * Get initial store properties - reuse edit page logic
   * Task 13: Implements data loading and initialization logic
   * - Reuses api.getStoreProperty function from edit page
   * - Implements initExtSyncConfigs function (same as edit page)
   * - Adds inventory sharing ratio data loading (non-multi-channel only)
   * - Sets default values for new fields
   * - Ensures takeaway invoice setting defaults to enabled
   * - Uses same initialization mode as edit page
   */
  const getStoreProperty = React.useCallback(async () => {
    try {
      setLoading(true);
      
      // Reuse edit page's api.getStoreProperty function
      const [data] = await Promise.all([
        api.getStoreProperty(Number(kdtId!), Number(channelId!)),
      ]);
      
      // Add inventory sharing ratio data loading (only for non-multi-channel)
      const percentList = !isSupportMultiChannel
        ? await api.queryChannelStockAllocation({
            storeId: Number(kdtId!),
            channelId: Number(channelId!),
          })
        : [];

      // Initialize form values using same pattern as edit page
      const initializeValue = {
        // Store module fields
        [StoreHandleConfig.storeOperatingStatus]: data.openStatus,
        [StoreHandleConfig.synchronizationOfBusinessHours]: data.syncBusinessHours,
        
        // Stock sync rules field
        [StoreHandleConfig.stockRealTimeSync]: `${+data.extConfigs.stockThreshold <= 15 ? 15 : 50}`,
        
        // Ensure takeaway invoice setting defaults to enabled (requirement 5.2)
        [StoreHandleConfig.OrderInvoiceSwitch]:
          data.extConfigs.allowOpenInvoice ?? AllowOpenInvoiceStatus.on,
        
        // Set default values for new fields (requirement 5.1)
        [StoreHandleConfig.itemSyncRange]: data.extConfigs.itemSyncRange || EItemSyncRange.all,
        [StoreHandleConfig.itemAutoPublishSync]: data.extConfigs.itemAutoPublishSync || EAutoPublishSync.manual,
        
        // Reuse existing sync configuration initialization using initExtSyncConfigs function
        ...initExtSyncConfigs(data.extConfigs),
      };

      // Initialize inventory sharing ratio data (only for non-multi-channel) - requirements 3.15, 3.16, 3.17
      if (!isSupportMultiChannel) {
        const stockData = find(percentList, percentItem => percentItem.channelId === Number(channelId!));
        if (stockData) {
          initializeValue[StoreHandleConfig.StockInventoryRatio] = stockData.stockAllocationPercent;
          const planValue = stockData.planAllocationPercent
            ? PlanAllocationStatus.SameWithStock
            : PlanAllocationStatus.NotShare;
          initializeValue[StoreHandleConfig.PlanStockInventoryRatio] = PlanStockOptions.find(
            item => item.key === planValue,
          );
        }
      }

      // Debug log to match edit page behavior (removed for production)

      // Initialize form with all values using same mode as edit page
      form.initialize(initializeValue);
      setLoading(false);
    } catch (error: unknown) {
      setLoading(false);
      console.error('Failed to load store properties:', error);
      
      // Enhanced error handling for data loading
      const errorObj = error as { code?: string; message?: string };
      if (errorObj.code === 'NETWORK_ERROR') {
        Notify.error('网络错误，请检查网络连接后重试');
      } else if (errorObj.message) {
        Notify.error(`加载数据失败：${errorObj.message}`);
      } else {
        Notify.error('加载数据失败，请重试');
      }
    }
  }, [kdtId, channelId, form, isSupportMultiChannel]);

  // Load data when drawer becomes visible
  useEffect(() => {
    if (visible) {
      getStoreProperty();
    }
  }, [visible, kdtId, channelId, getStoreProperty]);

  // Handle store operating status change with JD takeout confirmation dialog
  const handleStoreOperatingStateChange = (value: string | number) => {
    if (isJdTakeout && value === EStoreOperatingStatus.off && !isPreviewMode) {
      const { openDialog, closeDialog } = Dialog;
      const id = 'jd_dialog';
      openDialog({
        dialogId: id,
        title: '提示',
        children: <div>修改营业时间会影响所有京东分店</div>,
        footer: (
          <>
            <Button
              onClick={() => {
                form.patchValue({
                  [StoreHandleConfig.storeOperatingStatus]: EStoreOperatingStatus.on,
                });
                closeDialog(id);
              }}
            >
              取消
            </Button>
            <Button type="primary" onClick={() => closeDialog(id)}>
              确定
            </Button>
          </>
        ),
      });
    }
  };

  /**
   * Task 14: Implement data saving logic
   * Task 15: Enhanced with validation error handling
   * - Reuse edit page's api.updateChannelShop function
   * - Implement formatExtSyncConfigs4Submit function (same as edit page)
   * - Add inventory sharing ratio saving logic (using api.updateChannelStockAllocation)
   * - Add new fields to extConfigs
   * - Implement field clearing logic on save:
   *   - "Partial info": Set all basic info and price info fields to 0 (by removing from selected array)
   *   - "Bind only": Set all sales info, basic info and price info fields to 0 (by clearing selected array)
   *   - Utilize formatExtSyncConfigs4Submit function feature: fields not in array automatically set to 0
   * - Ensure same saving logic as edit page
   * - Add comprehensive error handling for validation and network errors
   * Requirements: 5.3, 5.4, 3.4.3, 3.4.4, 3.15, 3.16, 3.17
   */
  const onSubmit = React.useCallback(async () => {
    
    const submitFormValue = form.getValue();
    const {
      [StoreHandleConfig.storeOperatingStatus]: operateStatus,
      [StoreHandleConfig.synchronizationOfBusinessHours]: syncBusinessHours,
      [StoreHandleConfig.StockInventoryRatio]: stockAllocationPercent,
      [StoreHandleConfig.PlanStockInventoryRatio]: planAllocationPercent,
      [StoreHandleConfig.stockRealTimeSync]: stockThreshold,
      [StoreHandleConfig.OrderInvoiceSwitch]: allowOpenInvoice,
      [StoreHandleConfig.itemSyncRange]: itemSyncRange,
      [StoreHandleConfig.itemAutoPublishSync]: itemAutoPublishSync,
    } = submitFormValue;

    // Process form value based on itemSyncRange for field clearing logic (requirements 3.4.3, 3.4.4)
    // Create a properly typed object for formatExtSyncConfigs4Submit
    const syncConfigData: {
      hqTemplateInfoSync: string[];
      outItemInfoSync: string[];
    } = {
      hqTemplateInfoSync: submitFormValue[StoreHandleConfig.HqTemplateInfoSync] || [],
      outItemInfoSync: submitFormValue[StoreHandleConfig.OutItemInfoSync] || [],
    };
    
    if (itemSyncRange === EItemSyncRange.partial) {
      // "Partial info": Set all basic info and price info fields to 0 by removing them from array
      // formatExtSyncConfigs4Submit will automatically set fields not in array to 0
      syncConfigData.outItemInfoSync = syncConfigData.outItemInfoSync.filter(
        (field: string) => !ALL_BASIC_AND_PRICE_FIELDS.includes(field as typeof YouzanGoodsUpdateRuleItem[keyof typeof YouzanGoodsUpdateRuleItem])
      );
      
    } else if (itemSyncRange === EItemSyncRange.bindOnly) {
      // "Bind only": Set all sales, basic and price info fields to 0 by clearing array
      // formatExtSyncConfigs4Submit will automatically set all fields to 0 when array is empty
      syncConfigData.outItemInfoSync = [];
    }

    try {
      setLoading(true);
      const postData = {
          syncBusinessHours,
          operateStatus,
          kdtId: Number(kdtId!),
          channelId: Number(channelId!),
          extConfigs: {
            // Add inventory sharing ratio to extConfigs (requirement 3.15, 3.16, 3.17)
            stockSynPercent: !isSupportMultiChannel ? String(stockAllocationPercent) : undefined,
            stockThreshold,
            allowOpenInvoice,
            // Add new fields to extConfigs (requirement 5.3)
            itemSyncRange,
            itemAutoPublishSync,
            // Use formatExtSyncConfigs4Submit function (same as edit page) with processed data
            ...formatExtSyncConfigs4Submit(syncConfigData, {
              [StoreHandleConfig.HqTemplateInfoSync]: GoodsUpdateRuleItem,
              [StoreHandleConfig.OutItemInfoSync]: YouzanGoodsUpdateRuleItem,
            }),
          },
        };
      console.log('postData', postData);
      return;
      // Reuse edit page's api.updateChannelShop function with same structure
      await Promise.all([
        api.updateChannelShop(postData),
      ]);

      // Handle inventory sharing ratio update (only for non-multi-channel) - requirements 3.15, 3.16, 3.17
      const _planAllocationPercent =
        planAllocationPercent && typeof planAllocationPercent === 'object'
          ? planAllocationPercent.key
          : planAllocationPercent;

      if (!isSupportMultiChannel) {
        // Add inventory sharing ratio saving logic using api.updateChannelStockAllocation
        await api.updateChannelStockAllocation({
          stockAllocationPercent,
          planAllocationPercent:
            _planAllocationPercent === PlanAllocationStatus.SameWithStock
              ? stockAllocationPercent
              : _planAllocationPercent,
          channelId: Array.isArray(channelId!) ? Number(channelId![0]) : Number(channelId!),
          storeId: Array.isArray(kdtId!) ? Number(kdtId![0]) : Number(kdtId!),
        });
      }

      // Ensure same saving logic as edit page (requirement 5.4)
      Notify.success('设置成功');
      setLoading(false);
      
      // Call success callback and close drawer (requirement 5.4)
      if (onSuccess) {
        // onSuccess();
      }
      onClose();
    } catch (error: unknown) {
      setLoading(false);
      console.error('Failed to save settings:', error);
      
      // Enhanced error handling with appropriate error messages
      const errorObj = error as { code?: string; message?: string };
      if (errorObj.code === 'NETWORK_ERROR') {
        Notify.error('网络错误，请检查网络连接后重试');
      } else if (errorObj.code === 'VALIDATION_ERROR') {
        Notify.error('数据验证失败，请检查输入内容');
      } else if (errorObj.message) {
        Notify.error(errorObj.message);
      } else {
        Notify.error('保存失败，请重试');
      }
    }
  }, [channelId, form, kdtId, onSuccess, onClose, isSupportMultiChannel]);

  const handleSubmit = React.useCallback(() => form.submit(), [form]);

  // Task 16: Preview mode footer - different buttons based on mode (requirement 6.3)
  const footerContent = (
    <div className="button-group">
      <Button onClick={onClose}>
        {isPreviewMode ? '关闭' : '取消'} {/* Different button text in preview mode */}
      </Button>
      {!isPreviewMode && ( /* Hide save button in preview mode (requirement 6.3) */
        <Button type="primary" onClick={handleSubmit} loading={loading}>
          保存
        </Button>
      )}
    </div>
  );

  return (
    <div className={`edit-drawer ${isPreviewMode ? 'preview-mode' : ''}`}>
      <Drawer
        visible={visible}
        onClose={onClose}
        title={isPreviewMode ? '查看渠道设置' : '编辑渠道设置'}
        placement="right"
        width={800}
        className="edit-drawer-component"
        footer={footerContent}
      >
        <BlockLoading loading={loading}>
          <Form layout="horizontal" form={form} onSubmit={onSubmit} className='edit-drawer-form'>
            <FormContext.Provider value={{ labelStyle: { flexBasis: 115 } }}>
              {/* Store module - following edit page conditional display logic */}
              {canShowSetting && showFormElement && (
                <div className="form-section">
                  <BlockHeader type="minimum" title="店铺" />
                  <div className="module-content">
                    <StoreOperatingStatus 
                      {...fieldProps}
                      onChange={!isPreviewMode ? handleStoreOperatingStateChange : undefined}
                    />
                    <SynchronizationOfBusinessHours
                      {...fieldProps}
                      disabled={
                        isPreviewMode ||
                        loading ||
                        (isMeiTuanShangou &&
                          form.getValue()[StoreHandleConfig.synchronizationOfBusinessHours] ===
                            EsynchronizationOfBusinessHours.off)
                      }
                    />
                  </div>
                </div>
              )}

              {/* Product sync rules module */}
              <div className="form-section product-sync-rules-section">
                <BlockHeader type="minimum" title="商品同步规则" />
                <div className="module-content">
                  <ItemSyncRangeField {...fieldProps} />
                  <ProductSyncRulesField 
                    {...fieldProps} 
                    isMeiTuanShangou={isMeiTuanShangou} 
                    isMeiTuan={isMeiTuan}
                  />
                  <StockSyncRulesField {...fieldProps} />
                  <AutoPublishSyncField {...fieldProps} />
                  
                  {/* Headquarters update rules - as the last field in product sync rules module */}
                  {!(isJdTakeout && isRetailSingleStore) && (
                    <GoodsUpdateRuleField 
                      {...fieldProps} 
                      isMeiTuanShangou={isMeiTuanShangou} 
                      isJdTakeout={isJdTakeout}
                      label="总部可修改分店外卖商品："
                    />
                  )}
                </div>
              </div>

              {/* Inventory sharing ratio module - only show for non-multi-channel */}
              {canShowSetting && !isSupportMultiChannel && (
                <div className="form-section">
                  <BlockHeader type="minimum" title="库存共享比例" />
                  <div className="module-content">
                    <DrawerInventorySynchronizationRatio {...fieldProps} />
                  </div>
                </div>
              )}

              {/* Takeaway invoice setting module */}
              <div className="form-section">
                <BlockHeader type="minimum" title="外卖开票设置" />
                <div className="module-content">
                  <DrawerOrderInvoiceSwitchField {...fieldProps} />
                </div>
              </div>
            </FormContext.Provider>
          </Form>
        </BlockLoading>
      </Drawer>
    </div>
  );
};

export default EditDrawer;