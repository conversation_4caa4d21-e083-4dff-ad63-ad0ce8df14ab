import { Router, API, Inject } from '@youzan/assets-route-plugin';
import BaseController from './BaseController';
import TiktokShopService from '../../../services/api/channels/tiktok/TiktokShopService';

interface ITiktokQrCodePayload {
  url: string;
  width?: number;
  path?: string;
  businessType: number;
}
@Router('tiktok-shelf')
class QRCodeController extends BaseController {
  @Inject()
  private readonly tiktokShopService!: TiktokShopService;

  private async requestTiktokQrCode(payload: ITiktokQrCodePayload) {
    const { path, width, businessType } = payload;
    const res = await this.tiktokShopService.getTiktokQrCode({
      kdtId: this.ctx.kdtId,
      path: path || 'pages/common/blank-page/index',
      width: width || 280,
      businessType,
    });
    return {
      qrCodeUrl: typeof res === 'string' ? `data:image/png;base64,${res}` : res,
    };
  }

  /**
   * 获取小程序二维码 - 对外提供
   */
  @API('GET', 'qr-code.json')
  public async getTiktokQrCode() {
    const { request } = this.ctx;
    const { page = '', width = 280, businessType = 1 } = request.query;
    try {
      await this.checkIsBind();
    } catch (e) {
      if (e && e.errorContent) {
        return this.ctx.json(e.errorContent.code, e.errorContent.msg);
      }
      throw e;
    }

    const result = await this.requestTiktokQrCode({
      url: page,
      width,
      businessType,
    });
    return this.ctx.json(0, 'ok', result);
  }

  /**
   * 获取小程序二维码 - 对内使用
   */
  @API('GET', 'qr-code-internally.json')
  public async getTiktokQrCodeInternally() {
    const { request } = this.ctx;
    const { page = '', width = 280, businessType = 1 } = request.query;
    const result = await this.requestTiktokQrCode({
      path: page,
      width,
      businessType,
      url: '',
    });
    await this.ctx.json(0, 'ok', result);
  }
}

export = QRCodeController;
