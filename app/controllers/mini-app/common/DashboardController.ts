import { Router, Index, Metadata, API, Inject } from '@youzan/assets-route-plugin';
import { checkRetailSingleStore, checkWscSingleStore } from '@youzan/utils-shop';
import { PureObject } from '@youzan/wsc-pc-base/definitions/core';
import BaseController from '../BaseController';
import WhitelistService from '@youzan/wsc-pc-base/app/services/common/WhitelistService';
import GrayReleaseService from '@youzan/wsc-pc-base/app/services/common/GrayReleaseService';
import MpVersionService from '../../../services/api/channels/apps/MpVersionService';
import KsMiniAppService from '../../../services/api/channels/apps/KsMiniAppService';
import MarketRemoteService from '../../../services/api/yop/MarketRemoteService';
import BBSPostService from '../../../services/api/ebiz/mall/gemini/BBSPostService';
import UnifiedCertQueryService from '../../../services/api/pay/customer/cert/UnifiedCertQueryService';
import UnifiedCertQueryService1 from '../../../services/api/pay/unified/cert/UnifiedCertQueryService';
import XiaohongshuHomepageService from '../../../services/api/ebiz/video/channels/uc/XiaohongshuHomepageService';
import XiaohongshuChannelConsoleService from '../../../services/api/ebiz/video/channels/item/xhs/XiaohongshuChannelConsoleService';
import TiktokUnderwritingService from '../../../services/api/channels/tiktok/TiktokUnderwritingService';
import TiktokAccountInfoService from '../../../services/api/channels/tiktok/TiktokAccountInfoService';
import TiktokShopService from '../../../services/api/channels/tiktok/TiktokShopService';
import ThirdQualCertQueryService from '../../../services/api/pay/unified/cert/qualification/ThirdQualCertQueryService';
import AbilityReadService from '../../../services/api/shopcenter/shopprod/api/AbilityReadService';
import ShopConfigReadService from '../../../services/api/shopcenter/shopconfig/ShopConfigReadService';
import ShopContactService from '../../../services/api/shopcenter/shop/ShopContactService';
import XhsShopOpenService from '../../../services/api/ebiz/video/channels/uc/XhsShopOpenService';
import WeappIcpFilingService from '../../../services/api/ebiz/video/channels/uc/WeappIcpFilingService';
import { ShopAbilityUtil } from '@youzan/plugin-shop-ability';
import ShopThirdChannelService from '../../../services/api/channels/omni-channel/ShopThirdChannelService';
import channelMap from '../../../constants/miniAppChannelMap';
import { ChangingStatus, ValidStatus, mainBodyAuditStatus } from '../../../constants/thirdQualCert';
import { settleConfigKey } from '../../../constants/xhsLocalLife';

@Router(':channelName')
class DashboardController extends BaseController {
  @Inject()
  private readonly XiaohongshuChannelConsoleService!: XiaohongshuChannelConsoleService;

  @Inject()
  private readonly mpVersionService!: MpVersionService;

  @Inject()
  private readonly KsMiniAppService!: KsMiniAppService;

  @Inject()
  private readonly bbsPostService!: BBSPostService;

  @Inject()
  private readonly marketRemoteService!: MarketRemoteService;

  @Inject()
  public readonly UnifiedCertQueryService!: UnifiedCertQueryService;

  @Inject()
  private readonly UnifiedCertQueryService1!: UnifiedCertQueryService1;

  @Inject()
  private readonly XiaohongshuHomepageService!: XiaohongshuHomepageService;

  @Inject()
  private readonly TiktokUnderwritingService!: TiktokUnderwritingService;

  @Inject()
  private readonly TiktokAccountInfoService!: TiktokAccountInfoService;

  @Inject()
  private readonly TiktokShopService!: TiktokShopService;

  @Inject()
  private readonly ThirdQualCertQueryService!: ThirdQualCertQueryService;

  @Inject()
  private readonly abilityReadService!: AbilityReadService;

  @Inject()
  public readonly shopConfigReadService!: ShopConfigReadService;

  @Inject()
  public readonly ShopContactService!: ShopContactService;

  @Inject()
  private readonly shopThirdChannelService!: ShopThirdChannelService;

  @Inject()
  private readonly xhsShopOpenService!: XhsShopOpenService;

  @Inject()
  private readonly whitelistService!: WhitelistService;

  @Inject()
  private readonly grayReleaseService!: GrayReleaseService;

  @Inject()
  private readonly weappIcpFilingService!: WeappIcpFilingService;

  /**
   * 授权页
   */
  @Index(['dashboard', 'settings/dashboard', 'xhs-independent-dashboard'])
  @Metadata('INDEX')
  async getIndexHtml() {
    const { ctx } = this;
    const { kdtId, request } = ctx;

    // 小红书独立版本判断
    const hasXhsIndependentAbility = await new ShopAbilityUtil(ctx).checkAbilityValid({
      keys: ['xhs_independent_ability'],
      kdtId: ctx.kdtId,
    });
    if (hasXhsIndependentAbility && ctx.url.indexOf('xhs-independent') < 0) {
      this.ctx.redirect('/v4/channel/xhs/xhs-independent-dashboard');
      return;
    }
    // @ts-ignore
    const { accountType, bundleId, businessType = 1, id } = ctx.getState('channelMeta');
    await new ShopAbilityUtil(ctx).set2022MakeUpAbilityToGlobal();
    ctx.setState('subtitle', '概况');
    const bindInfo = await this.getBindInfo();
    const shopInfo = ctx.getState('shopInfo');
    const isRetailSingleStore = checkRetailSingleStore(shopInfo);
    let vInfo;
    const vInfoParam = {
      accountType,
      businessType, // 所有渠道统一加了个 businessType
      kdtId,
      bundleId,
    };
    if (id === 'ks') {
      vInfo = this.KsMiniAppService.getMpVersion(vInfoParam);
    } else {
      vInfo = this.mpVersionService.getMpVersion(vInfoParam);
    }
    const [
      versionInfo,
      xhsAbilityInfo,
      alipayAbilityInfo,
      dyAbilityInfo,
      dySxtAbilityInfo,
      shopVersion,
      mainBodyInfo,
      AuthenticationInfo,
      isDecoInGray,
      settleConfig,
      tradeAbilityPath,
    ] = await Promise.all([
      vInfo,
      this.abilityReadService.queryShopAbilityInfo(kdtId, 'xhs_ability'),
      this.abilityReadService.queryShopAbilityInfo(kdtId, 'mini_alipay_ability'),
      this.abilityReadService.queryShopAbilityInfo(kdtId, 'dy_mini_app_ability'),
      this.abilityReadService.queryShopAbilityInfo(kdtId, 'douyin_wish_group_buy_ability'),
      this.abilityReadService.queryShopProdVersions(kdtId),
      this.TiktokUnderwritingService.queryUnifiedCertConsistence({ kdtId, businessType: 7 }),
      this.ThirdQualCertQueryService.queryQualCertValidAndChanging({
        sourceId: `${kdtId}`,
        sourceIdType: 'KDT_ID',
      }),
      this.grayReleaseService.isInGrayReleaseByKdtId('brand-template-new', kdtId),
      this.shopConfigReadService.queryShopConfig(ctx.kdtId, settleConfigKey).catch(() => ({
        value: '0',
      })),
      this.getApolloConfigData({
        appId: 'wsc-pc-channel',
        namespace: 'application',
        key: 'xhs-trade-switch',
      }),
      // ⬇️ 以下不需要返回值
      this.checkXhsLocalLife(),
      this.xhsShopOpenService.generatePrincipalWatermark({ kdtId }),
    ]);

    // 店铺付费情况
    const defaultPayVersion = -1;
    let version = defaultPayVersion;
    try {
      const { version: rVersion } = await this.marketRemoteService.getServerInfo(kdtId);
      version = rVersion;
    } catch (error) {
      version = defaultPayVersion;
    }

    const mainBodyAudit: { isConsistence: boolean; status?: mainBodyAuditStatus } = {
      isConsistence: mainBodyInfo?.consistence as boolean,
    };
    if (AuthenticationInfo?.changing?.status === ChangingStatus.process) {
      mainBodyAudit.status = mainBodyAuditStatus.process;
    } else if (AuthenticationInfo?.changing?.status === ChangingStatus.fail) {
      mainBodyAudit.status = mainBodyAuditStatus.fail;
    } else if (AuthenticationInfo?.valid?.status === ValidStatus.success) {
      mainBodyAudit.status = mainBodyAuditStatus.success;
      // 后端只能拿到有赞的主体，拿不到资产验证后的主题，判断审核通过就视为一致。
      mainBodyAudit.isConsistence = true;
    } else {
      mainBodyAudit.status = mainBodyAuditStatus.canApply;
    }

    const pageData: PureObject = {
      tradeAbilityPath,
      bindInfo,
      versionInfo,
      shopPurchaseInfo: { version },
      mainBodyAudit,
      dy: {
        abilityInfo: dyAbilityInfo,
        enable: [1].includes(dyAbilityInfo?.abilityStatus),
      },
      'dy-sxt': {
        abilityInfo: dySxtAbilityInfo,
        enable: [1].includes(dySxtAbilityInfo?.abilityStatus),
      },
      xhs: {
        abilityInfo: xhsAbilityInfo,
        // 小红书渠道，wsc和零售公用一个能力判断
        enable: [1].includes(xhsAbilityInfo?.abilityStatus),
      },
      alipay: {
        abilityInfo: alipayAbilityInfo,
        enable: isRetailSingleStore ? [1].includes(alipayAbilityInfo?.abilityStatus) : true,
      },
      ks: {
        abilityInfo: xhsAbilityInfo,
        enable: [1].includes(xhsAbilityInfo?.abilityStatus),
      },
    };

    if (isRetailSingleStore) {
      const isBaseVersion =
        shopVersion &&
        shopVersion.length &&
        shopVersion.find((i: { lifecycleStatus: string }) => i.lifecycleStatus === 'valid')
          ?.versionCode === 'base_version';
      pageData.xhs.needUpgradeVersion = isBaseVersion;
      // 抖音新增基础版判断
      pageData.dy.isBaseVersion = isBaseVersion;
    }

    ctx.setGlobal('isDecoInGray', isDecoInGray);
    ctx.setGlobal('pageData', pageData);
    ctx.setGlobal('settleType', settleConfig.value);

    const released =
      bindInfo && versionInfo?.releaseVersion && request.query?.isAuthFail !== 'true';

    const shopOpenTask = await this.xhsShopOpenService.queryTasks({
      kdtId,
    });

    const APPROVED_CODE = 2;
    // 已经授权 没有走代开店
    const alreadyAudit =
      bindInfo && (!shopOpenTask || shopOpenTask?.authAudit?.applyStatus !== APPROVED_CODE);

    // 小红书页 会在授权之后，就跳转到概况页
    if (bindInfo && ctx.params?.channelName === 'xhs') {
      // const isSame = await this.isSameXhsPrincipalName();
      if ((released || alreadyAudit) && request.query?.forceOpenShop !== 'true') {
        // 线上已有版本
        await this.initXHS(pageData, released);
        return;
      }
    }
    // 抖音点单初始化
    if (+businessType === channelMap['tiktok-shelf'].businessType) {
      const isOpenFusion = await this.isOpenFusion();
      // 融合版本由于版本号没有在版本列表里面，所以versionInfo?.releaseVersion是为空的，这里就根据releaseTime来判断,不为空代表发过版本
      const tiktokShelfReleased = isOpenFusion
        ? bindInfo && versionInfo.releaseTime && request.query?.isAuthFail !== 'true'
        : released;
      await this.initTiktokShelf(pageData, tiktokShelfReleased, businessType);
      return;
    }
    // 抖音卡券初始化
    if (+businessType === channelMap['tiktok-coupon'].businessType) {
      await this.initTiktokCoupon(pageData, released, businessType);
      return;
    }
    // 走抖音的判断，抖音不仅需要发版，还需要开通用户能力
    if (accountType === 21) {
      await this.initTiktok(pageData, released, businessType);
      return;
    }

    // 快手渠道仅支持微商城单店和零售单店
    if (accountType === 22) {
      if (!(checkWscSingleStore(shopInfo) || checkRetailSingleStore(shopInfo))) {
        this.ctx.json(400, 'error', '店铺类型不支持');
        return;
      }
    }

    if (released && request.query?.forceOpenShop !== 'true') {
      await ctx.render('mini-app/common/dashboard/index.html');
    } else {
      await ctx.render('mini-app/common/settings/dashboard.html');
    }
  }

  async initXHS(pageData = {}, released: boolean) {
    const { ctx } = this;
    const { kdtId } = ctx;
    const [homeData, goodsRes, xhsBasicInfo] = await Promise.all([
      await this.XiaohongshuHomepageService.getXhsHomePageReplicableInfo({ kdtId }),
      // 取一个数据就行了
      await this.XiaohongshuChannelConsoleService.searchGoods({
        kdtId,
        pageSize: 1,
        page: 1,
        // 小红书审核通过的
        channelItemMark: 'xhs_available',
      }),
      await this.getXhsMiniAppAccountInfo(),
    ]);

    const { replicable, h5Replicable, wechatReplicable, pageId, isRenovated } = homeData;

    ctx.setGlobal('pageData', {
      ...pageData,
      released,
      featurePageId: pageId,
      canCopy: replicable,
      // 装修已完成
      isRenovated,
      supportWeappIndexPage: wechatReplicable,
      supportWebIndexPage: h5Replicable,
      // 商品已推广
      goodsPromoted: goodsRes?.items?.length > 0,
      xhsBasicInfo,
    });

    // 跳转到概况页
    await ctx.render('mini-app/common/dashboard/index.html');
  }

  /**
   * 抖音小程序初始化
   */
  async initTiktok(pageData: PureObject, released: boolean, businessType: number) {
    const { ctx } = this;
    const { userId } = ctx;
    const rootKdtId = this.getRootKdtId();
    // 进件状态查询
    const underwriting = pageData.bindInfo
      ? await this.TiktokUnderwritingService.queryMchStatus({
          kdtId: rootKdtId,
          adminId: userId,
        })
      : { mchStatus: 'UN_COMMIT' };

    // 小程序基础信息处理，授权页需要一个小程序名称，用户能力依赖 appid
    const basicInfo = pageData.bindInfo
      ? await this.TiktokAccountInfoService.getBasicInfo({
          kdtId: rootKdtId as number,
          businessType,
        })
      : {};

    // 用户手机号能力查询，依赖上面的 getBasicInfo 的 appid
    const userPhoneAbility = released
      ? await this.TiktokShopService.queryGetPhoneAbility({
          kdtId: rootKdtId,
          appId: basicInfo.appId,
          businessType,
        })
      : { status: 0 };

    const tikTokReleased =
      released && underwriting.mchStatus === 'SUCCESS' && userPhoneAbility.status === 4;

    ctx.setGlobal('pageData', {
      ...pageData,
      underwriting,
      basicInfoData: { name: basicInfo?.name },
      userPhoneAbility,
    });
    if (tikTokReleased) {
      await ctx.render('mini-app/common/dashboard/index.html');
    } else {
      await ctx.render('mini-app/common/settings/dashboard.html');
    }
  }

  async initTiktokShelf(pageData: PureObject, released: boolean, businessType: number) {
    const { ctx } = this;
    const { userId } = ctx;
    const rootKdtId = this.getRootKdtId();
    const channelConfig = channelMap['tiktok-shelf'];
    const underwriting = pageData.bindInfo
      ? await this.TiktokUnderwritingService.queryMchStatus({
          kdtId: rootKdtId,
          adminId: userId,
          businessType,
        })
      : { mchStatus: 'UN_COMMIT' };

    // 如果商户号非未提交状态，则 必有抖音来客信息
    if (pageData.bindInfo) {
      const channelInfo = await this.shopThirdChannelService.getShopChannelAccount({
        kdtId: rootKdtId,
        channelAccountType: channelConfig.channelAccountType,
        channelId: channelConfig.channelId,
      });
      if (channelInfo && channelInfo[0]) {
        underwriting.channelAccountValue = channelInfo[0]?.channelAccountValue ?? '';
      }
    }

    // 小程序基础信息处理，授权页需要一个小程序名称，用户能力依赖 appid
    const basicInfo = pageData.bindInfo
      ? await this.TiktokAccountInfoService.getBasicInfo({
          kdtId: rootKdtId as number,
          businessType,
        })
      : {};

    const tikTokReleased = released && underwriting.mchStatus === 'SUCCESS';

    ctx.setGlobal('pageData', {
      ...pageData,
      underwriting,
      basicInfoData: { name: basicInfo?.name },
    });
    if (tikTokReleased) {
      await ctx.render('mini-app/common/dashboard/index.html');
    } else {
      await ctx.render('mini-app/common/settings/dashboard.html');
    }
  }

  async initTiktokCoupon(pageData: PureObject, released: boolean, businessType: number) {
    const { ctx } = this;
    const { userId } = ctx;
    const rootKdtId = this.getRootKdtId();
    const channelConfig = channelMap['tiktok-coupon'];
    const underwriting = pageData.bindInfo
      ? await this.TiktokUnderwritingService.queryMchStatus({
          kdtId: rootKdtId,
          adminId: userId,
          businessType,
        })
      : { mchStatus: 'UN_COMMIT' };

    // 如果商户号非未提交状态，则 必有抖音来客信息
    if (pageData.bindInfo) {
      const channelInfo = await this.shopThirdChannelService.getShopChannelAccount({
        kdtId: rootKdtId,
        channelAccountType: channelConfig.channelAccountType,
        channelId: channelConfig.channelId,
      });
      if (channelInfo && channelInfo[0]) {
        underwriting.channelAccountValue = channelInfo[0]?.channelAccountValue ?? '';
      }
    }

    // 小程序基础信息处理，授权页需要一个小程序名称，用户能力依赖 appid
    const basicInfo = pageData.bindInfo
      ? await this.TiktokAccountInfoService.getBasicInfo({
          kdtId: rootKdtId as number,
          businessType,
        })
      : {};

    // 用户手机号能力查询，依赖上面的 getBasicInfo 的 appid
    // 返回的status含义：0：默认值, 1：可申请,2：不可申请,3：申请中,4：申请通过,5：申请失败,6：能力关闭
    const userPhoneAbility = released
      ? await this.TiktokShopService.queryGetPhoneAbility({
          kdtId: rootKdtId,
          businessType,
        })
      : { status: 0 };

    const tikTokReleased =
      released && underwriting.mchStatus === 'SUCCESS' && userPhoneAbility.status === 4;

    ctx.setGlobal('pageData', {
      ...pageData,
      underwriting,
      basicInfoData: { name: basicInfo?.name },
      userPhoneAbility,
    });
    if (tikTokReleased) {
      await ctx.render('mini-app/common/dashboard/index.html');
    } else {
      await ctx.render('mini-app/common/settings/dashboard.html');
    }
  }

  /**
   * 获取小程序动态帖子(五篇)
   */
  @API('GET', 'bbs_post.json')
  public async getWeappBBSPostJson() {
    const result = await this.getWeappBBS();
    return this.ctx.json(0, 'ok', result || []);
  }

  /**
   * 获取优秀案例
   */
  @API('GET', 'excellent_example.json')
  public async getExcellentExampleJson() {
    const result = await this.getApolloExcellentExample();
    return this.ctx.json(0, 'ok', result || []);
  }

  /**
   * 获取广告banner
   */
  @API('GET', 'apollo_publicity_banner.json')
  public async getAdBannerJson() {
    const result = await this.getApolloAdBanner();
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 获取apollo公告配置
   */
  @API('GET', 'apollo-announcement.json')
  public async getApolloAnnouncementJson() {
    const result = await this.getApolloAnnouncement();
    await this.ctx.json(0, 'ok', result);
  }

  /**
   * 获取小程序动态帖子(五篇)
   */
  public getWeappBBS() {
    const { ctx } = this;
    const { adGroupId } = ctx.getState('channelMeta' as any);
    return this.bbsPostService.listMiniPost({
      materialGroupId: adGroupId,
    });
  }

  /**
   * 获取统一认证查询主体信息
   */
  @API('GET', 'principal-msg.json')
  public async queryPrincipalMsg() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const result = await this.UnifiedCertQueryService.queryPrincipalMsg({
      sourceId: `${kdtId}`,
      sourceIdType: 'KDT_ID',
      unifiedCertType: 1,
    });
    await this.ctx.json(0, 'ok', result);
  }

  /**
   * 获取有赞授权品牌信息
   */
  @API('GET', 'brand-msg.json')
  public async queryBrandMsg() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const result = await this.UnifiedCertQueryService1.queryBrandMsg({
      sourceId: `${kdtId}`,
      sourceIdType: 'KDT_ID',
    });
    await this.ctx.json(0, 'ok', result);
  }

  /**
   * 获取店铺认证白名单
   */
  @API('GET', 'xhs-verify-whiteList.json')
  public async getXhsVerifyWhiteList() {
    const result = await this.getApolloConfigData({
      appId: 'wsc-pc-channel',
      namespace: 'wsc-pc-channel.whitelist',
      key: 'xhs.verify',
    });
    await this.ctx.json(0, 'ok', result);
  }

  /**
   * 获取店铺认证白名单
   */
  @API('GET', 'is-xhs-verify.json')
  public async getXhsVerify() {
    const { kdtId } = this.ctx;
    const result = await this.whitelistService.isInWhitelist('xhs_verify', `${kdtId}`);
    this.ctx.json(0, 'ok', result);
  }

  /**
   * 复制接口
   */
  @API('GET', 'copy-xhs-homepage.json')
  public async copyXhsHomePage() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { query } = ctx;
    const { platformType } = query;

    this.validator.required(platformType, '参数错误');

    const result = await this.XiaohongshuHomepageService.copyXhsHomePage({
      kdtId,
      platformType,
    });

    await this.ctx.json(0, 'ok', result);
  }

  /**
   * 查询微信小程序备案状态
   */
  @API('GET', 'get-icp-entrance-info.json')
  async getIcpEntranceInfo() {
    const { ctx } = this;
    const { kdtId } = ctx;

    let result = {
      msg: '未绑定微信小程序',
      status: 999,
    };

    const mpBindResp = await this.channelCoreAccountService.queryMpBindInfoByKdtId({
      businessType: 1,
      accountType: 2,
      externalId: kdtId,
    });

    if (mpBindResp?.mpId) {
      result = await this.weappIcpFilingService.getIcpEntranceInfo({
        kdtId,
      });
    }

    this.ctx.json(0, 'ok', result);
  }

  /**
   * 查询店铺售后客服配置
   */
  @API('GET', 'shop-wsc-web-im.json')
  public async getShopWscWebIm() {
    const { ctx } = this;
    const { kdtId } = ctx;
    this.validator.required(kdtId, '参数 kdtId 不能为空');

    // @ts-ignore
    const afterSaleContact = await new ShopContactService(ctx).queryAfterSaleContact(kdtId);

    return ctx.json(0, 'ok', afterSaleContact);
  }

  protected async isOpenFusion() {
    // 0 - 老版本，1 - 新版本
    try {
      const {
        configs: { tiktok_miniapp_mix_version_switch: isOpenFusion = 0 },
      } = await this.shopConfigReadService.queryShopConfigs(this.ctx.kdtId, [
        'tiktok_miniapp_mix_version_switch',
      ]);

      return Number(isOpenFusion) === 1;
    } catch (error) {}

    return false;
  }
}

export = DashboardController;
