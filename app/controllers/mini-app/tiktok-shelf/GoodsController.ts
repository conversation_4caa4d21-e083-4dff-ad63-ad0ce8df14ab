import { Router, Index, API, Inject } from '@youzan/assets-route-plugin';

import CommonBasicInfoController from '../common/BasicInfoController';
import CategoryBackService from '../../../services/api/mall/item/CategoryBackService';
import ShopThirdChannelService from '../../../services/api/channels/omni-channel/ShopThirdChannelService';
import ThirdPartyChannelShopReadService from '../../../services/api/channels/omni-channel/ThirdPartyChannelShopReadService';
import ChannelCoreAccountService from '../../../services/api/channels/channel/core/ChannelCoreAccountService';
import { isNumber } from 'lodash';

const TiktokChannel = 401;

@Router('tiktok-shelf/goods')
class GoodsController extends CommonBasicInfoController {
  @Inject()
  public readonly categoryBackService!: CategoryBackService;

  @Inject()
  public readonly shopThirdChannelService!: ShopThirdChannelService;

  @Inject()
  public readonly thirdPartyChannelShopReadService!: ThirdPartyChannelShopReadService;

  @Inject()
  public readonly channelCoreAccountService!: ChannelCoreAccountService;

  public async init() {
    this.channelName = 'tiktok-shelf';
    super.init();
  }

  @Index('')
  public async getGoodsHtml() {
    const { ctx } = this;
    const { kdtId, userId } = this.ctx;

    // 判断抖音小程序授权
    let isBindTiktokShelf = false;
    try {
      // 授权账号
      const accountRes = await this.shopThirdChannelService.getShopChannelAccount({
        channelAccountType: 1,
        channelId: TiktokChannel,
        kdtId,
      });
      const channelRes = await this.channelCoreAccountService.queryMpBindInfoByKdtId({
        businessType: 7,
        accountType: 21,
        externalId: kdtId,
      });
      // 授权渠道
      if (Array.isArray(accountRes)) {
        isBindTiktokShelf =
          accountRes.some(({ channelId }) => channelId === TiktokChannel) && channelRes?.mpId;
      }
    } catch (error) {}

    ctx.setGlobal('isBindTiktokShelf', isBindTiktokShelf);

    // 判断poi绑定店铺>0
    let isBindPoiShop = false;
    try {
      const res = await this.thirdPartyChannelShopReadService.queryChannelShops({
        adminId: userId,
        channelIds: [TiktokChannel],
        kdtId,
        pageSize: 1,
        pageNum: 1,
      });
      if (isNumber(res?.total)) {
        isBindPoiShop = res.total > 0;
      }
    } catch (error) {}

    ctx.setGlobal('isBindPoiShop', isBindPoiShop);

    ctx.setGlobal('isOpenFusion', await this.isOpenFusion());
    ctx.setState('subtitle', '抖音商品');
    // const showRetailShelfOrderMode = this.isInWhiteListByApollo('show_retail_shelf_order_mode');
    ctx.setGlobal('showRetailShelfOrderMode', true);
    return ctx.render('mini-app/tiktok-shelf/goods/index.html');
  }

  /**
   * 查询渠道类目树状
   */
  @API('GET', 'out-category-tree.json')
  public async findOutCategoryTreeByChannel() {
    const { kdtId, request } = this.ctx;
    const { channel } = request.query;
    const res = await this.categoryBackService.findOutCategoryTreeByChannel({
      kdtId,
      channel,
    });
    return this.ctx.json(0, 'success', res);
  }
}

export = GoodsController;
