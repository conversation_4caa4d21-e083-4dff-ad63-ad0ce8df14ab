import { Router, Index, Metadata, Inject, API } from '@youzan/assets-route-plugin';
import lodash from 'lodash';
import BaseController from './BaseController';
import WechatChannelService from '../../../services/api/mall/item/WechatChannelService';
import WechatChannelConsoleService from '../../../services/api/ebiz/video/channels/item/wechat/WechatChannelConsoleService';
import ShopConfigReadService from '../../../services/api/shopcenter/shopconfig/ShopConfigReadService';
import OrderSearchService from '../../../services/api/ebiz/mall/trade/seller/OrderSearchService';
import FastOrderConfigService from '../../../services/api/ebiz/video/channels/uc/FastOrderConfigService';
import { EnumAccessInfoItem } from '../../../services/api/channels/channel/core/WeappAccountService';
import VideoAppChannelInfoService from '../../../services/api/ebiz/video/channels/uc/VideoAppChannelInfoService';
import WechatChannelToolService from '../../../services/api/ebiz/video/channels/item/wechat/WechatChannelToolService';
import { EnumSceneGroupId } from '../../../services/api/channels/apps/WeappTradeModuleService';
import { wxVideoGrayAuthSwitch } from '../../../utils/grayUtils';
import { compareVersion } from '../../../utils/compare-version';

@Router('wechat-video/basic-info')
class EntryController extends BaseController {
  @Inject()
  public readonly wechatChannelService!: WechatChannelService;

  @Inject()
  public readonly wechatChannelConsoleService!: WechatChannelConsoleService;

  @Inject()
  public readonly shopConfigReadService!: ShopConfigReadService;

  @Inject()
  public readonly FastOrderConfigService!: FastOrderConfigService;

  @Inject()
  private readonly orderSearchService!: OrderSearchService;

  @Inject()
  private readonly VideoAppChannelInfoService!: VideoAppChannelInfoService;

  @Inject()
  private readonly WechatChannelToolService!: WechatChannelToolService;

  public async init() {
    this.channelName = 'wechat-video';
    super.init();
  }

  isInWhiteList() {
    const isInWhiteListShift = this.isInWhiteListByApollo('basic-info', {
      appId: 'wsc-pc-wxvideo',
      namespace: 'wsc-pc-wxvideo.migrate',
      useRootKdtId: true,
    });

    if (isInWhiteListShift) {
      const url = '//www.youzan.com/v4/wxvideo/basic-info/index';
      this.ctx.redirect(url);
    }

    return isInWhiteListShift;
  }

  @Index('')
  @Metadata('INDEX')
  async getBasicInfoHtml() {
    const { ctx } = this;

    if (this.isInWhiteList()) {
      return;
    }

    if (!wxVideoGrayAuthSwitch(ctx)) {
      return;
    }

    const [weixinVideoInfo, weappVersionInfo] = await Promise.all([
      this.getWeixinVideoInfo(),
      this.getVersionCheckInfo().catch(() => null),
    ]);

    if (weappVersionInfo) {
      const isFastbuyWeappVersionOk = this.versionJudgeFastBuy(weappVersionInfo.curVersion);
      ctx.setGlobal('pageData', {
        isFastbuyWeappVersionOk,
      });
    }

    if (weixinVideoInfo) {
      return ctx.render('video-app/wechat-video/basic-info.html');
    }

    return ctx.redirect('/v4/channel/video/wechat-video/premise-check?failType=1');
  }

  /**
   * 判断版本极速下单页 小程序版本是否可用
   */
  protected versionJudgeFastBuy(curVersion: string): boolean {
    if (!curVersion) {
      return false;
    }
    // 微商城店铺 当前小程序版本小于2.90.6
    if (compareVersion(curVersion, '2.90.6') === -1) {
      return false;
    }
    return true;
  }

  /**
   * 交易组件启用检查
   */
  @API('GET', 'trade-module-enable-check-result.json')
  public async getTradeModuleEnableCheckResult() {
    const result = await this.getTradeModuleEnableCheck();
    return this.ctx.json(0, 'ok', result || {});
  }

  /**
   * 交易组件启用
   */
  @API('POST', 'finish-all-access-info.json')
  public async finishAllAccessInfo() {
    const { ctx } = this;
    const { kdtId, userId } = ctx;
    const { accessInfoItemList } = ctx.request.body;
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');

    this.validator.required(accessInfoItemList, 'accessInfoItemList 不能为空');

    const finishRes = await this.weappAccountService.finishAllAccessInfo({
      kdtId,
      accountType,
      businessType: 1,
      userId,
      accessInfoItemList,
    });

    if (!finishRes) {
      return this.ctx.json(0, '完成接入任务失败', 'finish_fail');
    }

    // 临时兼容 更新TradeModuleTicketStatus
    const result = await this.weappAccountService.updateTradeModuleTicketStatus({
      kdtId,
      ticketStatus: 1,
      accountType,
      businessType: 1,
      userId,
    });

    if (!result) {
      return this.ctx.json(0, '更新ticke失败', 'update_ticket_fail');
    }

    return this.ctx.json(0, 'ok', 'finish_success');
  }

  /**
   * 更新ticket status
   */
  @API('POST', 'update-trade-module-ticket-status.json')
  public async updateTradeModuleTicketStatus() {
    const { ctx } = this;
    const { kdtId, userId } = ctx;
    const { ticketStatus } = ctx.request.body;
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');

    this.validator.required(ticketStatus, 'ticketStatus 不能为空');

    const result = await this.weappAccountService.updateTradeModuleTicketStatus({
      kdtId,
      ticketStatus,
      accountType,
      businessType: 1,
      userId,
    });
    return this.ctx.json(0, 'ok', result);
  }

  /**
   * 获取微信视频号信息
   */
  @API('GET', 'wechat-video-basic-info.json')
  public async getWechatVideoBasicInfo() {
    const [wechatVideoInfo = {}, tradeModuleAccessInfo] = await Promise.all([
      this.getWeixinVideoInfo(),
      this.getTradeModuleInfo(EnumSceneGroupId.WXVIDEO_OR_WXPUBLIC),
    ]);

    const { isEnable, isOpen, updatedAt } = tradeModuleAccessInfo;

    const result = {
      // 微信视频号信息
      wechatVideoInfo: {
        ...wechatVideoInfo,
        videoId: wechatVideoInfo.videoChannelId,
      },
      // 交易组件是否启用
      isEnableWechatModule: isEnable,
      // 交易组件是否开通
      isOpenWechatModule: isOpen,
      // 交易组件开通时间
      openTime: updatedAt || '',
    };
    return this.ctx.json(0, 'ok', result);
  }

  /**
   * 更新添加推广员状态
   */
  @API('POST', 'update-officer-open-status.json')
  public async updateOfficerOpenStatus() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { extensionOfficerOpenStatus } = ctx.request.body;

    let wechatVideoInfo = {};
    try {
      wechatVideoInfo = await this.getWeixinVideoInfo();
    } catch (error) {
      ctx.toLog('更新添加推广员状态 获取基础信息失败', error);
    }

    const result = await this.weappAccountService.saveWeixinVideoInfo({
      kdtId,
      ...(wechatVideoInfo || {}),
      extensionOfficerOpenStatus,
    });
    return this.ctx.json(0, 'ok', result);
  }

  /**
   * 获取视频号极速下单开关
   */
  @API('GET', 'get-fast-order-switch.json')
  public async getFastOrderSwitch() {
    const canWxVideoSync = await this.FastOrderConfigService.getFastOrderSwitch({
      kdtId: this.ctx.kdtId,
    });
    return this.ctx.json(0, 'success', canWxVideoSync);
  }

  /**
   * 修改视频号极速下单开关
   */
  @API('POST', 'update-fast-order-switch.json')
  public async updateFastOrderSwitch() {
    const { kdtId, request } = this.ctx;
    const { status } = request.body;
    const canWxVideoSync = await this.FastOrderConfigService.updateFastOrderSwitch({
      kdtId,
      status,
    });
    return this.ctx.json(0, 'success', canWxVideoSync);
  }

  /**
   * 保存微信视频号信息
   */
  @API('POST', 'save-wechat-video-info.json')
  public async saveWeixinVideoInfo() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { videoName, videoExtensionUrl, videoId, extensionOfficerOpenStatus } = ctx.request.body;

    this.validator.required(videoName, '视频号名称不能为空');
    this.validator.required(videoExtensionUrl, '视频号名片不能为空');
    this.validator.required(videoId, '视频号id不能为空');

    const result = await this.weappAccountService.saveWeixinVideoInfo({
      kdtId,
      videoName,
      videoExtensionUrl,
      videoChannelId: videoId,
      extensionOfficerOpenStatus,
    });
    return this.ctx.json(0, 'ok', result);
  }

  async getTradeModuleEnableCheck() {
    const [versionCheckInfo, tradeModuleStatusRealTimeInfo] = await Promise.all([
      this.getVersionCheckInfo(),
      this.getTradeModuleStatus(),
    ]);
    const { isLastVersion, curVersion, lastVersion } = versionCheckInfo;
    const isUsableVersion = this.versionJudge(curVersion);
    const { accessInfo } = tradeModuleStatusRealTimeInfo || {};
    // 上传商品并审核成功
    const spuAuditSuccess = lodash.get(accessInfo, 'spuAuditSuccess', 0) === 1;
    // 发起一笔订单并支付成功
    const payOrderSuccess = lodash.get(accessInfo, 'payOrderSuccess', 0) === 1;
    // 物流接口调用成功
    const sendDeliverySuccess = lodash.get(accessInfo, 'sendDeliverySuccess', 0) === 1;
    // 售后接口调用成功
    const addAfterSaleSuccess = lodash.get(accessInfo, 'addAfterSaleSuccess', 0) === 1;

    // 商品接口调试完成
    const spuAuditFinished = lodash.get(accessInfo, 'spuAuditFinished', 0) === 1;
    // 订单接口调试完成
    const payOrderFinished = lodash.get(accessInfo, 'payOrderFinished', 0) === 1;
    // 物流接口调试完成
    const sendDeliveryFinished = lodash.get(accessInfo, 'sendDeliveryFinished', 0) === 1;
    // 售后接口调试完成
    const addAfterSaleFinished = lodash.get(accessInfo, 'addAfterSaleFinished', 0) === 1;
    // 发版完成
    const deployWxaFinished = lodash.get(accessInfo, 'deployWxaFinished', 0) === 1;
    // 测试完成
    const testApiFinished = lodash.get(accessInfo, 'testApiFinished', 0) === 1;
    // 未完成的检查项
    const needAccessItemList = [
      spuAuditFinished || EnumAccessInfoItem.spuAuditFinished,
      payOrderFinished || EnumAccessInfoItem.payOrderFinished,
      sendDeliveryFinished || EnumAccessInfoItem.sendDeliveryFinished,
      addAfterSaleFinished || EnumAccessInfoItem.addAfterSaleFinished,
      testApiFinished || EnumAccessInfoItem.testApiFinished,
      deployWxaFinished || EnumAccessInfoItem.deployWxaFinished,
    ].filter(item => item !== true);

    return {
      isUsableVersion,
      isLastVersion,
      curVersion,
      lastVersion,
      spuAuditSuccess,
      payOrderSuccess,
      sendDeliverySuccess,
      addAfterSaleSuccess,
      needAccessItemList,
      spuAuditFinished,
      payOrderFinished,
      sendDeliveryFinished,
      addAfterSaleFinished,
    };
  }

  getWxReviewSkuCount() {
    const { ctx } = this;
    const { kdtId } = ctx;

    return this.wechatChannelConsoleService
      .listWechatChannelPaged({
        kdtId,
        auditStatus: 2,
        auditPlatform: 2,
      })
      .then(data => {
        return data.totalCount;
      });
  }

  getWxVideoOrderCount() {
    const { ctx } = this;
    const { kdtId } = ctx;
    return this.orderSearchService.countOrder({
      kdtId,
      syncWxSuccess: true,
    });
  }

  /**
   * 获取承诺函数据
   */
  @API('GET', 'get-commitment-letter.json')
  public async getCommitmentLetter() {
    const rootKdtId = lodash.get(this.ctx.getState('shopInfo'), 'rootKdtId', this.ctx.kdtId);
    const res = await this.WechatChannelToolService.getCommitmentLetter(rootKdtId);
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 设置承诺函数据
   */
  @API('POST', 'set-commitment-letter.json')
  public async setCommitmentLetter() {
    const { commitmentLetter } = this.ctx.request.body;
    const rootKdtId = lodash.get(this.ctx.getState('shopInfo'), 'rootKdtId', this.ctx.kdtId);
    const res = await this.VideoAppChannelInfoService.setAppChannelInfo({
      kdtId: rootKdtId,
      channel: 2,
      commitmentLetter,
    });
    return this.ctx.json(0, 'success', res);
  }
}

export = EntryController;
