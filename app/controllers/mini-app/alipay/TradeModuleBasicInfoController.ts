import { Router, Index, API, Inject } from '@youzan/assets-route-plugin';
import ChannelCommonMerchantService from '../../../services/api/channels/channel/core/ChannelCommonMerchantService';
import AlipayTradeModuleOpenService from '../../../services/api/channels/channel/core/AlipayTradeModuleOpenService';
import ChannelCommonCategoryService from '../../../services/api/channels/channel/core/ChannelCommonCategoryService';
import ItemQueryService from '../../../services/api/channels/channel/core/ItemQueryService';
import GoodsSelectorService from '../../../services/api/channels/channel/core/GoodsSelectorService';
import ChannelItemQueryService from '../../../services/api/channels/channel/core/ChannelItemQueryService';
import ChannelItemOperateService from '../../../services/api/channels/channel/core/ChannelItemOperateService';
import BaseController from './BaseController';

@Router('alipay/trade-module')
class TradeModuleBasicInfoController extends BaseController {
  @Inject()
  private readonly channelCommonMerchantService!: ChannelCommonMerchantService;

  @Inject()
  private readonly alipayTradeModuleOpenService!: AlipayTradeModuleOpenService;

  @Inject()
  private readonly channelCommonCategoryService!: ChannelCommonCategoryService;

  @Inject()
  private readonly goodsSelectorService!: GoodsSelectorService;

  @Inject()
  private readonly itemQueryService!: ItemQueryService;

  @Inject()
  private readonly channelItemQueryService!: ChannelItemQueryService;

  @Inject()
  private readonly channelItemOperateService!: ChannelItemOperateService;

  @Index('basic-info')
  public async getBaseInfoHtml() {
    const { ctx } = this;
    // if (await this.checkPermission()) {
    const hasPermission = await this.checkPermission();
    if (!hasPermission) {
      return this.ctx.render('mini-app/alipay/trade-module/no-permission');
    }
    // 获取交易组件开通情况
    const tradeModuleInfo = await this.alipayTradeModuleOpenService
      .getTradeModuleInfo({
        kdtId: ctx.kdtId,
        channel: 5,
        queryTaskDetail: false,
      })
      .catch(err => console.log(err));
    const { channelOpenStatus } = tradeModuleInfo;
    if (channelOpenStatus === 2) {
      return ctx.render('mini-app/alipay/trade-module/basic-info.html');
    }
    return ctx.render('mini-app/alipay/trade-module/settings.html');
  }

  @Index('pre-check')
  public async getPreCheckHtml() {
    const { ctx } = this;
    const hasPermission = await this.checkPermission();
    if (!hasPermission) {
      return this.ctx.render('mini-app/alipay/trade-module/no-permission');
    }
    return ctx.render('mini-app/alipay/trade-module/pre-check.html');
  }

  // 检查是否有开通支付组件权限
  public async checkPermission() {
    const { ctx } = this;
    const result = await this.alipayTradeModuleOpenService.checkTradeModuleWhiteList({
      kdtId: ctx.kdtId,
    });
    const hasPermission = result || false;
    return hasPermission;
  }

  // 检查是否有开通支付组件权限
  @API('POST', 'checkTradeModuleWhiteList.json')
  public async checkTradeModuleWhiteList() {
    const { ctx } = this;
    const result = await this.alipayTradeModuleOpenService.checkTradeModuleWhiteList({
      kdtId: ctx.kdtId,
    });
    return this.ctx.json(0, 'ok', result);
  }

  // 获取商户号信息
  @API('POST', 'getChannelMerchant.json')
  public async getChannelMerchant() {
    const { ctx } = this;
    const { queryTaskDetail } = ctx.request.body;
    const result = await this.channelCommonMerchantService.getChannelMerchant({
      channel: 5,
      kdtId: ctx.kdtId,
      queryTaskDetail,
    });
    return this.ctx.json(0, 'ok', result);
  }

  // 修改商户号名称
  @API('POST', 'updateChannelMerchant.json')
  public async updateChannelMerchant() {
    const { ctx } = this;
    const { mchName } = ctx.request.body;
    const result = await this.channelCommonMerchantService.updateChannelMerchant({
      channel: 5,
      kdtId: ctx.kdtId,
      mchName,
    });
    return this.ctx.json(0, 'ok', result);
  }

  // 组件开通信息查询
  @API('POST', 'getTradeModuleInfo.json')
  public async getTradeModuleInfo() {
    const { ctx } = this;
    const { queryTaskDetail } = ctx.request.body;
    const result = await this.alipayTradeModuleOpenService.getTradeModuleInfo({
      channel: 5,
      kdtId: ctx.kdtId,
      queryTaskDetail,
    });
    return this.ctx.json(0, 'ok', result);
  }

  // 子任务提交执行
  @API('POST', 'submitSubTask.json')
  public async submitSubTask() {
    const { ctx } = this;
    const {
      taskType,
      mchId,
      mchName,
      itemAlias,
      itemName,
      itemId,
      itemKdtId,
      itemOutCatId,
    } = ctx.request.body;
    const result = await this.alipayTradeModuleOpenService.submitSubTask({
      channel: 5,
      kdtId: ctx.kdtId,
      taskType,
      mchId,
      mchName,
      itemAlias,
      itemName,
      itemId,
      itemKdtId,
      itemOutCatId,
    });
    return this.ctx.json(0, 'ok', result);
  }

  // 交易组件开通完成
  @API('POST', 'finishTradeModule.json')
  public async finishTradeModule() {
    const { ctx } = this;
    const result = await this.alipayTradeModuleOpenService.finishTradeModule({
      channel: 5,
      kdtId: ctx.kdtId,
    });
    return this.ctx.json(0, 'ok', result);
  }

  // 查询商家申请的渠道类目列表
  @API('POST', 'queryChannelApplyCategory.json')
  public async queryChannelApplyCategory() {
    const { ctx } = this;
    const { pageSize, pageNum } = ctx.request.body;
    const result = await this.channelCommonCategoryService.queryChannelApplyCategory({
      channel: 5,
      kdtId: ctx.kdtId,
      pageSize,
      pageNum,
    });
    return this.ctx.json(0, 'ok', result);
  }

  // 刷新渠道类目
  @API('POST', 'refreshChannelCategory.json')
  public async refreshChannelCategory() {
    const { ctx } = this;
    const result = await this.channelCommonCategoryService.refreshChannelCategory({
      channel: 5,
      kdtId: ctx.kdtId,
    });
    return this.ctx.json(0, 'ok', result);
  }

  // 查询渠道类目树
  @API('POST', 'queryCategoryTree.json')
  public async queryCategoryTree() {
    const { ctx } = this;
    const result = await this.channelCommonCategoryService.queryCategoryTree({
      channel: 5,
      kdtId: ctx.kdtId,
    });
    return this.ctx.json(0, 'ok', result);
  }

  // 分页查询商品列表
  @API('POST', 'queryGoodsList.json')
  public async queryGoodsList() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { pageSize, page, keyword } = ctx.request.body;
    const result = await this.goodsSelectorService.queryGoodsList({
      goodsChannel: 'ONLINE',
      goodsClusterType: 'ALL',
      keyword,
      pageNo: page,
      pageSize,
      shopId: kdtId,
      umpType: 30020,
    });
    return this.ctx.json(0, 'ok', result);
  }

  // 获取商品详情
  @API('POST', 'getById.json')
  public async getById() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { itemId } = ctx.request.body;
    const result = await this.itemQueryService.getById({
      kdtId,
      itemId: Number(itemId),
    });
    return this.ctx.json(0, 'ok', result);
  }

  // 已提审商品分页列表
  @API('POST', 'listOfPage.json')
  public async listOfPage() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const {
      page = 1,
      pageSize = 10,
      title,
      startCreateTime,
      endCreateTime,
      auditStatus,
      auditStatuses,
      status,
    } = ctx.request.body;
    const result = await this.channelItemQueryService.listOfPage({
      channel: 5,
      kdtId,
      fromApp: 'wsc-h5-aftersales',
      page,
      pageSize,
      title,
      startCreateTime,
      endCreateTime,
      auditStatus,
      auditStatuses,
      status,
    });
    return this.ctx.json(0, 'ok', result);
  }

  // 批量创建渠道商品
  @API('POST', 'batchCreateNonatomic.json')
  public async batchCreateNonatomic() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { items } = ctx.request.body;
    const result = await this.channelItemOperateService.batchCreateNonatomic({
      channel: 5,
      kdtId,
      fromApp: 'wsc-h5-aftersales',
      items,
    });
    return this.ctx.json(0, 'ok', result);
  }

  // 批量重新提审
  @API('POST', 'batchRemand.json')
  public async batchRemand() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { items } = ctx.request.body;
    const result = await this.channelItemOperateService.batchRemand({
      channel: 5,
      kdtId,
      fromApp: 'wsc-h5-aftersales',
      items,
    });
    return this.ctx.json(0, 'ok', result);
  }

  // 重新提审
  @API('POST', 'remand.json')
  public async remand() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { itemId } = ctx.request.body;
    const result = await this.channelItemOperateService.remand({
      channel: 5,
      kdtId,
      fromApp: 'wsc-h5-aftersales',
      itemId,
    });
    return this.ctx.json(0, 'ok', result);
  }

  // 取消提审
  @API('POST', 'delete.json')
  public async delete() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { itemId } = ctx.request.body;
    const result = await this.channelItemOperateService.delete({
      channel: 5,
      kdtId,
      fromApp: 'wsc-h5-aftersales',
      itemId,
    });
    return this.ctx.json(0, 'ok', result);
  }

  // 修改类目
  @API('POST', 'update.json')
  public async update() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { itemId, extra } = ctx.request.body;
    const result = await this.channelItemOperateService.update({
      channel: 5,
      kdtId,
      fromApp: 'wsc-h5-aftersales',
      itemId,
      extra,
    });
    return this.ctx.json(0, 'ok', result);
  }
}

export = TradeModuleBasicInfoController;
