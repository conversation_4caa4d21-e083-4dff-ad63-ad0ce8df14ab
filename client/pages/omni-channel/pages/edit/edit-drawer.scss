/**
 * Edit Drawer Styles
 * 
 * Task 18: Styles and layout for the edit-drawer component
 */

@import 'shared/sass/vars/_colors';
@import 'shared/sass/vars/_font';

// Edit Drawer Styles
.edit-drawer,
.edit-drawer-component {
  // Drawer content styling
  .zent-drawer-content {
    background-color: $theme-stroke-9;
    overflow-y: auto;
    height: calc(100vh - 120px);
    display: flex;
    flex-direction: column;
  }

  .edit-drawer-form {
    padding: 16px;
  }

  // Drawer footer styling
  .zent-drawer-footer {
    padding: 16px 24px;
    background-color: $theme-stroke-9;
    border-top: 1px solid $theme-stroke-6;
    text-align: right;

    .zent-btn + .zent-btn {
      margin-left: 8px;
    }
  }

  // Form layout
  .zent-form {
    .zent-form-control-group {
      margin-bottom: 20px;

      .zent-form-label {
        flex-basis: 120px !important;
        text-align: right;
        padding-right: 16px;
        font-size: $font-size-normal;
        color: $theme-stroke-1;
        font-weight: $font-weight-normal;
        line-height: 32px;
      }

      .zent-form-control {
        flex: 1;

        .zent-input,
        .zent-select,
        .zent-number-input {
          width: 100%;
          max-width: 320px;
        }
      }
    }
  }

  // Special styling for product sync rules module
  .product-sync-rules-section {
    .zent-form-control {
      .zent-form-label {
        flex-basis: 168px !important;
      }
    }
    .product-sync-rules-field {
      .zent-form-label {
        flex-basis: 0 !important;
      }
    }
    .zent-form-error {
      margin-left: 168px;
    }
  }

  .zent-form-label {
    flex-basis: 168px !important;
  }

  // Block header styling
  .zent-block-header {
    padding-bottom: 12px;
    box-shadow: none !important;

    .zent-block-header-title {
      font-size: $font-size-large;
      font-weight: $font-weight-medium;
      color: $theme-stroke-1;
      position: relative;
      padding-left: 0;
    }
  }

  // Module content padding
  .module-content {
    padding-left: 0;
    margin-bottom: 28px;
  }

  // Custom label styling
  .custom-label {
    width: 120px;
    position: absolute;
    left: 0;
    text-align: right;
    height: 32px;
    font-size: $font-size-normal;
    color: $theme-stroke-1;
    font-weight: $font-weight-normal;
    line-height: 32px;
    padding-right: 16px;
  }

  // Checkbox and radio group styling
  .zent-checkbox-group,
  .zent-radio-group {
    .zent-checkbox,
    .zent-radio {
      .zent-checkbox-label,
      .zent-radio-label {
        font-size: $font-size-normal;
        color: $theme-stroke-1;
        line-height: $line-height-normal;
      }
    }
  }

  // Checkbox group layout
  .zent-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .zent-checkbox {
      margin-right: 0;
      flex: 0 0 auto;
    }
  }

  // Checkbox group container for label and list layout
  .checkbox-group-container {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .checkbox-group-label {
      box-sizing: border-box;
      padding-right: 8px;
      flex-shrink: 0;
      width: 168px;
      text-align: right;
      font-size: $font-size-normal;
      color: $theme-stroke-1;
      font-weight: $font-weight-normal;
    }

    .checkbox-group-list {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .zent-checkbox {
        margin-right: 0;
        flex: 0 0 auto;
      }
    }
  }

  // Help text and tips styling
  .tip {
    font-size: $font-size-small;
    line-height: 18px;
    color: $theme-stroke-3;
    margin-top: 6px;
    margin-bottom: 0;

    &:not(:last-child) {
      margin-bottom: 8px;
    }

    .zent-checkbox + &,
    .zent-radio + & {
      margin-left: 24px;
      margin-top: 4px;
    }
  }

  // Error message styling
  .validation-error {
    margin-left: 176px;
    color: $theme-error-2;
    font-size: $font-size-small;
    margin-top: -12px;
    margin-bottom: 12px;
    line-height: $line-height-small;
  }

  // Threshold input styling
  .threshold {
    margin-left: 3px;
    margin-right: 3px;

    &.zent-input-wrapper {
      display: inline-flex;
    }
  }

  // Preview mode styles
  &.preview-mode {
    .zent-form-control {
      .zent-input[disabled],
      .zent-select[disabled],
      .zent-number-input[disabled] {
        background-color: $theme-stroke-7;
        color: $theme-stroke-3;
        cursor: not-allowed;
      }

      .zent-checkbox[disabled],
      .zent-radio[disabled] {
        .zent-checkbox-label,
        .zent-radio-label {
          color: $theme-stroke-3;
          cursor: not-allowed;
        }
      }
    }

    .zent-checkbox-group,
    .zent-radio-group {
      .zent-checkbox[disabled],
      .zent-radio[disabled] {
        opacity: 0.6;

        .zent-checkbox-inner,
        .zent-radio-inner {
          background-color: $theme-stroke-7;
          border-color: $theme-stroke-5;
        }
      }
    }
  }

  // Loading state styling
  .zent-block-loading {
    .zent-loading {
      background-color: rgba($theme-stroke-9, 0.8);
    }
  }

  // Form sections spacing
  .form-section {
    margin-bottom: 36px;
    border-bottom: 1px solid $theme-stroke-6;

    &:last-child {
      margin-bottom: 20px;
    }
  }

  // Help icon styling
  .help-icon {
    color: $theme-stroke-4;
    margin-left: 4px;
    cursor: help;

    &:hover {
      color: $theme-stroke-3;
    }
  }

  // Button group styling
  .button-group {
    display: flex;
    justify-content: flex-end;
    gap: 12px;

    .zent-btn {
      min-width: 88px;
      height: 36px;
      font-size: $font-size-normal;

      &.zent-btn-primary {
        background-color: $theme-primary-4;
        border-color: $theme-primary-4;

        &:hover {
          background-color: $theme-primary-3;
          border-color: $theme-primary-3;
        }
      }

      &:not(.zent-btn-primary) {
        background-color: $theme-stroke-9;
        border-color: $theme-stroke-5;
        color: $theme-stroke-1;

        &:hover {
          border-color: $theme-primary-4;
          color: $theme-primary-4;
        }
      }
    }
  }

  // Drawer styling
  .zent-drawer {
    z-index: 1050;

    .zent-drawer-header {
      padding: 16px 24px;
      border-bottom: 1px solid $theme-stroke-6;
      background-color: $theme-stroke-9;

      .zent-drawer-title {
        font-size: $font-size-large;
        font-weight: $font-weight-medium;
        color: $theme-stroke-1;
      }

      .zent-drawer-close {
        color: $theme-stroke-3;

        &:hover {
          color: $theme-stroke-1;
        }
      }
    }
  }

  // Form validation styling
  .zent-form-control-group.has-error {
    .zent-form-control {
      .zent-input,
      .zent-select,
      .zent-number-input {
        border-color: $theme-error-2;

        &:focus {
          border-color: $theme-error-2;
          box-shadow: 0 0 4px 0 rgba($theme-error-2, 0.2);
        }
      }
    }

    .zent-form-error-message {
      color: $theme-error-2;
      font-size: $font-size-small;
      margin-top: 4px;
    }
  }

  // Help descriptions
  .zent-form-help-desc {
    margin-top: 4px;

    .tip {
      margin-top: 0;
    }
  }

  // Loading overlay
  .zent-block-loading-mask {
    background-color: rgba($theme-stroke-9, 0.8);
    backdrop-filter: blur(2px);
  }

  // Radio/checkbox options spacing
  .zent-radio-group .zent-radio,
  .zent-checkbox-group .zent-checkbox {
    margin-right: 0;

    &:last-child {
      margin-bottom: 0;
    }

    .tip {
      margin-left: 24px;
      margin-top: 4px;
      color: $theme-stroke-3;
      font-size: $font-size-small;
    }
  }

  // Inline checkboxes
  .inline-checkboxes {
    .zent-checkbox-group {
      display: flex;
      flex-wrap: wrap;
      gap: 12px 20px;

      .zent-checkbox {
        margin-bottom: 0;
        margin-right: 0;
        flex: 0 0 auto;
      }
    }
  }

  // Radio group with descriptions
  .radio-with-description {
    .zent-radio {
      align-items: flex-start;

      .zent-radio-label {
        display: flex;
        flex-direction: column;

        .tip {
          margin-left: 0;
          margin-top: 4px;
        }
      }
    }
  }

  // Focus states
  .zent-input:focus,
  .zent-select:focus,
  .zent-number-input:focus {
    border-color: $theme-primary-4;
    box-shadow: 0 0 4px 0 rgba($theme-primary-4, 0.2);
    outline: none;
  }

  .zent-radio:focus .zent-radio-inner,
  .zent-checkbox:focus .zent-checkbox-inner {
    border-color: $theme-primary-4;
    box-shadow: 0 0 4px 0 rgba($theme-primary-4, 0.2);
  }
}

// Global styles
body.zent-drawer-open {
  overflow: hidden;
}

.zent-drawer-mask {
  background-color: rgba($theme-stroke-1, 0.45);
}
