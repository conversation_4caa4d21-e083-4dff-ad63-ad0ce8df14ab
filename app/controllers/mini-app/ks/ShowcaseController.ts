import { Router, API, Inject } from '@youzan/assets-route-plugin';

import CommonBasicInfoController from '../common/BasicInfoController';
import KsAppletShowcaseService from '../../../services/api/ebiz/video/channels/uc/KsAppletShowcaseService';

@Router('ks/showcase')
class ShowcaseController extends CommonBasicInfoController {
  @Inject()
  private readonly KsAppletShowcaseService!: KsAppletShowcaseService;

  public get baseInfo() {
    const baseInfo = this.getOperatorParams();
    return {
      operatorId: baseInfo.operator.userId,
      operator: baseInfo.operator.nickName,
      fromApp: baseInfo.fromApp,
    };
  }

  public async init() {
    this.channelName = 'ks';
    super.init();
  }

  // 更新快手主页初始化模版
  @API('POST', 'init-homepage-data.json')
  public async initHomepageData() {
    const { ctx } = this;
    const { kdtId } = ctx;

    const res = await this.KsAppletShowcaseService.initHomepageData({
      kdtId,
    });
    return ctx.json(0, 'ok', res);
  }
}

export = ShowcaseController;
