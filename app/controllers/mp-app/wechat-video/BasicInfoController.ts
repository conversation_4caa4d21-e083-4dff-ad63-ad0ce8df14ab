import { Router, Index, API, Inject } from '@youzan/assets-route-plugin';
import formatDate from '@youzan/utils/date/formatDate';
import BaseController from './BaseController';
import CategoryBackService from '../../../services/api/mall/item/CategoryBackService';
import ItemCommonService from '../../../services/api/mall/item/ItemCommonService';
import ItemQueryService from '../../../services/api/mall/item/ItemQueryService';
import WechatChannelService from '../../../services/api/mall/item/WechatChannelService';
import ShopConfigReadService from '../../../services/api/shopcenter/shopconfig/ShopConfigReadService';
import ShopAddressOuterService from '../../../services/api/shopcenter/outer/address/ShopAddressOuterService';
import ProdReadService from '../../../services/api/shopcenter/shopprod/prod/ProdReadService';
import CustomerUnifiedCertQueryService from '../../../services/api/pay/customer/cert/UnifiedCertQueryService';
import PrincipalInfoQueryService, {
  IQueryCompanyTrademarkRes,
} from '../../../services/api/pay/unified/cert/PrincipalInfoQueryService';
import MultiBrandCertQueryV2Service from '../../../services/api/pay/unified/cert/brand/MultiBrandCertQueryV2Service';
import UnifiedCertQueryService, {
  IBrandCertDTO,
  IQueryBrandMsgRes,
} from '../../../services/api/pay/unified/cert/UnifiedCertQueryService';
import PlatformKdtAuditService from '../../../services/api/cps/goods/PlatformKdtAuditService';
import PlatformKdtTaskService from '../../../services/api/cps/goods/PlatformKdtTaskService';
import SelfrunGoodsQueryService from '../../../services/api/cps/goods/SelfrunGoodsQueryService';
import WeappAccountService from '../../../services/api/channels/channel/core/WeappAccountService';
import ChannelCoreAccountService from '../../../services/api/channels/channel/core/ChannelCoreAccountService';
import StorageQiniuReadService, {
  IOldPrivateUrlDTO,
  IRefreshPrivateUrlRes,
} from '../../../services/api/material/materialcenter/StorageQiniuReadService';
import utils from './utils';
import { channelTypeEnum } from '../../../constants/channelMap';

@Router('wechat-video/basic-info')
class BasicInfoController extends BaseController {
  @Inject()
  public readonly categoryBackService!: CategoryBackService;

  @Inject()
  public readonly itemCommonService!: ItemCommonService;

  @Inject()
  public readonly itemQueryService!: ItemQueryService;

  @Inject()
  public readonly wechatChannelService!: WechatChannelService;

  @Inject()
  public readonly shopConfigReadService!: ShopConfigReadService;

  @Inject()
  public readonly shopAddressOuterService!: ShopAddressOuterService;

  @Inject()
  public readonly prodReadService!: ProdReadService;

  @Inject()
  public readonly multiBrandCertQueryV2Service!: MultiBrandCertQueryV2Service;

  @Inject()
  public readonly unifiedCertQueryService!: UnifiedCertQueryService;

  @Inject()
  public readonly principalInfoQueryService!: PrincipalInfoQueryService;

  @Inject()
  public readonly customerUnifiedCertQueryService!: CustomerUnifiedCertQueryService;

  @Inject()
  public readonly platformKdtAuditService!: PlatformKdtAuditService;

  @Inject()
  public readonly platformKdtTaskService!: PlatformKdtTaskService;

  @Inject()
  public readonly weappAccountService!: WeappAccountService;

  @Inject()
  public readonly channelCoreAccountService!: ChannelCoreAccountService;

  @Inject()
  public readonly selfrunGoodsQueryService!: SelfrunGoodsQueryService;

  @Inject()
  public readonly storageQiniuReadService!: StorageQiniuReadService;

  public async init() {
    this.channelName = 'wechat-video';
    super.init();
  }

  @Index('')
  public async getBaseInfoHtml() {
    const { ctx } = this;

    // 老页面直接跳转新页面
    return ctx.redirect('/v4/channel/weapp/trade-module/basic-info');
  }

  isOrganizationCert(principalInfo: any) {
    // 未认证
    if (!principalInfo) {
      return false;
    }
    // 个人主体
    if (principalInfo.principalCertType === 1) {
      return false;
    }

    return true;
  }

  getStoreBasicCheckIsOkWithAllResult(storeBasicCheckResult: any[]) {
    const [principalInfo, wscWebImConfig, shopAddressList, lifecycle] = storeBasicCheckResult;
    if (!this.isOrganizationCert(principalInfo)) {
      return false;
    }

    // 未开通联系客服
    if (!wscWebImConfig || wscWebImConfig.value === '0') {
      return false;
    }
    // 退货地址为空
    if (!shopAddressList || shopAddressList.total <= 0) {
      return false;
    }
    // 店铺到期
    if (
      !lifecycle ||
      !lifecycle.endTime ||
      new Date(lifecycle.endTime).getTime() - new Date().getTime() <= 0
    ) {
      return false;
    }
    return true;
  }

  getStoreBasicCheckResult() {
    const { kdtId } = this.ctx;
    const getPrincipalInfoPromise = this.customerUnifiedCertQueryService
      .queryPrincipalMsg({
        sourceId: `${kdtId}`,
        sourceIdType: 'KDT_ID',
        unifiedCertType: 1,
      })
      .then(res => {
        this.ctx.toLog('查询店铺主体认证信息', !!res);
        return res;
      });
    const getShopWscWebImConfigPromise = this.shopConfigReadService
      .queryShopConfig(kdtId, 'show_wsc_web_im')
      .then(res => {
        this.ctx.toLog('查询店铺客服配置', res);
        return res;
      });
    const getShopAddressListPromise = this.shopAddressOuterService
      .queryShopAddressList({
        kdtId,
        addressTypeValues: [1],
        pageSize: 10,
        pageNum: 1,
      })
      .then(res => {
        this.ctx.toLog('查询店铺退货地址信息', res);
        return res;
      });
    const getShopProdsPromise = this.prodReadService.queryShopProds(kdtId).then(prodLifecycle => {
      this.ctx.toLog('查询店铺服务期', prodLifecycle);
      const prodCode = utils.getProdCode(
        this.ctx.shopType,
        this.ctx.shopRole,
        this.ctx.shopTopic as number,
      );
      return prodLifecycle.filter((item: { prodCode: any }) => item.prodCode === prodCode)[0];
    });
    return Promise.all([
      getPrincipalInfoPromise,
      getShopWscWebImConfigPromise,
      getShopAddressListPromise,
      getShopProdsPromise,
    ]);
  }

  getWeappTradeModuleIsAuth() {
    const { ctx } = this;
    const { kdtId } = ctx;
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    return this.channelCoreAccountService
      .queryChannelAuthInfo({
        accountType,
        businessType: 1,
        kdtId,
      })
      .then(res => {
        if (res && res.authInfoDTOS) {
          const weappTradeAuth =
            res.authInfoDTOS.find((item: { funcId: number }) => item.funcId === 85) || {};
          return weappTradeAuth.funcState === 1;
        }
        return false;
      });
  }

  /**
   * 当前店铺是否在白名单内
   * 若返回数组为空 则代表全量
   */
  public async isInWxModuleWhiteList() {
    const { apolloClient, kdtId } = this.ctx;
    const whitelist = this.channelName
      ? await apolloClient.getConfig({
          appId: 'wsc-pc-channel',
          namespace: `wsc-pc-channel.whitelist`,
          key:
            (this.channelType === channelTypeEnum.miniApp ? '' : `${this.channelType}-`) +
            this.channelName,
        })
      : null;
    if (!whitelist) {
      return false;
    }
    if (whitelist.length === 0) {
      return true;
    }

    return whitelist.includes(Number(kdtId));
  }

  /**
   * 获取有赞授权品牌信息
   */
  @API('GET', 'get-valid-brand-cert.json')
  public async getBrandMsg() {
    const brandMsg = await this.unifiedCertQueryService
      .queryBrandMsg({
        sourceId: `${this.ctx.kdtId}`,
        sourceIdType: 'KDT_ID',
      })
      .then((res: IQueryBrandMsgRes) => {
        return {
          brandCertDTOList:
            (res &&
              res.brandCertDTOList &&
              res.brandCertDTOList.filter(item => item.status === 'PASS')) ||
            [],
        };
      });
    return this.ctx.json(0, 'success', brandMsg);
  }

  /**
   * 获取未设置新类目的商品数量
   */
  @API('GET', 'list-items-page-no-category.json')
  public async getListItemsPagedNoCategory() {
    const res = await this.itemQueryService
      .listItemsPagedNoCategory({
        kdtId: this.ctx.kdtId,
      })
      .then(data => {
        return {
          count: data.count,
        };
      });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 获取商品审核数量
   */
  @API('GET', 'get-sku-review-count.json')
  public async getSkuReviewCount() {
    const { kdtId } = this.ctx;
    const reviewCount = await this.wechatChannelService
      .listWechatChannelPaged({
        kdtId,
      })
      .then(data => {
        return data.totalCount;
      });
    const refuseCount = await this.wechatChannelService
      .listWechatChannelPaged({
        kdtId,
        auditStatus: 3,
      })
      .then(data => {
        return data.totalCount;
      });
    return this.ctx.json(0, 'success', {
      reviewCount,
      refuseCount,
    });
  }

  /**
   * 获取交易组件商品同步状态
   */
  @API('GET', 'weapp-trade-module-apply-key.json')
  public async getWeappTradeModuleKey() {
    const res = await this.shopConfigReadService.queryShopConfig(
      this.ctx.kdtId,
      'weapp_trade_module_status',
    );
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 获取视频号所有三级类目
   */
  @API('GET', 'all-category.json')
  public async getAllCategory() {
    const { kdtId, userId } = this.ctx;
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    const res = await this.weappAccountService.getTradeModuleCategoryTree({
      businessType: 1,
      userId,
      accountType,
      kdtId,
    });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 获取视频号类目上传审核结果
   */
  @API('GET', 'video-category-audit-info.json')
  public async getVideoCategoryAuditInfo() {
    const { kdtId, userId } = this.ctx;
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    const res = await this.weappAccountService.listTradeModuleCategoryAuditInfo({
      businessType: 1,
      userId,
      accountType,
      kdtId,
    });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 获取视频号品牌上传审核结果
   */
  @API('GET', 'video-brand-audit-info.json')
  public async getVideoBrandAuditInfo() {
    const { kdtId, userId } = this.ctx;
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    const res = await this.weappAccountService.listTradeModuleBrandAuditInfo({
      businessType: 1,
      userId,
      accountType,
      kdtId,
    });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 获取交易组件能否开启商品同步
   */
  @API('GET', 'get-can-wx-video-sync.json')
  public async getCanWxVideoSync() {
    const canWxVideoSync = await this.selfrunGoodsQueryService.canWxVideoSync(this.ctx.kdtId);
    return this.ctx.json(0, 'success', canWxVideoSync);
  }

  /**
   * 获取全店商品审核状态
   */
  @API('GET', 'all-sku-review.json')
  public async getAllSkuReviewStatus() {
    const res = await this.platformKdtTaskService.getTaskStatus(this.ctx.kdtId);
    this.ctx.toLog('获取全店商品审核状态', res);
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 开通当前店铺新类目白名单
   */
  @API('POST', 'open-new-category-whitelist.json')
  public async openNewCategoryWhitelist() {
    const res = await this.categoryBackService.openNewCategory({
      kdtId: this.ctx.kdtId,
    });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 商品同步开通配置,并推送商品同步消息
   */
  @API('POST', 'update-trade-module-config.json')
  public async updateTradeModuleConfig() {
    const { kdtId, userId, request } = this.ctx;
    const { status } = request.body;
    this.validator.required(status, 'status 不能为空');
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    const res = await this.weappAccountService.updateTradeModuleConfig({
      businessType: 1,
      userId,
      accountType,
      kdtId,
      status,
    });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 微信小程序申请开通购物组件
   */
  @API('POST', 'apply-weapp-trade-module.json')
  public async applyWeappTradeModule() {
    const { kdtId, userId } = this.ctx;
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    const res = await this.weappAccountService.applyWeappTradeModule({
      businessType: 1,
      userId,
      accountType,
      kdtId,
    });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 检查申请状态
   */
  @API('POST', 'check-apply-trade-module-status.json')
  public async checkApplyTradeModuleStatus() {
    const { kdtId, userId } = this.ctx;
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    const res = await this.weappAccountService.checkApplyTradeModuleStatus({
      businessType: 1,
      userId,
      accountType,
      kdtId,
    });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 申请购物组件类目
   */
  @API('POST', 'apply-trade-module-category.json')
  public async applyTradeModuleCategory() {
    const { kdtId, userId, request } = this.ctx;
    const { categoryList } = request.body;
    this.validator.required(categoryList, 'categoryList 不能为空');
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    const principalInfo = await this.customerUnifiedCertQueryService.queryPrincipalMsg({
      sourceId: `${kdtId}`,
      sourceIdType: 'KDT_ID',
      unifiedCertType: 1,
    });
    this.ctx.toLog('申请购物组件类目获取主体信息', !!principalInfo);
    // 营业执照
    const license =
      principalInfo.merchantOrganizationCertResult.papersType === 10
        ? // 资产没有 type 区分是 营业执照号还是组织机构证代码，需要用或来取，最终解释 @明城
          principalInfo.merchantOrganizationCertResult.businessPhotoUrl ||
          principalInfo.merchantOrganizationCertResult.organizationCodePhotoUrl
        : principalInfo.merchantOrganizationCertResult.creditPhotoUrl;
    const newLicense = await this.storageQiniuReadService
      .refreshPrivateUrl({
        expires: 3600 * 48,
        oldPrivateUrlDTOS: [
          {
            oldPrivateUrl: license.replace('dn-kdt-private.qbox.me', 'pr.yzcdn.cn'),
          },
        ],
      })
      .then(([res]: IRefreshPrivateUrlRes) => (res && res.newPrivateUrl) || '');
    this.ctx.toLog('营业执照替换非私域url', !!newLicense);
    const res = await this.weappAccountService.batchApplyTradeModuleCategory({
      businessType: 1,
      userId,
      accountType,
      kdtId,
      list: categoryList.map((category: any) => ({
        ...category,
        license: newLicense,
        businessType: 1,
        userId,
        accountType,
        kdtId,
      })),
    });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 申请购物组件品牌
   */
  @API('POST', 'apply-trade-module-brand.json')
  public async applyTradeModuleBrand() {
    const { kdtId, userId } = this.ctx;
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    const principalInfo = await this.customerUnifiedCertQueryService.queryPrincipalMsg({
      sourceId: `${kdtId}`,
      sourceIdType: 'KDT_ID',
      unifiedCertType: 1,
    });
    this.ctx.toLog('申请购物组件品牌获取主体信息', !!principalInfo);
    // 营业执照
    const license =
      principalInfo.merchantOrganizationCertResult.papersType === 10
        ? // 资产没有 type 区分是 营业执照号还是组织机构证代码，需要用或来取，最终解释 @明城
          principalInfo.merchantOrganizationCertResult.businessPhotoUrl ||
          principalInfo.merchantOrganizationCertResult.organizationCodePhotoUrl
        : principalInfo.merchantOrganizationCertResult.creditPhotoUrl;
    const newLicense = await this.storageQiniuReadService
      .refreshPrivateUrl({
        expires: 3600 * 48,
        oldPrivateUrlDTOS: [
          {
            oldPrivateUrl: license.replace('dn-kdt-private.qbox.me', 'pr.yzcdn.cn'),
          },
        ],
      })
      .then(([res]: IRefreshPrivateUrlRes) => (res && res.newPrivateUrl) || '');
    this.ctx.toLog('营业执照刷新私有链接', !!newLicense);
    const brandMsg = await this.unifiedCertQueryService.queryBrandMsg({
      sourceId: `${kdtId}`,
      sourceIdType: 'KDT_ID',
      originalPhoto: true,
    });
    this.ctx.toLog('获取品牌信息', brandMsg);

    const brandCertDTOList = (brandMsg && brandMsg.brandCertDTOList) || [];
    const brandCertList = await Promise.all(
      brandCertDTOList.map((brandCertDTO: Partial<IBrandCertDTO>) => {
        const oldPrivateUrlDTOS: IOldPrivateUrlDTO[] = [];
        if (brandCertDTO.authorizationPapers) {
          oldPrivateUrlDTOS.push({
            oldPrivateUrl: brandCertDTO.authorizationPapers.replace(
              'dn-kdt-private.qbox.me',
              'pr.yzcdn.cn',
            ),
          });
        }
        if (brandCertDTO.trademarkPapers) {
          oldPrivateUrlDTOS.push({
            oldPrivateUrl: brandCertDTO.trademarkPapers.replace(
              'dn-kdt-private.qbox.me',
              'pr.yzcdn.cn',
            ),
          });
        }
        return this.storageQiniuReadService.refreshPrivateUrl({
          expires: 3600 * 48,
          oldPrivateUrlDTOS,
        });
      }),
    ).then((urlResList: IRefreshPrivateUrlRes[]) => {
      this.ctx.toLog('品牌信息证照刷新私有链接', !!urlResList);
      return urlResList.map((urlList: IRefreshPrivateUrlRes, index: number) => {
        const brandCertDTO = brandCertDTOList[index];
        const authorizationPapers = brandCertDTO.authorizationPapers
          ? (urlList[0] && urlList[0].newPrivateUrl) || ''
          : '';
        const trademarkPapers = brandCertDTO.trademarkPapers
          ? brandCertDTO.authorizationPapers
            ? (urlList[1] && urlList[1].newPrivateUrl) || ''
            : (urlList[0] && urlList[0].newPrivateUrl) || ''
          : '';
        return {
          ...brandCertDTO,
          authorizationPapers,
          trademarkPapers,
        };
      });
    });

    const brandList: any[] = await Promise.all(
      brandCertList.map((brandCert: any) =>
        this.principalInfoQueryService.queryCompanyTrademark({
          number: brandCert.brandRegisterNo,
          partnerId: 'enable_crm',
        }),
      ),
    ).then((companyBrandInfoList: Array<Partial<IQueryCompanyTrademarkRes>>) => {
      this.ctx.toLog('获取所有品牌启信宝信息', companyBrandInfoList);
      return companyBrandInfoList.map(
        (companyBrandInfo: Partial<IQueryCompanyTrademarkRes>, index: number) => {
          const brandCert = brandCertList[index];
          return {
            unifiedBrandId: brandCert.unifiedBrandId, // 品牌唯一Id
            brandManagementType: brandCert.authorizationLevel,
            brandAuditType: brandCert.trademarkStatus,
            brandWording: brandCert.brandName,
            commodityOriginType: 2, // 默认非进口
            saleAuthorization: brandCert.authorizationPapers ? [brandCert.authorizationPapers] : [],
            trademarkRegistrantNu: brandCert.brandRegisterNo,
            trademarkRegistrationApplication: brandCert.trademarkPapers
              ? [brandCert.trademarkPapers]
              : [],
            trademarkAuthorizationPeriod:
              brandCert.brandStartTime && brandCert.brandEndTime
                ? `${formatDate(new Date(brandCert.brandStartTime), 'YYYY/MM/DD')}-${formatDate(
                    new Date(brandCert.brandEndTime),
                    'YYYY/MM/DD',
                  )}`
                : '',
            trademarkType: brandCert.trademarkCategory,
            trademarkApplicationTime: companyBrandInfo.applyDate
              ? formatDate(new Date(companyBrandInfo.applyDate), 'YYYY-MM-DD HH:mm:ss')
              : companyBrandInfo.applyDate,
            trademarkRegistrationCertificate: brandCert.trademarkPapers
              ? [brandCert.trademarkPapers]
              : [],
            trademarkApplicant: companyBrandInfo.ename,
            trademarkRegistrant: brandCert.brandRegistrant,
            license: newLicense,
            businessType: 1,
            userId,
            accountType,
            kdtId,
          };
        },
      );
    });

    const res = await this.weappAccountService.batchApplyTradeModuleBrand({
      businessType: 1,
      userId,
      accountType,
      kdtId,
      list: brandList,
    });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 获取店铺基本信息检查结果
   */
  @API('GET', 'store-basic-check-result.json')
  public async getStoreBasicCheckResultJSON() {
    const [
      principalInfo,
      wscWebImConfig,
      shopAddressList,
      lifecycle,
    ] = await this.getStoreBasicCheckResult();

    // 若主体认证非个人 通知平台治理侧需要根据认证情况确定商品可售类目
    if (principalInfo && principalInfo.principalCertType !== 1) {
      const addShopAuditRes = await this.platformKdtAuditService.addShopAudit(this.ctx.kdtId);
      this.ctx.toLog('通知平台治理侧需要根据认证情况确定商品可售类目', addShopAuditRes);
    }

    return this.ctx.json(0, 'success', {
      principalInfo,
      wscWebImConfig,
      shopAddressList,
      lifecycle,
    });
  }
}

export = BasicInfoController;
