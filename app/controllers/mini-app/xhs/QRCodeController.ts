import { Router, API, Inject } from '@youzan/assets-route-plugin';

import BaseController from './BaseController';
import XhsAppsQrcodeService from '../../../services/api/ebiz/video/channels/uc/XhsAppsQrcodeService';

interface IXhsQrCodeQuery {
  url: string;
  width?: number;
  path?: string;
}

@Router('xhs')
class QRCodeController extends BaseController {
  @Inject()
  private readonly XhsAppsQrcodeService!: XhsAppsQrcodeService;

  private async requestXhsQrCode(query: IXhsQrCodeQuery) {
    const { path, url, width } = query;

    const res = await this.XhsAppsQrcodeService.getXhsQrCode({
      kdtId: this.ctx.kdtId,
      scene: url,
      path: path || 'pages/common/blank-page/index',
      width: width || 280,
    });

    return {
      qrCodeUrl: typeof res === 'string' ? `data:image/png;base64,${res}` : res,
    };
  }

  /**
   * 获取小程序二维码 - 对外提供
   */
  @API('GET', 'qr-code.json')
  public async getQqQrCode() {
    const { request } = this.ctx;
    const { page = '', width = 280 } = request.query;
    this.validator.required(page, 'page 不能为空');
    try {
      await this.checkIsBind();
    } catch (e) {
      if (e && e.errorContent) {
        return this.ctx.json(e.errorContent.code, e.errorContent.msg);
      }
      throw e;
    }

    const result = await this.requestXhsQrCode({
      url: page,
      width,
    });
    return this.ctx.json(0, 'ok', result);
  }

  /**
   * 获取小程序二维码 - 对内使用
   */
  @API('GET', 'qr-code-internally.json')
  public async getAlipayQrCodeInternally() {
    const { request } = this.ctx;
    const { page = '', width = 280 } = request.query;
    this.validator.required(page, 'page 不能为空');
    const result = await this.requestXhsQrCode({
      path: page,
      width,
      url: '',
    });
    await this.ctx.json(0, 'ok', result);
  }
}

export = QRCodeController;
