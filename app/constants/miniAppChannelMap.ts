// 小程序渠道元数据
export default {
  alipay: {
    id: 'alipay',
    name: '支付宝',
    accountType: 10,
    bundleId: 'com.alipay.alipaywallet',
    codeKey: 'app_auth_code',
    newCodeKey: 'auth_code',
    yopAppId: 50796,
    adGroupId: 404,
    businessType: 1,
  },
  qq: {
    id: 'qq',
    name: 'QQ',
    accountType: 11,
    codeKey: 'auth_code',
    expiresIn: 'expires_in',
    yopAppId: 50796,
    adGroupId: 425,
    businessType: 1,
  },
  weapp: {
    id: 'weapp',
    name: '微信',
    accountType: 2,
    yopAppId: 1407,
    businessType: 1,
  },
  baidu: {
    yopAppId: 44399,
    name: '百度',
    businessType: 1,
  },
  xhs: {
    id: 'xhs',
    name: '小红书',
    accountType: 16,
    codeKey: 'auth_code',
    yopAppId: 478166,
    adGroupId: 525,
    businessType: 1,
  },
  tiktok: {
    id: 'tiktok',
    name: '抖音核销',
    codeKey: 'authorization_code',
    accountType: 21,
    yopAppId: 478167,
    adGroupId: 525,
    businessType: 1,
  },
  'tiktok-shelf': {
    id: 'tiktok-shelf',
    name: '抖音点单',
    codeKey: 'authorization_code',
    accountType: 21,
    yopAppId: 478167,
    adGroupId: 525,
    businessType: 7,
    channelAccountType: 1,
    channelId: 401,
  },
  'tiktok-coupon': {
    id: 'tiktok-coupon',
    name: '抖音卡券',
    codeKey: 'authorization_code',
    accountType: 21,
    yopAppId: 478167,
    adGroupId: 525,
    businessType: 8,
    channelAccountType: 1,
    channelId: 401,
  },
  ks: {
    id: 'ks',
    name: '快手',
    accountType: 22,
    codeKey: 'authorization_code',
    expiresIn: 'expires_in',
    adGroupId: 526,
  },
};
