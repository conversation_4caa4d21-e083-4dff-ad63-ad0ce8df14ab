import { Inject } from '@youzan/assets-route-plugin';
import lodash from 'lodash';
import BaseController from '../BaseController';
import WeappAccountService from '../../../services/api/channels/channel/core/WeappAccountService';
import VersionInfoService from '../../../services/api/channels/apps/VersionInfoService';
import MpVersionService from '../../../services/api/channels/apps/MpVersionService';
import WeappTradeModuleService, {
  EnumWeappVersionCheckStatus,
  EnumTradeModuleInfoStatus,
  EnumAccessSpuAuditStatus,
  EnumAccessPayOrderStatus,
  EnumAccessSendDeliveryStatus,
  EnumAccessAddAftersaleStatus,
  EnumAccessTestApiStatus,
  EnumAccessDeployWxaStatus,
  EnumProcessFirstOrderStatus,
  EnumSceneGroupId,
  EnumWeappTradeModuleItemAuditStatus,
} from '../../../services/api/channels/apps/WeappTradeModuleService';
import { compareVersion } from '../../../utils/compare-version';

class WechatVideoBaseController extends BaseController {
  @Inject()
  private readonly mpVersionService!: MpVersionService;

  @Inject()
  private readonly versionInfoService!: VersionInfoService;

  @Inject()
  public readonly weappAccountService!: WeappAccountService;

  @Inject()
  protected readonly weappTradeModuleService!: WeappTradeModuleService;

  // 获取小程序基本信息
  protected async getMiniAppAccountInfo() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { businessType = 1 } = this.ctx.query;
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');

    const params = {
      accountType,
      businessType,
      kdtId,
    };
    const result = await this.channelCoreAccountService.queryMpAccountInfoByKdtId(params);
    return result;
  }

  /**
   * 获取小程序绑定情况
   */
  protected async getMiniAppBindInfo() {
    const { kdtId, request } = this.ctx;
    const { businessType = 1 } = request.body;
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    this.validator.required(accountType, 'accountType 不能为空');
    this.validator.required(businessType, 'businessType 不能为空');
    return this.channelCoreAccountService.queryMpBindInfoByKdtId({
      businessType,
      accountType,
      externalId: kdtId,
    });
  }

  protected async getWeixinVideoInfo() {
    const { ctx } = this;
    const { kdtId } = ctx;
    return this.weappAccountService.getWeixinVideoInfo({
      kdtId,
    });
  }

  /**
   * 获取视频号接入状况
   */
  protected getTradeModuleStatus() {
    const { ctx } = this;
    const { kdtId } = ctx;
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');

    const params = {
      accountType,
      businessType: 1,
      kdtId,
    };
    return this.weappAccountService.getTradeModuleStatus(params);
  }

  protected getVersionInfo() {
    const { ctx } = this;
    const { kdtId } = ctx;
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');

    const params = {
      accountType,
      businessType: 1,
      kdtId,
    };
    return this.mpVersionService.getMpVersion(params);
  }

  protected async getLatestVersionInfo() {
    const { ctx } = this;
    const { kdtId, shopType } = ctx;
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');

    const params = {
      accountType,
      businessType: 1,
      kdtId,
      shopType: Number(shopType),
    };
    const result = await this.versionInfoService.getLatestVersionInfo(params);
    return result || {};
  }

  /**
   * 获取小程序版本信息
   */
  protected async getVersionCheckInfo() {
    const [versionInfo, lastVersionInfo] = await Promise.all([
      this.getVersionInfo(),
      this.getLatestVersionInfo(),
    ]);
    const curVersion = versionInfo ? versionInfo.releaseVersion : '';
    const lastVersion = lastVersionInfo ? lastVersionInfo.version : '';
    const isLastVersion = curVersion && lastVersion && curVersion === lastVersion;

    return {
      isLastVersion,
      curVersion,
      lastVersion,
    };
  }

  /**
   * 判断版本是否可用
   */
  protected versionJudge(curVersion: string): boolean {
    const { ctx } = this;
    const { shopType } = ctx;
    if (!curVersion) {
      return false;
    }
    // 零售店铺 当前小程序版本小于3.24.6
    if (shopType === 7 && compareVersion(curVersion, '3.24.6') === -1) {
      return false;
    }
    // 微商城店铺 当前小程序版本小于2.68.5
    if (compareVersion(curVersion, '2.68.5') === -1) {
      return false;
    }
    return true;
  }

  async getRealTimeTradeModuleInfo(sceneGroupId?: EnumSceneGroupId) {
    const { ctx } = this;
    const { kdtId } = ctx;
    try {
      await this.weappTradeModuleService.refreshTradeModuleStatus({
        kdtId,
        sceneGroupId,
      });
    } catch (error) {
      const errCode = lodash.get(error, 'errorContent.code');
      // 160290017为微信小程序没有自定义交易组件申请记录 过滤掉来减少上报数量
      if (errCode !== 160290017) {
        ctx.toLog(`刷新自定义交易组件的状态失败 kdtId=${kdtId}`, error);
      }
    }
    return this.weappTradeModuleService.getTradeModuleInfo({
      kdtId,
      sceneGroupInfoNeed: true,
      sceneGroupId,
    });
  }

  async getTradeModuleInfo(sceneGroupId?: EnumSceneGroupId) {
    const { ctx } = this;
    const { kdtId } = ctx;
    const tradeModuleInfo = await this.getRealTimeTradeModuleInfo(sceneGroupId);
    const { sceneGroupDTOList = [], status, processFirstItemId } = tradeModuleInfo || {};
    const processFirstSkuAuditInfo = processFirstItemId
      ? await this.weappTradeModuleService.getItemAuditInfo({
          kdtId,
          itemId: processFirstItemId,
          sceneGroupId,
        })
      : {};
    // 交易组件开通
    const isOpen = status === EnumTradeModuleInfoStatus.open;
    // 交易组件封禁
    const isBanned = status === EnumTradeModuleInfoStatus.banned;
    // 交易组件封禁
    const isReject = status === EnumTradeModuleInfoStatus.failed;
    // 小程序版本检查
    const weappVersionCheckSuccess =
      lodash.get(tradeModuleInfo, 'weappVersionCheckStatus', '') ===
      EnumWeappVersionCheckStatus.success;
    // 上传商品并审核成功
    const spuAuditSuccess =
      lodash.get(tradeModuleInfo, 'accessSpuAuditStatus', '') === EnumAccessSpuAuditStatus.success;
    // 发起一笔订单并支付成功
    const payOrderSuccess =
      lodash.get(tradeModuleInfo, 'accessPayOrderStatus', '') === EnumAccessPayOrderStatus.success;
    // 物流接口调用成功
    const sendDeliverySuccess =
      lodash.get(tradeModuleInfo, 'accessSendDeliveryStatus', '') ===
      EnumAccessSendDeliveryStatus.success;
    // 售后接口调用成功
    const addAfterSaleSuccess =
      lodash.get(tradeModuleInfo, 'accessAddAftersaleStatus', '') ===
      EnumAccessAddAftersaleStatus.success;
    // 商品接口调试完成
    const spuAuditFinished =
      lodash.get(tradeModuleInfo, 'accessSpuAuditStatus', '') === EnumAccessSpuAuditStatus.finish;
    // 订单接口调试完成
    const payOrderFinished =
      lodash.get(tradeModuleInfo, 'accessPayOrderStatus', '') === EnumAccessPayOrderStatus.finish;
    // 物流接口调试完成
    const sendDeliveryFinished =
      lodash.get(tradeModuleInfo, 'accessSendDeliveryStatus', '') ===
      EnumAccessSendDeliveryStatus.finish;
    // 售后接口调试完成
    const addAfterSaleFinished =
      lodash.get(tradeModuleInfo, 'accessAddAftersaleStatus', '') ===
      EnumAccessAddAftersaleStatus.finish;
    // 发版接入任务完成
    const accessDeployWxaFinish =
      lodash.get(tradeModuleInfo, 'accessDeployWxaStatus', '') === EnumAccessDeployWxaStatus.finish;
    // 测试接入任务状态
    const accessTestApiFinish =
      lodash.get(tradeModuleInfo, 'accessTestApiStatus', '') === EnumAccessTestApiStatus.finish;
    // 首笔订单已付款
    const processFirstOrderPayed =
      (spuAuditSuccess || spuAuditFinished) &&
      (payOrderSuccess || payOrderFinished) &&
      lodash.get(tradeModuleInfo, 'processFirstOrderStatus', 0) ===
        EnumProcessFirstOrderStatus.payed;
    // 首笔订单完成
    const processFirstOrderFinish =
      (spuAuditSuccess || spuAuditFinished) &&
      (payOrderSuccess || payOrderFinished) &&
      (sendDeliverySuccess || sendDeliveryFinished) &&
      (addAfterSaleSuccess || addAfterSaleFinished) &&
      lodash.get(tradeModuleInfo, 'processFirstOrderStatus', 0) ===
        EnumProcessFirstOrderStatus.afterSaleFinish;
    // 商家选择第一个商品同步成功
    const processFirstSkuSuccess =
      (spuAuditSuccess || spuAuditFinished) &&
      lodash.get(processFirstSkuAuditInfo, 'editStatus', '') ===
        EnumWeappTradeModuleItemAuditStatus.success;
    // 交易组件是否启用
    const isEnable =
      spuAuditFinished &&
      payOrderFinished &&
      sendDeliveryFinished &&
      addAfterSaleFinished &&
      accessDeployWxaFinish &&
      accessTestApiFinish;
    // 场景申请审核数据
    const applyWxVideoScene = sceneGroupDTOList.find(scene => scene.groupId === 1) || {};
    return {
      ...(tradeModuleInfo || {}),
      isOpen,
      isBanned,
      isReject,
      weappVersionCheckSuccess,
      processFirstSkuSuccess,
      processFirstSkuAuditInfo,
      processFirstOrderPayed,
      processFirstOrderFinish,
      isEnable,
      applyWxVideoScene,
    };
  }
}

export default WechatVideoBaseController;
