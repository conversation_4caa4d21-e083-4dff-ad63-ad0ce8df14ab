# 需求文档

## 介绍

此功能涉及创建一个新的抽屉式组件 `edit-drawer.tsx`，用于替代当前渠道设置的页面导航。当用户点击"渠道设置"按钮时，系统将打开一个抽屉覆盖层，包含相同的功能但有一些UI修改，而不是导航到单独的编辑页面。

## 需求

### 需求 1

**用户故事：** 作为管理全渠道设置的用户，我希望通过抽屉界面访问渠道配置，而不是导航到单独的页面，这样我可以保持上下文并拥有更流畅的工作流程。

#### 验收标准

1. 当用户点击"渠道设置"按钮时，系统应打开 edit-drawer 组件，而不是导航到编辑页面
2. 当抽屉打开时，系统应使用 Zent Drawer 组件实现，从右侧滑出，宽度为800px
3. 当抽屉打开时，系统应显示与编辑页面相同的三个主要模块：店铺、商品同步规则和外卖开票设置
4. 当显示抽屉时，系统应保持与现有编辑页面相同的表单逻辑和验证
5. 当抽屉以预览模式打开时，系统应将所有表单字段设置为只读状态，用户只能查看不能编辑
6. 当抽屉以预览模式打开时，系统应隐藏保存按钮，只显示关闭按钮

### 需求 2

**用户故事：** 作为配置店铺设置的用户，我希望抽屉中的店铺模块与编辑页面功能完全相同，这样我在不同界面间可以有一致的行为。

#### 验收标准

1. 当渠道类型为美团、美团商购或京东外卖且满足显示条件时，系统应显示店铺模块
2. 当显示店铺模块时，系统应包含现有编辑页面店铺部分的所有字段和逻辑
3. 当进行表单验证时，系统应应用与编辑页面相同的验证规则
4. 当保存数据时，系统应使用与编辑页面相同的API端点和数据格式
5. 当渠道为京东外卖且用户选择关闭营业状态时，系统应显示确认对话框提示"修改营业时间会影响所有京东分店"

### 需求 3

**用户故事：** 作为设置商品同步规则的用户，我希望通过特定的表单字段配置同步设置，这样我可以控制商品数据在平台间如何同步。

#### 验收标准

1. 当显示商品同步规则部分时，系统应包含必填的单选字段"更新范围"(itemSyncRange)，选项包括："1:指定部分信息"、"0: 全部信息"、"2:仅绑定商品，不更新信息"
2. 当显示itemSyncRange字段时，系统应显示帮助文本"更新范围仅对已经绑定的外卖平台商品生效"
3. 当itemSyncRange设置为"指定部分信息"时，系统应隐藏基础信息和价格信息部分，同时保持字段数据不变
4. 当itemSyncRange设置为"仅绑定商品，不更新信息"时，系统应隐藏除"商品库发布外卖商品时"字段外的所有部分，同时保持字段数据不变
4.1. 当用户选择"全部信息"且取消所有基础信息和价格信息字段的选中状态时，系统应自动将更新范围切换到"指定部分信息"
4.2. 当用户处于"全部信息"状态且基础信息和价格信息所有字段都未选中时，系统应在下方显示红色提示文案"基础信息和价格信息至少选中一个选项"
4.3. 当用户保存时，如果选择"指定部分信息"，系统应将所有基础信息和价格信息字段设置为0
4.4. 当用户保存时，如果选择"仅绑定商品，不更新信息"，系统应将所有销售信息、基础信息和价格信息字段设置为0
5. 当显示销售信息部分时，系统应包含"销售状态上下架"(takeUpOrDown)和"商品库存"的复选框
6. 当显示销售信息复选框时，系统应显示帮助文本"仅饿了么外卖和京东外卖渠道，支持同步销售状态"
7. 当显示基础信息部分时，系统应包含以下复选框：商品名称、规格名称(skuName)、商品/规格条码(barcode)、商品类目、属性、商品主图、商品分组、商品描述、最小起购量
8. 当显示价格信息部分时，系统应包含以下复选框：价格和商品打包费
9. 当显示库存同步规则字段(stockThreshold)时，系统应显示带有更新文本的单选选项："当商品增加库存，或可售库存≤15个时，实时同步"和"当商品增加库存，或可售库存≤50个时，实时同步"
10. 当选中库存复选框且销售信息部分可见时，系统应显示stockThreshold字段
10.1. 当未选中库存复选框或销售信息部分被隐藏时，系统应隐藏stockThreshold字段
11. 当选择≤15选项时，系统应显示帮助文本"适合日库存固定/有限商家（如面包/商超便利等业态）"
12. 当选择≤50选项时，系统应显示帮助文本"适合日库存充足/无限商家（如正餐西餐、咖啡茶饮、仅生日蛋糕等业态）"
13. 当显示自动发布字段(autoPublishSync)时，系统应显示必填的单选选项："1:自动发布创建到外卖平台"和"0: 手动同步创建到外卖平台"
14. 当用户是总部账户且不是京东外卖单店时，系统应在商品同步规则模块内显示"总部可修改分店外卖商品"字段，逻辑与编辑页面相同
15. 当系统为非多渠道环境时，系统应显示库存共享比例模块
16. 当显示库存共享比例模块时，系统应包含两个字段：实物可售库存和计划可售库存
17. 当显示实物可售库存字段时，系统应设置为必填数字输入框，范围1-100，单位为%
18. 当显示计划可售库存字段时，系统应设置为必填下拉选择框，选项包括"不共享"和"与实物库存一致"
19. 当库存共享比例模块显示时，系统应显示与编辑页面相同的帮助说明文本

### 需求 4

**用户故事：** 作为配置开票设置的用户，我希望外卖开票设置与编辑页面工作方式相同但有不同的默认值，这样我可以一致地管理开票偏好。

#### 验收标准

1. 当显示开票设置部分时，系统应包含与编辑页面"订单模块"相同的字段
2. 当初始化开票设置时，系统应将"外卖订单开票设置"的默认值设置为启用（开启）

### 需求 5

**用户故事：** 作为使用抽屉界面的用户，我希望数据持久化和表单行为与编辑页面匹配，这样无论使用哪个界面我都能有一致的体验。

#### 验收标准

1. 当抽屉加载时，系统应使用与编辑页面相同的数据检索逻辑填充表单字段
2. 当保存表单数据时，系统应使用与编辑页面相同的保存逻辑和API调用
3. 当进行验证时，系统应应用与编辑页面相同的验证规则
4. 当关闭抽屉而不保存时，系统不应持久化任何更改
5. 当保存操作成功完成时，系统应显示与编辑页面相同的成功通知

### 需求 6

**用户故事：** 作为需要查看渠道配置的用户，我希望能够以预览模式打开抽屉，这样我可以查看当前设置而不会意外修改任何配置。

#### 验收标准

1. 当抽屉以预览模式打开时，系统应将所有表单字段设置为禁用状态
2. 当处于预览模式时，系统应显示所有当前配置值，但不允许用户修改
3. 当处于预览模式时，系统应在抽屉底部只显示"关闭"按钮，隐藏"保存"按钮
4. 当处于预览模式时，系统应在抽屉标题中明确标识为"查看模式"或类似标识
5. 当处于预览模式时，系统应保持所有条件显示逻辑正常工作，用户可以看到基于当前配置的字段显示状态

### 需求 7

**用户故事：** 作为系统维护者，我希望在完成edit-drawer组件开发后，将现有的渠道设置页面跳转替换为打开新的抽屉组件，这样用户可以获得更好的交互体验。

#### 验收标准

1. 当edit-drawer组件开发完成后，系统应识别所有跳转到渠道设置编辑页的地方
2. 当找到跳转逻辑后，系统应将页面跳转替换为打开edit-drawer组件
3. 当替换跳转逻辑时，系统应保持相同的参数传递（kdtId, channelId, accountType等）
4. 当用户点击原来会跳转到编辑页的按钮时，系统应打开edit-drawer抽屉
5. 当抽屉关闭或保存成功后，系统应刷新或更新相关的列表数据