import { Router, API, Inject, Index } from '@youzan/assets-route-plugin';

import CommonBasicInfoController from '../common/BasicInfoController';
import ChannelItemOperateService from '../../../services/api/ebiz/video/channels/item/channel/ChannelItemOperateService';
import ChannelItemQueryService from '../../../services/api/ebiz/video/channels/item/channel/ChannelItemQueryService';
import KsAppletCategoryService from '../../../services/api/ebiz/video/channels/uc/KsAppletCategoryService';

@Router('ks/goods')
class GoodsController extends CommonBasicInfoController {
  @Inject()
  private readonly ChannelItemOperateService!: ChannelItemOperateService;

  @Inject()
  private readonly ChannelItemQueryService!: ChannelItemQueryService;

  @Inject()
  private readonly KsAppletCategoryService!: KsAppletCategoryService;

  public get baseInfo() {
    const baseInfo = this.getOperatorParams();
    return {
      operatorId: baseInfo.operator.userId,
      operator: baseInfo.operator.nickName,
      fromApp: baseInfo.fromApp,
    };
  }

  public async init() {
    this.channelName = 'ks';
    super.init();
  }

  @Index('')
  public async getGoodsHtml() {
    await this.checkIsBind();
    const { ctx } = this;
    ctx.setState('subtitle', '商品管理');
    return ctx.render('mini-app/ks/goods/index.html');
  }

  // 获取快手商品列表
  @API('GET', 'get-ks-goods-list.json')
  public async getKsGoodsList() {
    const { ctx, baseInfo } = this;
    const { kdtId, request } = ctx;
    const {
      endCreateTime,
      pageSize,
      page,
      startCreateTime,
      title,
      innerAuditStatus = 0,
    } = request.query;

    const params = {
      ...baseInfo,
      innerAuditStatus,
      channel: 3,
      endCreateTime,
      kdtId,
      pageSize,
      page,
      startCreateTime,
      title,
    };

    if (!innerAuditStatus) {
      delete params.innerAuditStatus;
    }

    const res = await this.ChannelItemQueryService.listOfPage(params);
    return ctx.json(0, 'ok', res);
  }

  // 商品管理 - 添加推广 - 商品选择器商品列表
  @API('GET', 'search-goods.json')
  public async searchGoods() {
    const { ctx, baseInfo } = this;
    const { kdtId } = ctx;
    const { pageSize, page, keyword } = this.ctx.getQuery();
    const res = await this.ChannelItemQueryService.listPageOfSelector({
      ...baseInfo,
      kdtId,
      pageSize,
      page,
      keyword,
      channel: 3,
    });
    return ctx.json(0, 'ok', res);
  }

  // 商品批量推广至快手
  @API('POST', 'batch-select-channel-item.json')
  public async batchSelect() {
    const { ctx, baseInfo } = this;
    const { kdtId, request } = ctx;
    const {
      body: { items },
    } = request;

    const res = await this.ChannelItemOperateService.batchSelect({
      ...baseInfo,
      kdtId,
      items,
      channel: 3,
    });
    return ctx.json(0, 'ok', res);
  }

  // 更新快手商品信息
  @API('POST', 'update-channel-item.json')
  public async update() {
    const { ctx, baseInfo } = this;
    const { kdtId, request } = ctx;
    const {
      body: { categoryId: leafCategoryId, itemId, title },
    } = request;

    const res = await this.ChannelItemOperateService.update({
      ...baseInfo,
      channel: 3,
      itemId,
      kdtId,
      title,
      extra: {
        leafCategoryId,
      },
    });
    return ctx.json(0, 'ok', res);
  }

  // 商品取消推广至快手
  @API('POST', 'delete-channel-item.json')
  public async delete() {
    const { ctx, baseInfo } = this;
    const { kdtId, request } = ctx;
    const {
      body: { itemId },
    } = request;
    const res = await this.ChannelItemOperateService.delete({
      ...baseInfo,
      kdtId,
      itemId,
      channel: 3,
    });
    return ctx.json(0, 'ok', res);
  }

  // 商品管理 - 添加推广 - 商品选择器商品列表
  @API('GET', 'get-category-tree.json')
  public async getCategoryTree() {
    const { ctx } = this;
    const res = await this.KsAppletCategoryService.getCategoryTree();
    return ctx.json(0, 'ok', res);
  }
}

export = GoodsController;
