import { Router, Index, API, Inject } from '@youzan/assets-route-plugin';
import { checkChainStore } from '@youzan/utils-shop';
import BaseController from '../BaseController';
import MpVersionService from '../../../services/api/channels/apps/MpVersionService';
import KsMiniAppService from '../../../services/api/channels/apps/KsMiniAppService';
import VersionInfoService from '../../../services/api/channels/apps/VersionInfoService';
import WeappMpLinkService from '../../../services/api/channels/apps/WeappMpLinkService';
import HongShuMiniAppService from '../../../services/api/channels/channel/core/HongShuMiniAppService';
import ChannelCoreAccountService from '../../../services/api/channels/channel/core/ChannelCoreAccountService';
import KuaishouMiniAppService from '../../../services/api/channels/channel/core/KuaishouMiniAppService';
import ShopConfigWriteService from '../../../services/api/shopcenter/shopconfig/ShopConfigWriteService';
import channelMap from '../../../constants/miniAppChannelMap';
import { settleConfigKey } from '../../../constants/xhsLocalLife';
import ShopConfigReadService from '../../../services/api/shopcenter/shopconfig/ShopConfigReadService';
import { ShopAbilityUtil } from '@youzan/plugin-shop-ability';

/**
 * 基础信息
 */
@Router(':channelName/basic-info')
class IndexController extends BaseController {
  @Inject()
  protected readonly channelCoreAccountService!: ChannelCoreAccountService;

  @Inject()
  private readonly mpVersionService!: MpVersionService;

  @Inject()
  private readonly KsMiniAppService!: KsMiniAppService;

  @Inject()
  private readonly versionInfoService!: VersionInfoService;

  @Inject()
  private readonly weappMpLinkService!: WeappMpLinkService;

  @Inject()
  public readonly HongShuMiniAppService!: HongShuMiniAppService;

  @Inject()
  public readonly KuaishouMiniAppService!: KuaishouMiniAppService;

  @Inject()
  public readonly shopConfigReadService!: ShopConfigReadService;

  @Inject()
  public readonly shopConfigWriteService!: ShopConfigWriteService;

  @Index(['', 'xhs-independent'])
  public async getBaseInfoHtml() {
    const { ctx } = this;

    // 小红书独立版本判断
    const hasXhsIndependentAbility = await new ShopAbilityUtil(ctx).checkAbilityValid({
      keys: ['xhs_independent_ability'],
      kdtId: ctx.kdtId,
    });
    if (hasXhsIndependentAbility && ctx.url.indexOf('xhs-independent') < 0) {
      ctx.redirect('/v4/channel/xhs/basic-info/xhs-independent');
      return;
    }

    await this.checkIsBind();
    await this.resolveXhsLocalLifeInfo(ctx);

    ctx.setState('subtitle', '基础信息');
    ctx.setGlobal('isOpenFusion', await this.isOpenFusion());
    return ctx.render('mini-app/common/basic-info/index.html');
  }

  @Index(['settings', 'xhs-independent/settings'])
  public async getBaseInfoSettingsHtml() {
    const { ctx } = this;

    // 小红书独立版本判断
    const hasXhsIndependentAbility = await new ShopAbilityUtil(ctx).checkAbilityValid({
      keys: ['xhs_independent_ability'],
      kdtId: ctx.kdtId,
    });
    if (hasXhsIndependentAbility && ctx.url.indexOf('xhs-independent') < 0) {
      ctx.redirect('/v4/channel/xhs/basic-info/xhs-independent/settings');
      return;
    }

    await this.checkIsBind();
    ctx.setState('subtitle', '基础设置');
    return ctx.render('mini-app/common/basic-info/settings.html');
  }

  @API('GET', 'account-info.json')
  public async getAccountInfoJson() {
    const { ctx } = this;
    const accountInfo = await this.getAccountInfo();
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');
    let result = null;
    if (accountType === channelMap.xhs.accountType) {
      const { name, icon, desc, principalName, appId } = accountInfo || {};
      result = {
        name,
        avatar: icon,
        description: desc,
        category: '',
        principal: principalName,
        appId,
        registerSource: 3, // 3代表小红书官方注册
      };
    } else if (accountType === channelMap.ks.accountType) {
      const { name, icon, desc, appId, appCategory, subjectType, subjectName } = accountInfo;
      result = {
        name,
        avatar: icon,
        description: desc,
        category: appCategory,
        appId,
        registerSource: subjectType,
        principal: subjectName,
      };
    } else {
      result = {
        name: accountInfo.mpNickname,
        avatar: accountInfo.headImageInfo.headImageUrl,
        description: accountInfo.signatureInfo.signature,
        category: accountInfo.wxCategoryInfos,
        principal: accountInfo.principalName,
        appId: accountInfo.appId,
        registerSource: accountInfo.createdFrom,
      };
    }
    return ctx.json(0, 'ok', result);
  }

  @API('GET', 'version-info.json')
  public async getVersionInfoJson() {
    const { ctx } = this;
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');
    let versionInfo;
    if (accountType === channelMap.ks.accountType) {
      versionInfo = await this.getKsVersionInfo();
    } else {
      versionInfo = await this.getVersionInfo();
    }
    if (!versionInfo) return ctx.json(-1, '');
    return ctx.json(0, 'ok', {
      current: versionInfo.releaseVersion,
      updatedAt: versionInfo.releaseTime,
      status: versionInfo.status,
      auditingVersion: versionInfo.auditingVersion,
      auditTime: versionInfo.auditingTime,
      auditReason: versionInfo.auditReason,
      autoUpdate: versionInfo.autoUpgrade,
      releaseVersionId: versionInfo.releaseVersionId,
      releaseVersionOfflineReason: versionInfo.releaseVersionOfflineReason,
      releaseVersionStatus: versionInfo.releaseVersionStatus,
      auditPackageId: versionInfo.auditPackageId,
    });
  }

  @API('GET', 'latest-version-info.json')
  public async getLatestVersionInfoJson() {
    const { ctx } = this;
    const { version = '', versionType = '' } = await this.getLatestVersionInfo();
    return ctx.json(0, 'ok', {
      version,
      type: versionType,
    });
  }

  protected async getAccountInfo() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { businessType = 1 } = this.ctx.query;
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');
    const params = {
      accountType,
      businessType,
      kdtId,
    };
    let result = null;
    if (accountType === channelMap.xhs.accountType) {
      result = this.HongShuMiniAppService.getAccountBasicInfo(params);
    } else if (accountType === channelMap.ks.accountType) {
      result = this.KuaishouMiniAppService.getBasicInfo({ kdtId });
    } else {
      result = this.channelCoreAccountService.queryMpAccountInfoByKdtId(params);
    }
    return result;
  }

  protected getVersionInfo() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { businessType = 1 } = this.ctx.query;
    // @ts-ignore
    const { accountType, bundleId } = ctx.getState('channelMeta');

    const params = {
      accountType,
      businessType,
      kdtId,
    };
    if (bundleId) {
      // @ts-ignore
      params.bundleId = bundleId;
    }
    return this.mpVersionService.getMpVersion(params);
  }

  protected getKsVersionInfo() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { businessType = 1 } = this.ctx.query;
    // @ts-ignore
    const { accountType, bundleId } = ctx.getState('channelMeta');

    const params = {
      accountType,
      businessType,
      kdtId,
    };
    if (bundleId) {
      // @ts-ignore
      params.bundleId = bundleId;
    }
    return this.KsMiniAppService.getMpVersion(params);
  }

  protected async getLatestVersionInfo() {
    const { ctx } = this;
    const { kdtId, query } = ctx;
    const { businessType = 1, shopType } = query;
    // @ts-ignore
    const { accountType, bundleId } = ctx.getState('channelMeta');

    this.validator.required(shopType, '参数错误，shopType 不能为空');

    const params = {
      accountType,
      businessType: Number(businessType),
      kdtId,
      shopType: Number(shopType),
    };
    if (bundleId) {
      // @ts-ignore
      params.bundleId = bundleId;
    }
    const result = await this.versionInfoService.getLatestVersionInfo(params);
    return result || {};
  }

  protected async checkIsBind() {
    let isBind = true;
    try {
      isBind = await this.getAccountInfo();
    } catch (e) {
      isBind = false;
    }
    if (!isBind) {
      const { ctx } = this;
      // @ts-ignore
      const { id } = ctx.getState('channelMeta');
      ctx.redirect(`/v4/channel/${id}/auth/empty`);
    }
  }

  protected async resolveXhsLocalLifeInfo(ctx: any) {
    const xhsLocalLife = await this.checkXhsLocalLife();

    const shopInfo = ctx.getState('shopInfo');
    const isChainStore = checkChainStore(shopInfo);

    if (xhsLocalLife) {
      const { value } = await this.shopConfigReadService
        .queryShopConfig(ctx.kdtId, settleConfigKey)
        .catch(() => ({
          value: '0',
        }));

      ctx.setGlobal('settleType', value);
      ctx.setGlobal('isChainStore', isChainStore);
    }
  }

  protected async isOpenFusion() {
    // 0 - 老版本，1 - 新版本
    try {
      const {
        configs: { tiktok_miniapp_mix_version_switch: isOpenFusion = 0 },
      } = await this.shopConfigReadService.queryShopConfigs(this.ctx.kdtId, [
        'tiktok_miniapp_mix_version_switch',
      ]);

      return Number(isOpenFusion) === 1;
    } catch (error) {}

    return false;
  }
}

export = IndexController;
