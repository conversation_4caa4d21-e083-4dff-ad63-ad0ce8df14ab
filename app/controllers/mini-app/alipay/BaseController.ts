import { Inject } from '@youzan/assets-route-plugin';
import loadsh from 'lodash';
import MiniAppBaseController from '../BaseController';
import AlipayAccountService from '../../../services/api/channels/channel/core/AlipayAccountService';
import Applet from '../../../services/api/pay/customer/process/ali/Applet';
import channelMap from '../../../constants/miniAppChannelMap';
import PermiseCheckError from '../../../exceptions/PremiseCheckError';
import AlipayAntShopService from '../../../services/api/channels/channel/core/AlipayAntShopService';

class IndexController extends MiniAppBaseController {
  @Inject()
  public readonly alipayAccountService!: AlipayAccountService;

  @Inject()
  public readonly applet!: Applet;

  @Inject()
  public readonly alipayAntShopService!: AlipayAntShopService;

  public async init() {
    this.channelName = 'alipay';
    super.init();
  }

  protected async checkIsBind() {
    const bindInfo = await this.getBindInfo();
    if (!bindInfo) {
      throw new PermiseCheckError(12345, '支付宝小程序未授权');
    } else {
      return bindInfo;
    }
  }

  protected async checkPayState() {
    const { kdtId, request } = this.ctx;
    const { businessType = 1 } = request.query;
    // 获取rootKdtId
    const rootKdtId = loadsh.get(this.ctx.getState('shopInfo'), 'rootKdtId', kdtId);
    const accountInfo = await this.alipayAccountService.getAlipayAccountInfo({
      businessType,
      accountType: channelMap.alipay.accountType,
      kdtId: rootKdtId,
    });

    if (accountInfo) {
      const payInfo = await this.applet.submitQuery({
        kdtId: rootKdtId,
        appId: accountInfo.appId,
      });
      this.ctx.toLog('查询支付宝支付方式申请进度', {
        request: {
          kdtId: rootKdtId,
          appId: accountInfo.appId,
        },
        response: payInfo,
      });
      if (payInfo.state !== 'SUCCESS') {
        throw new PermiseCheckError(12345, '未开通支付宝小程序支付方式配置，请在「店铺-支付宝小程序-支付配置」开通支付宝小程序支付');
      }
    } else {
      throw new PermiseCheckError(12345, '支付宝小程序账号信息获取失败');
    }
  }
}

export default IndexController;
