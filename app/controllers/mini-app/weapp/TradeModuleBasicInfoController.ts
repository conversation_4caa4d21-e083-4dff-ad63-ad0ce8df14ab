import { Router, Index, API, Inject } from '@youzan/assets-route-plugin';
import formatDate from '@youzan/utils/date/formatDate';
import lodash from 'lodash';
import {
  checkUnifiedChainStoreSolution,
  checkEduChainStoreV4,
  checkBranchStore,
} from '@youzan/utils-shop';
import BaseController from './BaseController';
import CategoryBackService from '../../../services/api/mall/item/CategoryBackService';
import ItemCommonService from '../../../services/api/mall/item/ItemCommonService';
import ItemQueryService from '../../../services/api/mall/item/ItemQueryService';
import IcItemQueryService from '../../../services/api/ic/ItemQueryService';
import WechatChannelService from '../../../services/api/mall/item/WechatChannelService';
import ShopConfigReadService from '../../../services/api/shopcenter/shopconfig/ShopConfigReadService';
import ShopAddressOuterService from '../../../services/api/shopcenter/outer/address/ShopAddressOuterService';
import ProdReadService from '../../../services/api/shopcenter/shopprod/prod/ProdReadService';
import CustomerUnifiedCertQueryService from '../../../services/api/pay/customer/cert/UnifiedCertQueryService';
import PrincipalInfoQueryService, {
  IQueryCompanyTrademarkRes,
} from '../../../services/api/pay/unified/cert/PrincipalInfoQueryService';
import MultiBrandCertQueryV2Service from '../../../services/api/pay/unified/cert/brand/MultiBrandCertQueryV2Service';
import UnifiedCertQueryService, {
  IBrandCertDTO,
  IQueryBrandMsgRes,
} from '../../../services/api/pay/unified/cert/UnifiedCertQueryService';
import PlatformKdtAuditService from '../../../services/api/cps/goods/PlatformKdtAuditService';
import PlatformKdtTaskService from '../../../services/api/cps/goods/PlatformKdtTaskService';
import SelfrunGoodsQueryService from '../../../services/api/cps/goods/SelfrunGoodsQueryService';
import WeappAccountService from '../../../services/api/channels/channel/core/WeappAccountService';
import ChannelCoreAccountService from '../../../services/api/channels/channel/core/ChannelCoreAccountService';
import FastRegisterTransactionWeappService from '../../../services/api/channels/channel/core/FastRegisterTransactionWeappService';
import WeappTradeModuleService, {
  EnumWeappVersionCheckStatus,
  EnumTradeModuleInfoStatus,
  EnumAccessSpuAuditStatus,
  EnumAccessPayOrderStatus,
  EnumAccessSendDeliveryStatus,
  EnumAccessAddAftersaleStatus,
  EnumAccessTestApiStatus,
  EnumAccessDeployWxaStatus,
  EnumProcessFirstOrderStatus,
  EnumTradeModuleAccessInfoItem,
  EnumWeappTradeModuleItemAuditStatus,
  EnumSceneGroupId,
} from '../../../services/api/channels/apps/WeappTradeModuleService';
import ItemRecommendService from '../../../services/api/ebiz/video/channels/item/ItemRecommendService';
import LiveChannelCouponService from '../../../services/api/ebiz/video/channels/promotion/LiveChannelCouponService';
import ICWechatChannelsService from '../../../services/api/ic/external/wechat/channels/ICWechatChannelsService';
import WxWeappQRCodeService from '../../../services/api/channels/business/WxWeappQRCodeService';
import WeappQRCodeService from '../../../services/api/channels/apps/WeappQRCodeService';
import StorageQiniuReadService, {
  IOldPrivateUrlDTO,
  IRefreshPrivateUrlRes,
} from '../../../services/api/material/materialcenter/StorageQiniuReadService';
import WechatChannelConsoleService from '../../../services/api/ebiz/video/channels/item/wechat/WechatChannelConsoleService';
import HQStoreSearchService from '../../../services/api/retail/shop/HQStoreSearchService';
import WeappRegisterProxyService from '../../../services/api/channels/apps/WeappRegisterProxyService';
import utils from '../../../utils/shopUtils';
// import { channelTypeEnum } from '../../../constants/channelMap';

enum EnumWhiteListQueryList {
  shopType = 'shopType',
  shopTopic = 'shopTopic',
  shopRole = 'shopRole',
}

@Router('weapp/trade-module/basic-info')
class BasicInfoController extends BaseController {
  @Inject()
  public readonly categoryBackService!: CategoryBackService;

  @Inject()
  public readonly weappRegisterProxyService!: WeappRegisterProxyService;

  @Inject()
  public readonly itemCommonService!: ItemCommonService;

  @Inject()
  public readonly itemQueryService!: ItemQueryService;

  @Inject()
  public readonly icItemQueryService!: IcItemQueryService;

  @Inject()
  public readonly wechatChannelService!: WechatChannelService;

  @Inject()
  public readonly wechatChannelConsoleService!: WechatChannelConsoleService;

  @Inject()
  public readonly shopConfigReadService!: ShopConfigReadService;

  @Inject()
  public readonly shopAddressOuterService!: ShopAddressOuterService;

  @Inject()
  public readonly prodReadService!: ProdReadService;

  @Inject()
  public readonly multiBrandCertQueryV2Service!: MultiBrandCertQueryV2Service;

  @Inject()
  public readonly unifiedCertQueryService!: UnifiedCertQueryService;

  @Inject()
  public readonly principalInfoQueryService!: PrincipalInfoQueryService;

  @Inject()
  public readonly customerUnifiedCertQueryService!: CustomerUnifiedCertQueryService;

  @Inject()
  public readonly platformKdtAuditService!: PlatformKdtAuditService;

  @Inject()
  public readonly platformKdtTaskService!: PlatformKdtTaskService;

  @Inject()
  public readonly weappAccountService!: WeappAccountService;

  @Inject()
  public readonly channelCoreAccountService!: ChannelCoreAccountService;

  @Inject()
  public readonly selfrunGoodsQueryService!: SelfrunGoodsQueryService;

  @Inject()
  public readonly storageQiniuReadService!: StorageQiniuReadService;

  @Inject()
  public readonly weappTradeModuleService!: WeappTradeModuleService;

  @Inject()
  public readonly itemRecommendService!: ItemRecommendService;

  @Inject()
  public readonly liveChannelCouponService!: LiveChannelCouponService;

  @Inject()
  public readonly iCWechatChannelsService!: ICWechatChannelsService;

  @Inject()
  public readonly wxWeappQRCodeService!: WxWeappQRCodeService;

  @Inject()
  public readonly weappQRCodeService!: WeappQRCodeService;

  @Inject()
  public readonly FastRegisterTransactionWeappService!: FastRegisterTransactionWeappService;

  @Inject()
  public readonly hQStoreSearchService!: HQStoreSearchService;

  public async init() {
    this.channelName = 'weapp';
    super.init();
  }

  @Index('')
  public async getBaseInfoHtml() {
    const { ctx } = this;
    const { kdtId, query } = ctx;
    const shopMetaInfo = ctx.getState('shopMetaInfo');

    const isTradeModuleAlreadyApply = await this.getIsTradeModuleAlreadyApply();

    // 分店 && 交易组件未完成启用 分店无权限操作
    if (checkBranchStore(shopMetaInfo) && !isTradeModuleAlreadyApply) {
      return ctx.render('mini-app/weapp/trade-module/branch-settings.html');
    }

    const [
      miniAppInfoCheckResult,
      storeBasicCheckResult,
      isNewCategory,
      listItemsPagedNoCategory,
      isInWxModuleWhiteList,
      tradeModuleAccessInfo,
      tradeModuleV3AccessInfo,
      weixinVideoInfo,
      isPersonWeapp,
    ] = await Promise.all([
      this.getMiniAppInfoCheckResult(),
      this.getStoreBasicCheckResult(),
      this.itemCommonService.isNewCategoryKdtWhite(kdtId),
      this.itemQueryService.listItemsPagedNoCategory({
        kdtId: this.ctx.kdtId,
      }),
      this.isInWxModuleWhiteList(),
      this.getTradeModuleInfo(),
      this.getTradeModuleInfo(EnumSceneGroupId.WXVIDEO_OR_WXPUBLIC),
      this.getWeixinVideoInfo(),
      this.getIsPersonWeapp(),
    ]);

    const isShowNotAuthorized = this.getIsNotAuthorized(miniAppInfoCheckResult);

    if (isShowNotAuthorized) {
      // 未开通小程序
      return ctx.render('mini-app/weapp/trade-module/not-authorized.html');
    }

    let noNewCategoryCount = lodash.get(listItemsPagedNoCategory, 'count', '');
    // 获取线下课程未设置类目数量
    if (ctx.shopType === 0 && ctx.shopTopic === 1) {
      const listCourseItemsPagedNoCategory = await this.itemQueryService.listItemsPagedNoCategory({
        kdtId: this.ctx.kdtId,
        includeAbilityMarks: ['10007'], // 商品能力标 线下课10007
        includeBizCodes: ['000000000031'], // 商品业务标 课程商品000000000031
      });
      const listCourseItemsPagedNoCategoryCount = lodash.get(
        listCourseItemsPagedNoCategory,
        'count',
        '',
      );
      noNewCategoryCount += listCourseItemsPagedNoCategoryCount;
    }

    const isMiniAppInfoCheckOk = this.getMiniAppInfoCheckIsOkWithAllResult(miniAppInfoCheckResult);

    const isStoreBasicCheckOk = this.getStoreBasicCheckIsOkWithAllResult(storeBasicCheckResult);

    // 能否进入详情页
    const canDirectEnterBasicInfo =
      (tradeModuleAccessInfo.isOpen && tradeModuleAccessInfo.isEnable) ||
      query.directEntryDetail === '1'; // query.directEntryDetail === '1' || isOpenSkuSync;

    ctx.setGlobal('pageData', {
      isStoreBasicCheckOk,
      isMiniAppInfoCheckOk,
      miniAppAccountInfo: lodash.get(miniAppInfoCheckResult, ['0', 'miniAppAccountInfo']),
      isInSupportWeappCategories: lodash.get(miniAppInfoCheckResult, [
        '0',
        'isInSupportWeappCategories',
      ]),
      isNewCategory,
      noNewCategoryCount: !noNewCategoryCount ? Number(noNewCategoryCount) : noNewCategoryCount,
      isInWxModuleWhiteList,
      tradeModuleAccessInfo,
      tradeModuleV3AccessInfo,
      weixinVideoInfo,
      extensionOfficerOpenStatus: weixinVideoInfo?.extensionOfficerOpenStatus,
    });

    if (isPersonWeapp) {
      if (!isTradeModuleAlreadyApply) {
        // 个人小程序交易组件未开通
        return ctx.render('mini-app/weapp/trade-module/person-settings.html');
      }
      return ctx.render('mini-app/weapp/trade-module/basic-info.html');
    }

    /**
     *

     */
    // eslint-disable-next-line no-constant-condition
    if (canDirectEnterBasicInfo) {
      return ctx.render('mini-app/weapp/trade-module/basic-info.html');
    }
    // 未开通交易组件则进入开通流程
    return ctx.render('mini-app/weapp/trade-module/settings.html');
  }

  // 是否展示无小程序引导页
  getIsNotAuthorized(miniAppInfoCheckResult: any[]) {
    const res = lodash.get(miniAppInfoCheckResult, ['0', 'miniAppAccountInfo']);

    return !res;
  }

  // 是否是个人小程序
  async getIsPersonWeapp() {
    let res = false;
    const { kdtId } = this.ctx;
    try {
      res = await this.FastRegisterTransactionWeappService.isFastRegisterTransactionWeapp({
        kdtId,
        businessType: 1,
      });
    } catch (error) {
      this.ctx.logger.error('获取是否为个人小程序失败', error, { kdtId });
    }

    return res;
  }

  // 交易组件是否申请成功
  async getIsTradeModuleAlreadyApply() {
    const { kdtId } = this.ctx;
    let res = false;
    try {
      const {
        alreadyApply,
        accessFinish,
      } = await this.weappTradeModuleService.getTradeModuleStatus({
        kdtId,
      });

      res = alreadyApply && accessFinish;
    } catch (error) {
      this.ctx.logger.error('获取交易组件是否申请成功失败', error, { kdtId });
    }
    return res;
  }

  async getRealTimeTradeModuleInfo(sceneGroupId?: EnumSceneGroupId) {
    const { ctx } = this;
    const { kdtId } = ctx;
    try {
      await this.weappTradeModuleService.refreshTradeModuleStatus({
        kdtId,
        sceneGroupId,
      });
    } catch (error) {
      const errCode = lodash.get(error, 'errorContent.code');
      // 160290017为微信小程序没有自定义交易组件申请记录 过滤掉来减少上报数量
      if (errCode !== 160290017) {
        ctx.toLog(`刷新自定义交易组件的状态失败 kdtId=${kdtId}`, error);
      }
    }
    return this.weappTradeModuleService.getTradeModuleInfo({
      kdtId,
      sceneGroupInfoNeed: true,
      sceneGroupId,
    });
  }

  async getTradeModuleInfo(sceneGroupId?: EnumSceneGroupId) {
    const { ctx } = this;
    const { kdtId } = ctx;
    const tradeModuleInfo = await this.getRealTimeTradeModuleInfo(sceneGroupId);
    const { status, processFirstItemId } = tradeModuleInfo || {};
    const processFirstSkuAuditInfo = processFirstItemId
      ? await this.weappTradeModuleService.getItemAuditInfo({
          kdtId,
          itemId: processFirstItemId,
          sceneGroupId,
        })
      : {};
    // 交易组件开通
    const isOpen = status === EnumTradeModuleInfoStatus.open;
    // 交易组件封禁
    const isBanned = status === EnumTradeModuleInfoStatus.banned;
    // 交易组件开通拒绝
    const isReject = status === EnumTradeModuleInfoStatus.failed;
    // 小程序版本检查
    const weappVersionCheckSuccess =
      lodash.get(tradeModuleInfo, 'weappVersionCheckStatus', '') ===
      EnumWeappVersionCheckStatus.success;
    // 上传商品并审核成功
    const spuAuditSuccess =
      lodash.get(tradeModuleInfo, 'accessSpuAuditStatus', '') === EnumAccessSpuAuditStatus.success;
    // 发起一笔订单并支付成功
    const payOrderSuccess =
      lodash.get(tradeModuleInfo, 'accessPayOrderStatus', '') === EnumAccessPayOrderStatus.success;
    // 物流接口调用成功
    const sendDeliverySuccess =
      lodash.get(tradeModuleInfo, 'accessSendDeliveryStatus', '') ===
      EnumAccessSendDeliveryStatus.success;
    // 售后接口调用成功
    const addAfterSaleSuccess =
      lodash.get(tradeModuleInfo, 'accessAddAftersaleStatus', '') ===
      EnumAccessAddAftersaleStatus.success;
    // 商品接口调试完成
    const spuAuditFinished =
      lodash.get(tradeModuleInfo, 'accessSpuAuditStatus', '') === EnumAccessSpuAuditStatus.finish;
    // 订单接口调试完成
    const payOrderFinished =
      lodash.get(tradeModuleInfo, 'accessPayOrderStatus', '') === EnumAccessPayOrderStatus.finish;
    // 物流接口调试完成
    const sendDeliveryFinished =
      lodash.get(tradeModuleInfo, 'accessSendDeliveryStatus', '') ===
      EnumAccessSendDeliveryStatus.finish;
    // 售后接口调试完成
    const addAfterSaleFinished =
      lodash.get(tradeModuleInfo, 'accessAddAftersaleStatus', '') ===
      EnumAccessAddAftersaleStatus.finish;
    // 发版接入任务完成
    const accessDeployWxaFinish =
      lodash.get(tradeModuleInfo, 'accessDeployWxaStatus', '') === EnumAccessDeployWxaStatus.finish;
    // 测试接入任务状态
    const accessTestApiFinish =
      lodash.get(tradeModuleInfo, 'accessTestApiStatus', '') === EnumAccessTestApiStatus.finish;
    // 首笔订单已付款
    const processFirstOrderPayed =
      (spuAuditSuccess || spuAuditFinished) &&
      (payOrderSuccess || payOrderFinished) &&
      lodash.get(tradeModuleInfo, 'processFirstOrderStatus', '') >=
        EnumProcessFirstOrderStatus.payed;
    // 首笔订单完成
    const processFirstOrderFinish =
      (spuAuditSuccess || spuAuditFinished) &&
      (payOrderSuccess || payOrderFinished) &&
      (sendDeliverySuccess || sendDeliveryFinished) &&
      (addAfterSaleSuccess || addAfterSaleFinished) &&
      lodash.get(tradeModuleInfo, 'processFirstOrderStatus', '') ===
        EnumProcessFirstOrderStatus.afterSaleFinish;
    // 商家选择第一个商品同步成功
    const processFirstSkuSuccess =
      (spuAuditSuccess || spuAuditFinished) &&
      lodash.get(processFirstSkuAuditInfo, 'editStatus', '') ===
        EnumWeappTradeModuleItemAuditStatus.success;
    // 交易组件是否启用
    const isEnable =
      spuAuditFinished &&
      payOrderFinished &&
      sendDeliveryFinished &&
      addAfterSaleFinished &&
      accessDeployWxaFinish &&
      accessTestApiFinish;
    // 未完成的检查项
    const needAccessItemList = [
      spuAuditFinished || EnumTradeModuleAccessInfoItem.spuAuditFinished,
      payOrderFinished || EnumTradeModuleAccessInfoItem.payOrderFinished,
      sendDeliveryFinished || EnumTradeModuleAccessInfoItem.sendDeliveryFinished,
      addAfterSaleFinished || EnumTradeModuleAccessInfoItem.addAfterSaleFinished,
      accessTestApiFinish || EnumTradeModuleAccessInfoItem.testApiFinished,
      accessDeployWxaFinish || EnumTradeModuleAccessInfoItem.deployWxaFinished,
    ].filter(item => item !== true);
    return {
      ...(tradeModuleInfo || {}),
      isOpen,
      isBanned,
      isReject,
      weappVersionCheckSuccess,
      processFirstSkuSuccess,
      processFirstSkuAuditInfo,
      processFirstOrderPayed,
      processFirstOrderFinish,
      isEnable,
      needAccessItemList,
    };
  }

  getWeixinVideoInfo() {
    const { ctx } = this;
    const { kdtId } = ctx;
    return this.weappAccountService.getWeixinVideoInfo({
      kdtId,
    });
  }

  isOrganizationCert(principalInfo: any) {
    // 未认证
    if (!principalInfo) {
      return false;
    }
    // 个人主体
    if (principalInfo.principalCertType === 1) {
      return false;
    }

    return true;
  }

  getStoreBasicCheckIsOkWithAllResult(storeBasicCheckResult: any[]) {
    const [principalInfo, wscWebImConfig, shopAddressList, lifecycle] = storeBasicCheckResult;
    if (!this.isOrganizationCert(principalInfo)) {
      return false;
    }

    // 未开通联系客服
    if (!wscWebImConfig || wscWebImConfig.value === '0') {
      return false;
    }
    // 退货地址为空
    if (!shopAddressList || shopAddressList.total <= 0) {
      return false;
    }
    // 店铺到期
    if (
      !lifecycle ||
      !lifecycle.endTime ||
      new Date(lifecycle.endTime).getTime() - new Date().getTime() <= 0
    ) {
      return false;
    }
    return true;
  }

  getStoreBasicCheckResult() {
    const { kdtId } = this.ctx;
    const getPrincipalInfoPromise = this.customerUnifiedCertQueryService
      .queryPrincipalMsg({
        sourceId: `${kdtId}`,
        sourceIdType: 'KDT_ID',
        unifiedCertType: 1,
      })
      .then(res => {
        this.ctx.toLog('查询店铺主体认证信息', !!res);
        return res;
      });
    const getShopWscWebImConfigPromise = this.shopConfigReadService
      .queryShopConfig(kdtId, 'show_wsc_web_im')
      .then(res => {
        this.ctx.toLog('查询店铺客服配置', res);
        return res;
      });
    const getShopAddressListPromise = this.shopAddressOuterService
      .queryShopAddressList({
        kdtId,
        addressTypeValues: [1],
        pageSize: 10,
        pageNum: 1,
      })
      .then(res => {
        this.ctx.toLog('查询店铺退货地址信息', res);
        return res;
      });
    const getShopProdsPromise = this.prodReadService.queryShopProds(kdtId).then(prodLifecycle => {
      this.ctx.toLog('查询店铺服务期', prodLifecycle);
      const prodCode = utils.getProdCode(
        this.ctx.shopType,
        this.ctx.shopRole,
        this.ctx.shopTopic as number,
      );
      return prodLifecycle.filter((item: { prodCode: any }) => item.prodCode === prodCode)[0];
    });
    return Promise.all([
      getPrincipalInfoPromise,
      getShopWscWebImConfigPromise,
      getShopAddressListPromise,
      getShopProdsPromise,
    ]);
  }

  getMiniAppInfoCheckIsOkWithAllResult(miniAppInfoCheckResult: any[]) {
    const weappVersionInfo = lodash.get(miniAppInfoCheckResult, ['1']);
    const weappTradeModuleIsAuth = lodash.get(miniAppInfoCheckResult, ['2']);
    const shopAddressList = lodash.get(miniAppInfoCheckResult, ['3']);
    // 小程序版本不达标
    if (!weappVersionInfo?.isUsableVersion) {
      return false;
    }
    // 自定义交易组件未授权
    if (!weappTradeModuleIsAuth) {
      return false;
    }
    // 退货地址为空
    if (!shopAddressList || shopAddressList.total <= 0) {
      return false;
    }
    return true;
  }

  getMiniAppInfoCheckResult() {
    const { kdtId } = this.ctx;
    const getMiniAppAccountInfoPromise = this.getMiniAppAccountInfo().then(
      async miniAppAccountInfo => {
        // 小程序是否为非个人认证
        const isMiniAppOrganizationCert =
          miniAppAccountInfo &&
          miniAppAccountInfo.verifyType > -1 &&
          miniAppAccountInfo.principalName &&
          miniAppAccountInfo.principalName !== '个人';

        // 是否为非代注册小程序
        const isNotCreateFromProxy = miniAppAccountInfo && miniAppAccountInfo.createdFrom !== 2;

        // 类目信息
        let categories = [];
        if (miniAppAccountInfo && [2, 4, 5, 6].indexOf(miniAppAccountInfo?.createdFrom) !== -1) {
          const categoriesData = await this.weappRegisterProxyService.getCategory({
            kdtId: this.getRootKdtId(),
          });
          categories = lodash.get(categoriesData, 'categories', []);
        } else {
          categories = lodash.get(miniAppAccountInfo, 'wxCategoryInfos', []);
        }

        const isInSupportWeappCategories = await this.isInSupportWeappCategories(categories);

        return {
          isMiniAppOrganizationCert,
          isNotCreateFromProxy,
          miniAppAccountInfo,
          isInSupportWeappCategories,
        };
      },
    );
    const checkWeappVersionPromise = this.weappTradeModuleService
      .checkWeappVersion({
        kdtId,
      })
      .then(res => {
        return {
          curVersion: res.releasedVersion,
          isUsableVersion: res.checkResult === 1,
        };
      })
      .catch(() => {
        return {
          curVersion: '',
          isUsableVersion: false,
        };
      });
    const getShopAddressListPromise = this.shopAddressOuterService
      .queryShopAddressList({
        kdtId,
        addressTypeValues: [1],
        pageSize: 10,
        pageNum: 1,
      })
      .then(res => {
        this.ctx.toLog('查询店铺退货地址信息', res);
        return res;
      });
    return Promise.all([
      getMiniAppAccountInfoPromise,
      checkWeappVersionPromise,
      this.getWeappTradeModuleIsAuth(),
      getShopAddressListPromise,
    ]);
  }

  getWeappTradeModuleIsAuth() {
    const { ctx } = this;
    const { kdtId } = ctx;
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    return this.channelCoreAccountService
      .queryChannelAuthInfo({
        accountType,
        businessType: 1,
        kdtId,
      })
      .then(res => {
        if (res && res.authInfoDTOS) {
          const weappTradeAuth =
            res.authInfoDTOS.find((item: { funcId: number }) => item.funcId === 85) || {};
          return weappTradeAuth.funcState === 1;
        }
        return false;
      });
  }

  /**
   * 当前店铺小程序类目是否在交易组件允许类目范围内
   * return boolean
   */
  async isInSupportWeappCategories(categories: any[]) {
    const { apolloClient } = this.ctx;
    const supportWeappCategories = await apolloClient.getConfig({
      appId: 'wsc-pc-channel',
      namespace: `wsc-pc-channel.wxvideo`,
      key: `support-weapp-categories`,
    });
    const isExistSupportWeappCategories =
      lodash.intersectionBy(supportWeappCategories || [], categories, (category: any) => {
        return `${category.firstId || ''}_${category.secondId || ''}_${category.thirdId || ''}`;
      }).length > 0;
    return isExistSupportWeappCategories;
  }

  /**
   * 当前店铺是否在白名单内
   * 若返回数组为空 则代表全量
   */
  public async isInWxModuleWhiteList() {
    const { apolloClient, kdtId } = this.ctx;
    const queryStr = [
      EnumWhiteListQueryList.shopType,
      EnumWhiteListQueryList.shopTopic,
      EnumWhiteListQueryList.shopRole,
    ].reduce((cur, queryKey) => {
      const queryVal = this.ctx[queryKey];
      // queryVal为0的情况也不加参数
      // 例 教育单店shopType=0&&shopTopic=1&shopRole=0 则 key=video-wechat-video.shopTopic=1
      if (queryVal) {
        cur += `.${queryKey}=${queryVal}`;
      }
      return cur;
    }, '');
    const whitelist = this.channelName
      ? await apolloClient.getConfig({
          appId: 'wsc-pc-channel',
          namespace: `wsc-pc-channel.whitelist`,
          key: `video-wechat-video${queryStr}`,
          // (this.channelType === channelTypeEnum.miniApp ? '' : `${this.channelType}-`) +
          // this.channelName,
        })
      : null;
    if (!whitelist) {
      return false;
    }
    if (whitelist.length === 0) {
      return true;
    }

    return whitelist.includes(Number(kdtId));
  }

  public getWechatTempMediaTaskId(resourceUrl: string, kdtId: number) {
    return this.weappTradeModuleService.transformQiNiuUrlToWechatUrlAsync({
      qiNiuUrl: resourceUrl,
      kdtId,
    });
  }

  public getTransformationalWechatUrlByTaskId(taskId: string) {
    return this.weappTradeModuleService.getTransformationalWechatUrlByTaskId({ taskId });
  }

  // 获取微信临时图片
  public asyncGetWechatTempPic(resourceUrl: string, kdtId: number) {
    return new Promise<string>((resolve, reject) => {
      this.getWechatTempMediaTaskId(resourceUrl, kdtId)
        .then(taskId => {
          const tryCount = 10;
          const tryDelay = 1000;
          const pollingRequest = (currentTryCount = 1) => {
            this.getTransformationalWechatUrlByTaskId(taskId)
              .then(({ status, wechatUrl }) => {
                if (status === -1) {
                  reject('图片上传失败');
                } else if (status === 1) {
                  if (currentTryCount >= tryCount) {
                    reject('请求超时，请重试');
                  } else {
                    setTimeout(() => {
                      pollingRequest(currentTryCount + 1);
                    }, tryDelay);
                  }
                } else if (status === 3) {
                  reject('任务已过期，请重试');
                } else {
                  resolve(wechatUrl);
                }
              })
              .catch(reject);
          };
          pollingRequest();
        })
        .catch(reject);
    });
  }

  /**
   * 获取有赞授权品牌信息
   */
  @API('GET', 'get-valid-brand-cert.json')
  public async getBrandMsg() {
    const brandMsg = await this.unifiedCertQueryService
      .queryBrandMsg({
        sourceId: `${this.ctx.kdtId}`,
        sourceIdType: 'KDT_ID',
      })
      .then((res: IQueryBrandMsgRes) => {
        return {
          brandCertDTOList:
            (res &&
              res.brandCertDTOList &&
              res.brandCertDTOList.filter(item => item.status === 'PASS')) ||
            [],
        };
      });
    return this.ctx.json(0, 'success', brandMsg);
  }

  /**
   * 获取未设置新类目的商品数量
   */
  @API('GET', 'list-items-page-no-category.json')
  public async getListItemsPagedNoCategory() {
    const res = await this.itemQueryService
      .listItemsPagedNoCategory({
        kdtId: this.ctx.kdtId,
      })
      .then(data => {
        return {
          count: data.count,
        };
      });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 获取商品审核数量
   */
  @API('GET', 'get-sku-review-count.json')
  public async getSkuReviewCount() {
    const { kdtId } = this.ctx;
    const reviewCount = await this.wechatChannelConsoleService
      .listWechatChannelPaged({
        kdtId,
      })
      .then(data => {
        return data.totalCount;
      });
    const refuseCount = await this.wechatChannelConsoleService
      .listWechatChannelPaged({
        kdtId,
        auditStatus: 3,
      })
      .then(data => {
        return data.totalCount;
      });
    return this.ctx.json(0, 'success', {
      reviewCount,
      refuseCount,
    });
  }

  /**
   * 获取交易组件商品同步状态
   */
  @API('GET', 'weapp-trade-module-apply-key.json')
  public async getWeappTradeModuleKey() {
    const res = await this.shopConfigReadService.queryShopConfig(
      this.ctx.kdtId,
      'weapp_trade_module_status',
    );
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 获取视频号所有三级类目
   */
  @API('GET', 'all-category.json')
  public async getAllCategory() {
    const { kdtId, userId } = this.ctx;
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    const res = await this.weappAccountService.getTradeModuleCategoryTree({
      businessType: 1,
      userId,
      accountType,
      kdtId,
    });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 回补微信交易组件类目审核状态
   */
  @API('GET', 'makeup-trademodel-category.json')
  public async makeupTradeModelCategory() {
    const { kdtId, userId } = this.ctx;
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    const res = await this.weappAccountService.makeupTradeModelCategory({
      businessType: 1,
      userId,
      accountType,
      kdtId,
    });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 获取视频号类目上传审核结果
   */
  @API('GET', 'video-category-audit-info.json')
  public async getVideoCategoryAuditInfo() {
    const { kdtId, userId } = this.ctx;
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    const res = await this.weappAccountService.listTradeModuleCategoryAuditInfo({
      businessType: 1,
      userId,
      accountType,
      kdtId,
    });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 回补微信交易组件品牌审核状态
   */
  @API('GET', 'makeup-trade-module-auditing-brands.json')
  public async makeupTradeModuleAuditingBrands() {
    const { kdtId, userId } = this.ctx;
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    const res = await this.weappAccountService.makeupTradeModuleAuditingBrands({
      businessType: 1,
      userId,
      accountType,
      kdtId,
    });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 获取视频号品牌上传审核结果
   */
  @API('GET', 'video-brand-audit-info.json')
  public async getVideoBrandAuditInfo() {
    const { kdtId, userId } = this.ctx;
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    const res = await this.weappAccountService.listTradeModuleBrandAuditInfo({
      businessType: 1,
      userId,
      accountType,
      kdtId,
    });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 获取交易组件能否开启商品同步
   */
  @API('GET', 'get-can-wx-video-sync.json')
  public async getCanWxVideoSync() {
    const canWxVideoSync = await this.selfrunGoodsQueryService.canWxVideoSync(this.ctx.kdtId);
    return this.ctx.json(0, 'success', canWxVideoSync);
  }

  /**
   * 获取全店商品审核状态
   */
  @API('GET', 'all-sku-review.json')
  public async getAllSkuReviewStatus() {
    const res = await this.platformKdtTaskService.getTaskStatus(this.ctx.kdtId);
    this.ctx.toLog('获取全店商品审核状态', res);
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 开通当前店铺新类目白名单
   */
  @API('POST', 'open-new-category-whitelist.json')
  public async openNewCategoryWhitelist() {
    const res = await this.categoryBackService.openNewCategory({
      kdtId: this.ctx.kdtId,
    });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 商品同步开通配置,并推送商品同步消息
   */
  @API('POST', 'update-trade-module-config.json')
  public async updateTradeModuleConfig() {
    const { kdtId, userId, request } = this.ctx;
    const { status } = request.body;
    this.validator.required(status, 'status 不能为空');
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    const res = await this.weappAccountService.updateTradeModuleConfig({
      businessType: 1,
      userId,
      accountType,
      kdtId,
      status,
    });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 微信小程序申请开通购物组件
   */
  @API('POST', 'apply-weapp-trade-module.json')
  public async applyWeappTradeModule() {
    const { kdtId } = this.ctx;
    const res = await this.weappTradeModuleService.applyTradeModule({
      kdtId,
    });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 检查申请状态
   */
  @API('POST', 'check-apply-trade-module-status.json')
  public async checkApplyTradeModuleStatus() {
    const { kdtId, userId } = this.ctx;
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    const res = await this.weappAccountService.checkApplyTradeModuleStatus({
      businessType: 1,
      userId,
      accountType,
      kdtId,
    });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 申请购物组件类目
   */
  @API('POST', 'apply-trade-module-category.json')
  public async applyTradeModuleCategory() {
    const { kdtId, userId, request } = this.ctx;
    const { categoryList } = request.body;
    this.validator.required(categoryList, 'categoryList 不能为空');
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    const principalInfo = await this.customerUnifiedCertQueryService.queryPrincipalMsg({
      sourceId: `${kdtId}`,
      sourceIdType: 'KDT_ID',
      unifiedCertType: 1,
      originalPhoto: true,
    });
    this.ctx.toLog('申请购物组件类目获取主体信息', !!principalInfo);
    const merchantOrganizationCertResult = lodash.get(
      principalInfo,
      'merchantOrganizationCertResult',
      {},
    );
    // 营业执照
    const license =
      merchantOrganizationCertResult.papersType === 10
        ? // 资产没有 type 区分是 营业执照号还是组织机构证代码，需要用或来取，最终解释 @明城
          merchantOrganizationCertResult.businessPhotoUrl ||
          merchantOrganizationCertResult.organizationCodePhotoUrl
        : merchantOrganizationCertResult.creditPhotoUrl;
    const newLicense = license
      ? await this.storageQiniuReadService
          .refreshPrivateUrl({
            expires: 3600 * 48,
            oldPrivateUrlDTOS: [
              {
                oldPrivateUrl: license.replace('dn-kdt-private.qbox.me', 'pr.yzcdn.cn'),
              },
            ],
          })
          .then(([res]: IRefreshPrivateUrlRes) => (res && res.newPrivateUrl) || '')
      : '';
    this.ctx.toLog('营业执照替换非私域url', !!newLicense);
    let licenseUrlToWxTempUrl = '';
    if (newLicense) {
      try {
        const tempRes = await this.asyncGetWechatTempPic(newLicense, kdtId);
        licenseUrlToWxTempUrl = tempRes;
      } catch (e) {
        this.ctx.toLog(`转营业执照为微信临时链接${newLicense}失败:`, e);
      }
    }
    const res = await this.weappAccountService.batchApplyTradeModuleCategory({
      businessType: 1,
      userId,
      accountType,
      kdtId,
      list: categoryList.map((category: any) => ({
        ...category,
        license: licenseUrlToWxTempUrl,
        businessType: 1,
        userId,
        accountType,
        kdtId,
      })),
    });
    return this.ctx.json(0, 'success', res);
  }

  @API('GET', 'get-brand-list-info.json')
  public async getBrandList() {
    const { kdtId, userId } = this.ctx;
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    const principalInfo = await this.customerUnifiedCertQueryService.queryPrincipalMsg({
      sourceId: `${kdtId}`,
      sourceIdType: 'KDT_ID',
      unifiedCertType: 1,
      originalPhoto: true,
    });
    this.ctx.toLog('申请购物组件品牌获取主体信息', !!principalInfo);
    /**
     * 所有上传到微信交易组件的图片需要
     * 1.原图
     * 2.刷新私有链接，延长有效期
     */
    const merchantOrganizationCertResult = lodash.get(
      principalInfo,
      'merchantOrganizationCertResult',
      {},
    );
    // 营业执照
    const license =
      merchantOrganizationCertResult.papersType === 10
        ? // 资产没有 type 区分是 营业执照号还是组织机构证代码，需要用或来取，最终解释 @明城
          merchantOrganizationCertResult.businessPhotoUrl ||
          merchantOrganizationCertResult.organizationCodePhotoUrl
        : merchantOrganizationCertResult.creditPhotoUrl;
    const newLicense = license
      ? await this.storageQiniuReadService
          .refreshPrivateUrl({
            expires: 3600 * 48,
            oldPrivateUrlDTOS: [
              {
                oldPrivateUrl: license.replace('dn-kdt-private.qbox.me', 'pr.yzcdn.cn'),
              },
            ],
          })
          .then(([res]: IRefreshPrivateUrlRes) => (res && res.newPrivateUrl) || '')
      : '';
    this.ctx.toLog('营业执照刷新私有链接', !!newLicense);
    const brandMsg = await this.unifiedCertQueryService.queryBrandMsg({
      sourceId: `${kdtId}`,
      sourceIdType: 'KDT_ID',
      originalPhoto: true,
    });
    this.ctx.toLog('获取品牌信息', brandMsg);

    const brandCertDTOList = (brandMsg && brandMsg.brandCertDTOList) || [];
    const brandCertList = await Promise.all(
      brandCertDTOList.map((brandCertDTO: Partial<IBrandCertDTO>) => {
        const oldPrivateUrlDTOS: IOldPrivateUrlDTO[] = [];
        if (brandCertDTO.authorizationPapers) {
          oldPrivateUrlDTOS.push({
            oldPrivateUrl: brandCertDTO.authorizationPapers.replace(
              'dn-kdt-private.qbox.me',
              'pr.yzcdn.cn',
            ),
          });
        }
        if (brandCertDTO.trademarkPapers) {
          oldPrivateUrlDTOS.push({
            oldPrivateUrl: brandCertDTO.trademarkPapers.replace(
              'dn-kdt-private.qbox.me',
              'pr.yzcdn.cn',
            ),
          });
        }
        if (brandCertDTO.supplementaryPaperUrlList) {
          brandCertDTO.supplementaryPaperUrlList.forEach(supplementaryPaperUrl => {
            oldPrivateUrlDTOS.push({
              oldPrivateUrl: supplementaryPaperUrl.replace('dn-kdt-private.qbox.me', 'pr.yzcdn.cn'),
            });
          });
        }
        return this.storageQiniuReadService.refreshPrivateUrl({
          expires: 3600 * 48,
          oldPrivateUrlDTOS,
        });
      }),
    ).then((urlResList: IRefreshPrivateUrlRes[]) => {
      this.ctx.toLog('品牌信息证照刷新私有链接', !!urlResList);
      return urlResList.map((urlList: IRefreshPrivateUrlRes, index: number) => {
        const brandCertDTO = brandCertDTOList[index];
        let authorizationPapers = '';
        let trademarkPapers = '';
        let supplementaryPaperUrlList: string[] = [];
        if (brandCertDTO.authorizationPapers) {
          authorizationPapers = lodash.get(urlList.shift(), 'newPrivateUrl', '');
        }
        if (brandCertDTO.trademarkPapers) {
          trademarkPapers = lodash.get(urlList.shift(), 'newPrivateUrl', '');
        }
        if (
          brandCertDTO.supplementaryPaperUrlList &&
          brandCertDTO.supplementaryPaperUrlList.length > 0
        ) {
          supplementaryPaperUrlList = urlList.reduce((cur, url) => {
            if (url && url.newPrivateUrl) {
              cur.push(url.newPrivateUrl);
            }
            return cur;
          }, supplementaryPaperUrlList);
        }
        return {
          ...brandCertDTO,
          authorizationPapers,
          trademarkPapers,
          supplementaryPaperUrlList,
        };
      });
    });

    const brandList: any[] = await Promise.all(
      brandCertList.map((brandCert: any) =>
        this.principalInfoQueryService.queryCompanyTrademark({
          number: brandCert.brandRegisterNo,
          partnerId: 'enable_crm',
        }),
      ),
    ).then((companyBrandInfoList: Array<Partial<IQueryCompanyTrademarkRes>>) => {
      this.ctx.toLog('获取所有品牌启信宝信息', companyBrandInfoList);
      return companyBrandInfoList.map(
        (companyBrandInfo: Partial<IQueryCompanyTrademarkRes>, index: number) => {
          const brandCert = brandCertList[index];
          let trademarkAuthorizationPeriod;
          let trademarkApplicationTime;
          try {
            // 这块三方接口给到的时间可能为'-'，format可能会有问题，通过try catch吞掉报错，避免影响业务
            trademarkAuthorizationPeriod =
              brandCert.brandStartTime && brandCert.brandEndTime
                ? `${formatDate(new Date(brandCert.brandStartTime), 'YYYY/MM/DD')}-${formatDate(
                    new Date(brandCert.brandEndTime),
                    'YYYY/MM/DD',
                  )}`
                : '';
            trademarkApplicationTime = companyBrandInfo.applyDate
              ? formatDate(new Date(companyBrandInfo.applyDate), 'YYYY-MM-DD HH:mm:ss')
              : companyBrandInfo.applyDate;
          } catch (error) {}
          // 有赞品牌经营类型
          const YZAuthorizationLevelType = {
            AUTHORIZE_UNKOWN: 0, // 未知
            AUTHORIZE_SELF: 1, // 自有品牌
            AUTHORIZE_ONE: 2, // 一级授权
            AUTHORIZE_TWO: 3, // 二级授权
            AUTHORIZE_THREEANDMORE: 4, // 三级授权及以上
          };
          // 微信品牌经营类型
          const WXBrandManagementType = {
            SELF: 1, // 自有品牌
            AGENT: 2, // 代理品牌
            NONE: 3, // 无品牌
          };
          // 有赞品牌经营类型映射微信
          const brandManagementTypeYZ2WX: { [key: number]: number } = {
            [YZAuthorizationLevelType.AUTHORIZE_UNKOWN]: WXBrandManagementType.NONE,
            [YZAuthorizationLevelType.AUTHORIZE_SELF]: WXBrandManagementType.SELF,
            [YZAuthorizationLevelType.AUTHORIZE_ONE]: WXBrandManagementType.AGENT,
            [YZAuthorizationLevelType.AUTHORIZE_TWO]: WXBrandManagementType.AGENT,
            [YZAuthorizationLevelType.AUTHORIZE_THREEANDMORE]: WXBrandManagementType.AGENT,
          };
          return {
            unifiedBrandId: brandCert.unifiedBrandId, // 品牌唯一Id
            brandManagementType: brandManagementTypeYZ2WX[brandCert.authorizationLevel || 0],
            brandAuditType: brandCert.trademarkStatus,
            brandWording: brandCert.brandName,
            commodityOriginType: 2, // 默认非进口
            saleAuthorization: brandCert.authorizationPapers
              ? [brandCert.authorizationPapers, ...brandCert.supplementaryPaperUrlList] // 补充材料加入销售授权书
              : [],
            trademarkRegistrantNu: brandCert.brandRegisterNo,
            trademarkRegistrationApplication: brandCert.trademarkPapers
              ? [brandCert.trademarkPapers]
              : [],
            trademarkAuthorizationPeriod,
            trademarkType: brandCert.trademarkCategory,
            trademarkApplicationTime,
            trademarkRegistrationCertificate: brandCert.trademarkPapers
              ? [brandCert.trademarkPapers]
              : [],
            trademarkApplicant: companyBrandInfo.ename,
            trademarkRegistrant: brandCert.brandRegistrant,
            trademarkChangeCertificate: brandCert.supplementaryPaperUrlList || [], // 商标变更证明
            license: newLicense,
            businessType: 1,
            userId,
            accountType,
            kdtId,
          };
        },
      );
    });

    return this.ctx.json(0, 'success', brandList);
  }

  /**
   * 申请购物组件品牌
   */
  @API('POST', 'apply-trade-module-brand.json')
  public async applyTradeModuleBrand() {
    const { kdtId, userId, request } = this.ctx;
    const { brandList } = request.body;
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    const res = await this.weappAccountService.batchApplyTradeModuleBrand({
      businessType: 1,
      userId,
      accountType,
      kdtId,
      list: brandList,
    });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 获取小程序基本信息检查结果
   */
  @API('GET', 'mini-app-check-result.json')
  public async getMiniAppCheckResultJSON() {
    const [
      miniAppAccountCheckInfo,
      weappVersionInfo,
      weappTradeModuleIsAuth,
      shopAddressList,
    ] = await this.getMiniAppInfoCheckResult();

    return this.ctx.json(0, 'success', {
      miniAppAccountCheckInfo,
      weappVersionInfo,
      weappTradeModuleIsAuth,
      shopAddressList,
    });
  }

  /**
   * 获取店铺基本信息检查结果
   */
  @API('GET', 'store-basic-check-result.json')
  public async getStoreBasicCheckResultJSON() {
    const [
      principalInfo,
      wscWebImConfig,
      shopAddressList,
      lifecycle,
    ] = await this.getStoreBasicCheckResult();

    // 若主体认证非个人 通知平台治理侧需要根据认证情况确定商品可售类目
    if (principalInfo && principalInfo.principalCertType !== 1) {
      const addShopAuditRes = await this.platformKdtAuditService.addShopAudit(this.ctx.kdtId);
      this.ctx.toLog('通知平台治理侧需要根据认证情况确定商品可售类目', addShopAuditRes);
    }

    return this.ctx.json(0, 'success', {
      principalInfo,
      wscWebImConfig,
      shopAddressList,
      lifecycle,
    });
  }

  /**
   * 获取微信小程序是否有发版信息
   */
  @API('GET', 'has-release-version.json')
  public async hasReleaseVersion() {
    const versionInfo = await this.getVersionInfo();
    const releaseVersion = lodash.get(versionInfo, 'releaseVersion', '');
    const hasReleaseVersion = !!releaseVersion;
    return this.ctx.json(0, 'success', hasReleaseVersion);
  }

  /**
   * 交易组件接入信息
   */
  @API('GET', 'trade-module-access-info.json')
  public async getTradeModuleAccessInfo() {
    const res = await this.getTradeModuleInfo();

    return this.ctx.json(0, 'success', res);
  }

  /**
   * 交易组件启用检查 版本信息
   */
  @API('GET', 'trade-module-enable-check-version.json')
  public async getTradeModuleEnableCheckVersion() {
    const { kdtId } = this.ctx;
    const res = await this.weappTradeModuleService.checkWeappVersion({
      kdtId,
    });

    return this.ctx.json(0, 'success', {
      curVersion: res.releasedVersion,
      curAuditVersion: res.userVersion,
      isUsableVersion: res.checkResult === 1,
      isUsableAuditVersion: res.checkResult === 2,
    });
  }

  /**
   * 交易组件启用检查 获取推荐商品
   */
  @API('GET', 'trade-module-enable-recommend.json')
  public async getTradeModuleEnableRecommend() {
    const { kdtId } = this.ctx;
    const res = await this.itemRecommendService.getRecommendItem({
      kdtId,
    });

    return this.ctx.json(0, 'success', res);
  }

  /**
   * 交易组件启用检查 获取系统创建商品
   */
  @API('GET', 'or-create-recommend.json')
  public async getOrCreateRecommendItem() {
    const { kdtId, request } = this.ctx;
    const { onlineKdtId } = request.query;
    const res = await this.weappTradeModuleService.getOrCreateRecommendItem({
      kdtId,
      onlineKdtId,
    });

    return this.ctx.json(0, 'success', res);
  }

  /**
   * 交易组件启用检查 创建系统商品
   */
  @API('POST', 'create-recommend.json')
  public async createRecommendItem() {
    const { kdtId, request } = this.ctx;
    const { onlineKdtId } = request.body;
    const res = await this.weappTradeModuleService.createRecommendItem({
      kdtId,
      onlineKdtId,
    });

    return this.ctx.json(0, 'success', res);
  }

  /**
   * 交易组件启用检查 获取当前系统是否创建商品
   */
  @API('GET', 'is-create-recommend.json')
  public async getRecommendItemId() {
    const { ctx } = this;
    const { kdtId } = ctx;

    const tradeModuleAccessInfo = await this.getTradeModuleInfo();
    const itemId = tradeModuleAccessInfo?.recommendItemId;

    if (!itemId) {
      return ctx.json(0, 'success', false);
    }

    const shopMetaInfo = ctx.getState('shopMetaInfo');
    const isUnifiedChainStoreSolution = checkUnifiedChainStoreSolution(shopMetaInfo);
    const isEduChainStoreV4 = checkEduChainStoreV4(shopMetaInfo);
    // 连锁L、教育连锁V4 需要获取网店的商品信息
    if (isUnifiedChainStoreSolution || isEduChainStoreV4) {
      if (!tradeModuleAccessInfo?.onlineKdtId) {
        ctx.toLog('获取商品详情时，未取到网店商品Id', {
          kdtId,
          itemId,
        });
      }

      const res = await this.icItemQueryService
        .getById({
          kdtId: tradeModuleAccessInfo?.onlineKdtId as number,
          itemId,
        })
        .catch(() => false);

      return ctx.json(0, 'success', !!res);
    }

    const res = await this.icItemQueryService
      .getById({
        kdtId,
        itemId,
      })
      .catch(() => false);

    return this.ctx.json(0, 'success', !!res);
  }

  /**
   * 交易组件启用检查 获取推荐的商品详情信息
   */
  @API('GET', 'get-item-detail-by-id.json')
  public async getItemDetail() {
    const { kdtId, request } = this.ctx;
    const { itemId } = request.query;
    const res = await this.itemRecommendService.getItemDetail({
      kdtId,
      itemId,
    });

    return this.ctx.json(0, 'success', res);
  }

  /**
   * 交易组件启用检查 获取微信商品审核状态
   */
  @API('GET', 'query-audit-status.json')
  public async queryAuditStatus() {
    const { kdtId, request } = this.ctx;
    const { itemId } = request.query;
    const res = await this.weappTradeModuleService.getItemAuditInfo({
      kdtId,
      itemId,
    });

    return this.ctx.json(0, 'success', res);
  }

  /**
   * 交易组件启用检查 同步商品到微信
   */
  @API('POST', 'sync-goods-item.json')
  public async syncItem() {
    const { kdtId, request } = this.ctx;
    const { itemId } = request.body;
    const res = await this.weappTradeModuleService.syncItem({
      kdtId,
      itemId,
    });

    return this.ctx.json(0, 'success', res);
  }

  /**
   * 交易组件启用检查 更新交易组件接入流程关联信息
   */
  @API('POST', 'update-trade-module-access-info.json')
  public async updateTradeModuleAccessInfo() {
    const { kdtId, request } = this.ctx;
    const { processFirstItemId } = request.body;
    const res = await this.weappTradeModuleService.updateProcessFirstItemId({
      kdtId,
      processFirstItemId,
    });

    return this.ctx.json(0, 'success', res);
  }

  /**
   *  删除交易组件的系统推荐商品
   */
  @API('POST', 'delete-system-recommend-item.json')
  public async deleteSystemRecommendItem() {
    const { kdtId } = this.ctx;
    const res = await this.weappTradeModuleService.deleteSystemRecommendItem({
      kdtId,
    });

    return this.ctx.json(0, 'success', res);
  }

  /**
   * 批量推广商品
   */
  @API('POST', 'batch-add-item.json')
  public async batchAddItem() {
    const { kdtId, request } = this.ctx;
    const { itemIds } = request.body;
    const operateKdtId = kdtId;
    const res = await this.wechatChannelConsoleService.batchAddItem(operateKdtId, kdtId, itemIds);

    return this.ctx.json(0, 'success', res);
  }

  /**
   * 提交审核
   */
  @API('POST', 'submit-audit.json')
  public async submitAudit() {
    const { kdtId, request } = this.ctx;
    const { itemIdGetParams } = request.body;

    const res = await this.wechatChannelConsoleService.submitAudit({
      itemIdGetReqs: itemIdGetParams,
      operateKdtId: kdtId,
      kdtId,
    });

    return this.ctx.json(0, 'success', res);
  }

  /**
   * 撤销审核
   */
  @API('POST', 'cancel-audit.json')
  public async cancelAudit() {
    const { kdtId, request } = this.ctx;
    const { itemId } = request.body;

    const res = await this.wechatChannelConsoleService.cancelAudit({
      kdtId,
      itemId,
      operateKdtId: kdtId,
    });

    return this.ctx.json(0, 'success', res);
  }

  /**
   * 交易组件启用检查 完成接入任务
   */
  @API('POST', 'finish-access-info.json')
  public async finishAllAccessInfo() {
    const { kdtId, request } = this.ctx;
    const { accessProcessItemStatusList } = request.body;
    const res = await this.weappTradeModuleService.finishAllAccessInfo({
      kdtId,
      accessProcessItemStatusList,
    });

    return this.ctx.json(0, 'success', res);
  }

  /**
   * 获取微信小程序二维码
   */
  @API('GET', 'get-weapp-code.json')
  public async getWeappCode() {
    const { ctx } = this;
    const { kdtId, request } = ctx;
    const { params } = request.query;
    let enterKdtId = kdtId;
    this.validator.required(params, 'params 不能为空');

    const shopMetaInfo = ctx.getState('shopMetaInfo');
    const isUnifiedChainStoreSolution = checkUnifiedChainStoreSolution(shopMetaInfo);
    const isEduChainStoreV4 = checkEduChainStoreV4(shopMetaInfo);
    // 连锁L、教育连锁V4 店铺需要进网店
    if (isUnifiedChainStoreSolution || isEduChainStoreV4) {
      const tradeModuleAccessInfo = await this.getTradeModuleInfo();
      enterKdtId = tradeModuleAccessInfo?.onlineKdtId as number;
    }

    const res = await this.weappQRCodeService.wxaGetCodeUltra({
      kdtId,
      params: {
        ...JSON.parse(params),
        kdtId: enterKdtId,
      },
      page: 'pages/common/blank-page/index',
      hyaLine: false,
    });
    const base64Image = res?.imageBase64 ? `data:image/png;base64,${res?.imageBase64}` : '';

    return ctx.json(0, 'success', base64Image);
  }

  /**
   * 根据商品id获取商品详情
   */
  @API('GET', 'getById.json')
  public async getById() {
    const { kdtId, request } = this.ctx;
    const { itemId } = request.query;
    const res = await this.icItemQueryService.getById({
      kdtId,
      itemId,
    });

    return this.ctx.json(0, 'success', res);
  }

  /**
   * 兼容网店商品 根据商品id获取商品详情
   */
  @API('GET', 'getItemDetailCompatibleOnlineShop.json')
  public async getItemDetailCompatibleOnlineShop() {
    const { ctx } = this;
    const { kdtId, request } = ctx;
    const { itemId } = request.query;

    const shopMetaInfo = ctx.getState('shopMetaInfo');
    const isUnifiedChainStoreSolution = checkUnifiedChainStoreSolution(shopMetaInfo);
    const isEduChainStoreV4 = checkEduChainStoreV4(shopMetaInfo);
    // 连锁L、教育连锁V4 需要获取网店的商品信息
    if (isUnifiedChainStoreSolution || isEduChainStoreV4) {
      const tradeModuleAccessInfo = await this.getTradeModuleInfo();
      if (!tradeModuleAccessInfo?.onlineKdtId) {
        ctx.toLog('获取商品详情时，未取到网店商品Id', {
          kdtId,
          itemId,
        });
      }

      const res = await this.icItemQueryService.getById({
        kdtId: tradeModuleAccessInfo?.onlineKdtId as number,
        itemId,
      });

      return ctx.json(0, 'success', res);
    }

    const res = await this.icItemQueryService.getById({
      kdtId,
      itemId,
    });

    return ctx.json(0, 'success', res);
  }

  /**
   * 优惠券组件是否在白名单
   */
  @API('GET', 'coupon-module-is-gray.json')
  public async isGrayScale() {
    const { kdtId } = this.ctx;
    const res = await this.liveChannelCouponService.isGrayScale(kdtId);

    return this.ctx.json(0, 'success', res);
  }

  /**
   * 开通优惠券组件
   */
  @API('POST', 'open-coupon-module.json')
  public async openCouponModule() {
    const { kdtId } = this.ctx;
    const res = await this.liveChannelCouponService.openCouponModule(kdtId);

    return this.ctx.json(0, 'success', res);
  }

  /**
   * 查询店铺优惠券组件状态
   */
  @API('GET', 'query-coupon-module.json')
  public async queryCouponModule() {
    const { kdtId } = this.ctx;
    const res = await this.liveChannelCouponService.queryCouponModule(kdtId);

    return this.ctx.json(0, 'success', res);
  }

  /**
   * 素材转换成微信临时素材
   * @returns taskId
   */
  @API('POST', 'upload-wechat-temp-media-async.json')
  public async uploadWechatTempMediaAsync() {
    const { kdtId, request } = this.ctx;
    const { resourceUrl } = request.body;
    const res = await this.getWechatTempMediaTaskId(resourceUrl, kdtId);
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 通过taskId获取微信临时素材
   */
  @API('GET', 'get-wechat-temp-media-by-task-id.json')
  public async getWechatTempMediaByTaskId() {
    const { request } = this.ctx;
    const { taskId } = request.query;
    const res = await this.getTransformationalWechatUrlByTaskId(taskId);
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 为交易组件商品图片地址转换微信地址（异步）
   */
  @API('POST', 'trans-form-wechat-url-for-item-url.json')
  public async transformWechatUrlForItemUrlAsync() {
    const { kdtId } = this.ctx;
    const res = await this.weappTradeModuleService.transformWechatUrlForItemUrlAsync({ kdtId });
    return this.ctx.json(0, 'success', res);
  }

  /**
   * 获取所有店铺列表
   */
  @API('GET', 'all-shop-list.json')
  public async getAllShopList() {
    const { kdtId, userId } = this.ctx;
    const shopInfo = this.ctx.getState('shopInfo');
    const hqKdtId = shopInfo.rootKdtId;
    const retailSource = 'wsc-pc-channel';
    const params = {
      // extends 中解析JSON数据
      ...this.ctx.getJsonParams(),
      kdtId,
      hqKdtId,
      adminId: userId,
      retailSource,
    };
    const res = await this.hQStoreSearchService.searchWithDataPermission(params);
    return this.ctx.json(0, 'success', res);
  }
}

export = BasicInfoController;
