import { Router, Index, Metadata } from '@youzan/assets-route-plugin';
import lodash from 'lodash';
import BaseController from './BaseController';

@Router('wechat-video/entry')
class EntryController extends BaseController {
  public async init() {
    this.channelName = 'wechat-video';
    super.init();
  }

  @Index('')
  @Metadata('INDEX')
  async getEntryHtml() {
    const { ctx } = this;

    const miniAppAccountInfo = await this.getMiniAppAccountInfo();
    const miniAppBindInfo = await this.getMiniAppBindInfo();

    ctx.setGlobal('pageData', {
      miniAppAccountInfo,
      isBindWxWeapp: !lodash.isEmpty(miniAppBindInfo),
    });

    await ctx.render('video-app/wechat-video/entry.html');
  }
}

export = EntryController;
