import { Router, API, Inject } from '@youzan/assets-route-plugin';
import MiniAppBaseController from '../BaseController';
import TiktokUnderwritingService from '../../../services/api/channels/tiktok/TiktokUnderwritingService';

@Router('tiktok/underwriting')
class UnderwritingController extends MiniAppBaseController {
  @Inject()
  private readonly tikTokUnderwritingService!: TiktokUnderwritingService;

  public async init() {
    this.channelName = 'tiktok';
    super.init();
  }

  /**
   * 抖音查询商户号和进件信息
   */
  @API('GET', 'query-underwriting-status.json')
  public async queryUnderwritingStatus() {
    const { kdtId, userId } = this.ctx;
    const result = await this.tikTokUnderwritingService.queryMchStatus({
      kdtId,
      adminId: userId,
    });
    await this.ctx.json(0, 'ok', result);
  }

  /**
   * 分店查询抖音商户号和进件信息
   */
  @API('GET', 'query-branch-underwriting-status.json')
  public async queryBranchUnderwritingStatus() {
    const { userId, request } = this.ctx;
    const { kdtId, businessType } = request.query;
    const params: any = {
      kdtId,
      adminId: userId,
    };
    if (businessType) params.businessType = businessType;
    const result = await this.tikTokUnderwritingService.queryMchStatus(params);
    return this.ctx.json(0, 'ok', result);
  }

  /**
   * 更新商户号
   */
  @API('GET', 'update-business-number.json')
  public async updateBusinessNumber() {
    const { request } = this.ctx;
    const { mchId, kdtId } = request.query;

    const data = await this.tikTokUnderwritingService.modifySubShopMch({
      kdtId,
      mchId,
      businessType: 7,
    });

    return this.ctx.json(0, 'success', data);
  }

  /**
   * 抖音提交商户号
   */
  @API('POST', 'submit-merchant.json')
  public async submitMerchant() {
    const { kdtId, userId } = this.ctx;
    const { mchId = 1 } = this.ctx.request.body;
    const result = await this.tikTokUnderwritingService.commitMch({
      mchId,
      kdtId,
      adminId: userId,
    });
    await this.ctx.json(0, 'ok', result);
  }

  /**
   * 抖音修改商户号
   */
  @API('POST', 'update-merchant.json')
  public async updateMerchant() {
    const { kdtId, userId } = this.ctx;
    const { mchId = 1 } = this.ctx.request.body;
    const result = await this.tikTokUnderwritingService.modifyMch({
      mchId,
      kdtId,
      adminId: userId,
    });
    await this.ctx.json(0, 'ok', result);
  }
}

export = UnderwritingController;
