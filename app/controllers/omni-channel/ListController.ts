import { Router, Index, API, Inject } from '@youzan/assets-route-plugin';
import { ShopAbilityUtil } from '@youzan/plugin-shop-ability';
import { checkRetailSingleStore, checkUnifiedShop } from '@youzan/utils-shop';
import { PureObject } from '@youzan/wsc-pc-base/definitions/core';

import BaseController from './BaseController';
import WaimaiPluginWriteService from '../../services/api/channels/omni-channel/WaimaiPluginWriteService';
import ShopCenterService from '../../services/api/channels/omni-channel/ShopCenterService';
import ThirdPartyChannelShopReadService from '../../services/api/channels/omni-channel/ThirdPartyChannelShopReadService';
import ThirdPartyChannelShopWriteService from '../../services/api/channels/omni-channel/ThirdPartyChannelShopWriteService';
import ThirdChannelBBSReadService from '../../services/api/channels/omni-channel/ThirdChannelBBSReadService';
import DistributionChannelReadService from '../../services/api/channels/omni-channel/DistributionChannelReadService';
import HQStoreSearchService from '../../services/api/retail/shop/HQStoreSearchService';
import ChannelShopService from '../../services/api/channels/omni-channel/ChannelShopService';
import ChannelAuthBindService from '../../services/api/channels/omni-channel/ChannelAuthBindService';
import NorthStarCouponService from '../../services/api/retail/open/NorthStarCouponService';
import StaffServiceV2 from '../../services/api/sam/StaffServiceV2';
import WeappAccountService from '../../services/api/channels/business/WeappAccountService';
import SalesOrgCategoryService from '../../services/api/shopcenter/outer/org/SalesOrgCategoryService';
import AbilityReadService from '../../services/api/shopcenter/shopprod/api/AbilityReadService';
import ShopExportService from '../../services/api/shopcenter/shopfront/ShopExportService';
import ShopConfigWriteService from '../../services/api/shopcenter/shopconfig/ShopConfigWriteService';

import RoleService from '../../services/api/rig/RoleService';
import SalesChannelMgrService from '../../services/api/shopcenter/shopfront/SalesChannelMgrService';
import ShopAbilityService from '../../services/api/channels/omni-channel/ShopAbilityService';
import ChannelStockAllocationApi from '../../services/api/retail/stock/ChannelStockAllocationApi';
import SaleChannelBetaTestService from '../../services/api/retail/trademanager/SaleChannelBetaTestService';
import ChannelCoreAccountService from '../../services/api/channels/channel/core/ChannelCoreAccountService';
import ShopThirdChannelService from '../../services/api/channels/omni-channel/ShopThirdChannelService';
import ThirdShopService from '../../services/api/channels/omni-channel/ThirdShopService';
import {
  CHANNEL_ID,
  CHANNEL_TYPE,
  ChannelStatus,
  ChannelVersionMap,
  GetMPVersionKeys,
} from '../../constants/omni-channel';
import CouponService from '../../services/api/channels/omni-channel/CouponService';
import ShopConfigReadService from '../../services/api/shopcenter/shopconfig/ShopConfigReadService';
import MpVersionService from '../../services/api/channels/apps/MpVersionService';

@Router('omni-channel')
class BasicInfoController extends BaseController {
  @Inject()
  public readonly waimaiPluginWriteService!: WaimaiPluginWriteService;

  @Inject()
  public readonly shopCenterService!: ShopCenterService;

  @Inject()
  public readonly thirdPartyChannelShopReadService!: ThirdPartyChannelShopReadService;

  @Inject()
  public readonly ThirdChannelBBSReadService!: ThirdChannelBBSReadService;

  @Inject()
  public readonly thirdPartyChannelShopWriteService!: ThirdPartyChannelShopWriteService;

  @Inject()
  public readonly salesChannelMgrService!: SalesChannelMgrService;

  @Inject()
  public readonly distributionChannelReadService!: DistributionChannelReadService;

  @Inject()
  public readonly hQStoreSearchService!: HQStoreSearchService;

  @Inject()
  public readonly channelAuthBindService!: ChannelAuthBindService;

  @Inject()
  public readonly northStarCouponService!: NorthStarCouponService;

  @Inject()
  public readonly staffServiceV2!: StaffServiceV2;

  @Inject()
  public readonly roleService!: RoleService;

  @Inject()
  public readonly shopAbilityService!: ShopAbilityService;

  @Inject()
  public readonly channelShopService!: ChannelShopService;

  @Inject()
  public readonly channelStockAllocationApi!: ChannelStockAllocationApi;

  @Inject()
  public readonly saleChannelBetaTestService!: SaleChannelBetaTestService;

  @Inject()
  public readonly weappAccountService!: WeappAccountService;

  @Inject()
  public readonly salesOrgCategoryService!: SalesOrgCategoryService;

  @Inject()
  public readonly channelCoreAccountService!: ChannelCoreAccountService;

  @Inject()
  public readonly shopThirdChannelService!: ShopThirdChannelService;

  @Inject()
  public readonly thirdShopService!: ThirdShopService;

  @Inject()
  public readonly abilityReadService!: AbilityReadService;

  @Inject()
  public readonly shopExportService!: ShopExportService;

  @Inject() couponService!: CouponService;

  @Inject()
  public readonly shopConfigWriteService!: ShopConfigWriteService;

  @Inject()
  public readonly shopConfigReadService!: ShopConfigReadService;

  @Inject()
  public readonly mpVersionService!: MpVersionService;

  public async init() {
    this.channelName = 'omni-channel';
    super.init();
  }

  /**
   * 店铺是否在可以预约升级新商品模型
   */
  private async checkCanUpgradeGoodsModel() {
    const supportedKdtIds =
      this.ctx.apolloClient.getConfig({
        appId: 'retail-node-goods',
        namespace: 'retail-node-goods.whitelist',
        key: 'show_update_alert',
      }) || '';

    return supportedKdtIds.includes(this.ctx.kdtId);
  }

  // 判断是否是新流程北极星（休闲娱乐类目）
  async getIsNewPolarStar() {
    const rootKdtId = this.getRootKdtId();
    const relaxedChannelShopList = await this.couponService
      .isMtRelaxedChanelShop({
        rootKdtId,
        adminId: this.ctx.userId,
        retailSource: 'wsc-pc-channel',
        platform: 1,
      })
      .catch(err => {
        this.ctx.logger.error('判断是否是新流程北极星（休闲娱乐类目）失败', err);
        return {};
      });

    return Boolean(relaxedChannelShopList[rootKdtId]);
  }

  protected async isOpenFusion() {
    // 0 - 老版本，1 - 新版本
    try {
      const {
        configs: { tiktok_miniapp_mix_version_switch: isOpenFusion = 0 },
      } = await this.shopConfigReadService.queryShopConfigs(this.ctx.kdtId, [
        'tiktok_miniapp_mix_version_switch',
      ]);

      return Number(isOpenFusion) === 1;
    } catch (error) {
      console.log(error, 'error');
    }

    return false;
  }

  @Index('')
  public async getBaseInfoHtml() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const shopMetaInfo = ctx.getState('shopMetaInfo');
    const isRetailSingleStore = checkRetailSingleStore(shopMetaInfo);

    const [
      offlineManageAbility,
      isLiteOnlineStoreManager,
      isSupportMultiChannel,
      weappConfg,
      orgCategory,
      shopVersion,
      retailTableBookingAbility,
      isNewPolarStar,
      tiktokBindMsg,
      isOpenFusion = false,
    ] = await Promise.all([
      new ShopAbilityUtil(ctx).checkAbilityValid({
        keys: ['offline_store_manage_ability'], // 门店管理能力
      }),
      this.checkLiteOnlineStoreManager(),
      isRetailSingleStore
        ? Promise.resolve(false)
        : this.salesChannelMgrService.isHitWhiteList(kdtId),
      this.weappAccountService.getWeappCodeLcByKdtId(kdtId),
      checkUnifiedShop(shopMetaInfo)
        ? this.salesOrgCategoryService.queryOne(kdtId)
        : Promise.resolve({}),
      this.abilityReadService.queryShopProdVersions(kdtId),
      new ShopAbilityUtil(ctx).checkAbilityValid({
        keys: ['retail_table_booking_ability'],
      }),
      this.getIsNewPolarStar(),
      this.channelCoreAccountService.queryMpBindInfoByKdtId({
        businessType: 7,
        accountType: 21,
        externalId: shopMetaInfo.rootKdtId || kdtId,
      }),
      this.isOpenFusion(),
    ]);

    const pageData: PureObject = {
      shopVersion,
    };

    if (isRetailSingleStore === false) {
      const { valid } = await this.shopAbilityService.queryHqAbility(
        ctx.kdtId,
        'lite_online_store_manage_ability',
      );
      ctx.setGlobal('liteShopAbility', valid);
    }
    // 北极星白名单
    this.isInWhiteListByApollo('channel_meituan_dp_bjx', {
      useRootKdtId: true,
      globalKey: 'isSupportBindMeiTuanBjx',
    });

    ctx.setGlobal('isOpenFusion', isOpenFusion);
    ctx.setGlobal('pageData', pageData);
    ctx.setGlobal('offlineManageAbility', offlineManageAbility);
    ctx.setGlobal('retailTableBookingAbility', retailTableBookingAbility);
    ctx.setGlobal('isLiteOnlineStoreManager', isLiteOnlineStoreManager);
    ctx.setGlobal('isSupportMultiChannel', isSupportMultiChannel);
    ctx.setGlobal('isSupportWeapp', Boolean(weappConfg?.releasedVersion));
    ctx.setGlobal('orgCategory', orgCategory);
    ctx.setGlobal('isBindTiktok', !!tiktokBindMsg?.mpId); // 是否授权抖音小程序

    const { nickName } = ctx.getLocalSession('userInfo') || {};
    ctx.setGlobal('nickName', nickName);

    ctx.setGlobal('isNewPolarStar', isNewPolarStar);

    try {
      const canUpgradeGoodsModel = await this.checkCanUpgradeGoodsModel();
      ctx.setGlobal('canUpgradeGoodsModel', canUpgradeGoodsModel);
    } catch (err) {
      this.ctx.logger.error('获取预约升级新商品模型白名单失败', err);
    }

    return ctx.render('omni-channel/index.html');
  }

  @Index('plugin')
  public async getPluginIndexHTML() {
    this.ctx.setGlobal('isOmniChannelPlugin', true);
    return this.getBaseInfoHtml();
  }

  @Index('bind-shop')
  public async getBindShopHTML() {
    const { ctx } = this;
    return ctx.render('omni-channel/bind-shop.html');
  }

  @Index('bind-success')
  public async getBindSuccess() {
    const { ctx } = this;
    // 模板不要删, 在使用
    // app/controllers/mini-app/meituan/AuthController.ts
    return ctx.render('omni-channel/bind-success.html');
  }

  // 随心团项目，新版授权列表
  @Index(['auth-list', 'xhs-independent-auth-list'])
  public async getAuthListIndexHTML() {
    // 小红书独立版本判断
    const hasXhsIndependentAbility = await new ShopAbilityUtil(this.ctx).checkAbilityValid({
      keys: ['xhs_independent_ability'],
      kdtId: this.ctx.kdtId,
    });
    if (hasXhsIndependentAbility && this.ctx.url.indexOf('xhs-independent') < 0) {
      const currentPath = this.ctx.url.split('/').pop() || '';
      const redirectPath = `/v4/channel/omni/omni-channel${
        currentPath ? `/xhs-independent-${currentPath}` : ''
      }`;
      this.ctx.redirect(redirectPath);

      return;
    }

    return this.getBaseInfoHtml();
  }

  /**
   * 获取开关状态接口
   */
  @API('GET', 'getProcessSettingConfig.json')
  public async getProcessSettingConfig() {
    const { kdtId } = this.ctx;
    const data = await this.shopCenterService.queryShopAbilityInfo(kdtId);
    return this.ctx.json(0, 'success', data);
  }

  /**
   * 设置开关状态接口
   */
  @API('POST', 'setProcessSettingConfig.json')
  public async setProcessSettingConfig() {
    const { kdtId, request } = this.ctx;
    const { body } = request;

    let data;
    if (body.status === 1) {
      data = await this.waimaiPluginWriteService.turnOn(kdtId, this.baseInfo);
    } else {
      data = await this.waimaiPluginWriteService.turnOff(kdtId, this.baseInfo);
    }

    return this.ctx.json(0, 'success', data);
  }

  /**
   * 查询店铺已渠道的绑定列表
   */
  @API('GET', 'getList.json')
  public async getList() {
    const { kdtId, request, userId } = this.ctx;

    const param = JSON.parse(request.query.param);

    const { channelId, searchKdtId, pageSize, pageNum } = param;
    const query = {
      adminId: userId,
      kdtId,
      pageSize,
      channelIds: Number(channelId) === 0 ? [] : [channelId],
      searchKdtIds: Number(searchKdtId) === 0 ? [] : [searchKdtId],
      pageNum,
    };
    const data = await this.thirdPartyChannelShopReadService.queryChannelShops(query);
    return this.ctx.json(0, 'success', data);
  }

  /**
   * 查询店铺已绑定的渠道列表
   */
  @API('GET', 'getWaiMaiTongList.json')
  public async getWaiMaiTongList() {
    const { kdtId, request, userId } = this.ctx;

    const {
      channelId,
      searchKdtId,
      pageSize,
      pageNo,
      channelType,
      relatedChannel,
      status,
    } = request.query;

    const query = {
      adminId: userId,
      kdtId,
      pageSize,
      channelIds: Number(channelId) === 0 ? [] : [channelId],
      searchKdtIds: Number(searchKdtId) === 0 ? [] : [searchKdtId],
      pageNum: pageNo,
      channelType,
      status,
      relatedChannel,
    };

    const data = (await this.thirdPartyChannelShopReadService.queryChannelShops(query)) || {};

    return this.ctx.json(0, 'success', {
      items: (data.data || []).map((item: any) => ({
        ...item,
        id: `${item.channelId}-${item.kdtId}`,
      })),
      paginator: {
        page: data.pageNum || pageNo,
        totalCount: data.total || 0,
        pageSize: data.pageSize || pageSize,
      },
    });
  }

  /**
   * 查询店铺已绑定的卡券渠道列表
   * @deprecated - 之后下线, 请使用 getBoundThirdChannelShops
   */
  @API('GET', 'getKaQuanList.json')
  public async getKaQuanList() {
    const { kdtId, request, userId } = this.ctx;

    const { channelId, searchKdtId, pageSize, pageNo, channelType } = request.query;

    const query = {
      adminId: userId,
      kdtId,
      pageSize,
      channelIds: Number(channelId) === 0 ? [] : [channelId],
      searchKdtIds: Number(searchKdtId) === 0 ? [] : [searchKdtId],
      pageNo,
      channelType,
    };

    const data = (await this.channelShopService.queryChannelShops(query)) || {};

    return this.ctx.json(0, 'success', {
      items: (data.data || []).map((item: any) => ({
        ...item,
        id: `${item.channelId}-${item.kdtId}`,
      })),
      paginator: {
        page: data.pageNum || pageNo,
        totalCount: data.total || 0,
        pageSize: data.pageSize || pageSize,
      },
    });
  }

  /**
   * 查询所有店铺三方渠道绑定列表
   */
  @API('GET', 'getBoundThirdChannelShops.json')
  public async getBoundThirdChannelShops() {
    const { kdtId, userId } = this.ctx;

    const { channelId, searchKdtId, pageSize, pageNo, channelType } = this.ctx.validator(
      this.ctx.joi.object({
        channelId: this.ctx.joi.number(),
        searchKdtId: this.ctx.joi.number(),
        pageSize: this.ctx.joi.number(),
        pageNo: this.ctx.joi.number(),
        channelType: this.ctx.joi.number(),
      }),
    );

    let channelIds;
    if (channelType) {
      /** 如果传了 channelType，需要过滤下当前 channelType 可用的 channelId */
      const limitChannelList = await this.getLimitThirdChannelListByChannelType({
        kdtId,
        userId,
        channelType,
      });

      const targetChannel = limitChannelList.find(item => item.channelId === channelId);
      channelIds = targetChannel
        ? [targetChannel.channelId]
        : limitChannelList.map(item => item.channelId);
    } else {
      channelIds = channelId ? [channelId] : undefined;
    }

    const query = {
      adminId: userId,
      kdtId,
      pageSize,
      channelIds,
      searchKdtIds: searchKdtId ? [searchKdtId] : undefined,
      pageNo,
      channelType,
    };

    const data = (await this.channelShopService.queryChannelShops(query)) || {};

    return this.ctx.json(0, 'success', {
      items: (data.data || []).map((item: any) => ({
        ...item,
        id: `${item.channelId}-${item.kdtId}`,
      })),
      paginator: {
        page: data.pageNum || pageNo,
        totalCount: data.total || 0,
        pageSize: data.pageSize || pageSize,
      },
    });
  }

  /**
   * 查询渠道的列表
   */
  @API('GET', 'getChannelList.json')
  public async getChannelList() {
    const { kdtId, userId } = this.ctx;
    const rootKdtId = this.ctx.getState('shopInfo').rootKdtId || kdtId;
    const {
      channelType,
      needAuthInfo,
      cloudStore,
      /** 是否包含内测渠道 */
      containInternalTest = false,
    } = this.ctx.validator(
      this.ctx.joi.object({
        containInternalTest: this.ctx.joi.boolean(),
      }),
    );

    const [channel, internalTestChannel] = await Promise.all([
      this.thirdPartyChannelShopReadService.querySupportThirdChannelList({
        kdtId,
        adminId: userId,
        channelType,
        needAuthInfo,
        cloudStore,
      }),
      containInternalTest
        ? this.saleChannelBetaTestService
            .queryBetaTestList(
              /** 报名按总部维度统计, 需要用 rootKdtId */
              rootKdtId,
            )
            .catch(err => {
              /** 获取内部外卖渠道(假的渠道), 不打断获取实际渠道 */
              this.ctx.logger.error('获取内测外卖渠道错误', err);
              return [];
            })
        : [],
    ]);

    return this.ctx.success([...channel, ...internalTestChannel]);
  }

  /**
   * 查询卡券渠道的列表
   * @deprecated - 之后下线, 请使用 getSupportThirdChannelList
   */
  @API('GET', 'getKaQuanChannelList.json')
  public async getKaQuanChannelList() {
    const { kdtId, request, userId } = this.ctx;
    const { needAuthInfo, cloudStore } = request.query;
    const data = await this.channelShopService.querySupportThirdChannelList({
      kdtId,
      adminId: userId,
      needAuthInfo,
      cloudStore,
    });
    return this.ctx.success(data);
  }

  /**
   * 根据 channelType, 获取 channelType 可用 channelId 列表
   */
  private async getLimitThirdChannelListByChannelType(params: {
    kdtId: number;
    userId: number;
    channelType?: CHANNEL_TYPE;
    needAuthInfo?: boolean;
    cloudStore?: boolean;
  }): Promise<Array<{ channelId: CHANNEL_ID }>> {
    const data = await this.channelShopService.querySupportThirdChannelList(params);

    let filterFn: ((item: { channelId: CHANNEL_ID }) => boolean) | undefined;
    switch (params.channelType) {
      case CHANNEL_TYPE.TABLE_BOOKING:
        filterFn = item => item.channelId === CHANNEL_ID.MEITUAN_TABLE_BOOKING;
        break;
      case CHANNEL_TYPE.KAQUAN:
        filterFn = item =>
          [CHANNEL_ID.MEITUAN, CHANNEL_ID.TIKTOK, CHANNEL_ID.XIAOHONGSHU].includes(item.channelId);
        break;
      default:
        break;
    }

    return filterFn ? data.filter(filterFn) : data;
  }

  /**
   * 查询三方渠道列表
   */
  @API('GET', 'getSupportThirdChannelList.json')
  public async getSupportThirdChannelList() {
    const { kdtId, userId } = this.ctx;

    const { needAuthInfo, cloudStore, channelType } = this.ctx.validator(
      this.ctx.joi.object({
        needAuthInfo: this.ctx.joi.boolean(),
        cloudStore: this.ctx.joi.boolean(),
        channelType: this.ctx.joi.number(),
      }),
    );

    const data = await this.getLimitThirdChannelListByChannelType({
      kdtId,
      userId,
      needAuthInfo,
      cloudStore,
      channelType,
    });

    return this.ctx.success(data);
  }

  /**
   * 查询新渠道概况
   */
  @API('GET', 'queryThirdChannelOverviewInfo.json')
  public async queryThirdChannelOverviewInfo() {
    const { kdtId, request, userId } = this.ctx;
    const { channel } = request.query;
    const data = await this.thirdPartyChannelShopReadService.queryThirdChannelOverviewInfo({
      kdtId,
      adminId: userId,
      channel: channel ? Number(channel) : undefined,
    });
    // 查询版本
    await this.getMPVersion(data);
    return this.ctx.success(this.filterChannelData(data));
  }

  filterChannelData(data: any[]) {
    return (data || []).filter((item: { status: number; channel: CHANNEL_ID }) => {
      if (item.channel === CHANNEL_ID.TIKTOK_SHELF) {
        return [ChannelStatus.unBind, ChannelStatus.alBind].includes(+item.status);
      }
      return true;
    });
  }

  async getMPVersion(data: Record<string, any>) {
    const { kdtId } = this.ctx;
    const getVersionPromise: any[] = [];
    data.forEach((item: { status: number; channel: CHANNEL_ID }) => {
      // 已开通
      if (item.status === ChannelStatus.alBind && GetMPVersionKeys.includes(item.channel)) {
        // @ts-ignore
        const { channelName, channelType } = ChannelVersionMap[item.channel];
        getVersionPromise.push(
          this.getVersionInfo({
            kdtId,
            channelName,
            channelType,
          })
            .then(res => {
              return {
                channel: item.channel,
                res,
              };
            })
            .catch(() => ({})),
        );
      }
    });
    const versionResults = await Promise.all(getVersionPromise);

    data.forEach(
      (item: {
        status: number;
        channel: CHANNEL_ID;
        version: Record<string, any>;
        disableSubscribe: boolean;
      }) => {
        // 已开通
        if (item.status === ChannelStatus.alBind && GetMPVersionKeys.includes(item.channel)) {
          const mpVersionInfo = versionResults.find(i => i.channel === item.channel)?.res;
          if (mpVersionInfo) {
            item.version = mpVersionInfo.releaseVersion;
          }
        }
      },
    );
  }

  /**
   * 根据渠道查询bbs
   */
  @API('GET', 'queryBBSList.json')
  public async queryBBSList() {
    const { request } = this.ctx;
    const { channel } = request.query;
    const data = await this.ThirdChannelBBSReadService.queryBBSList({
      channel: channel ? Number(channel) : undefined,
    });
    return this.ctx.success(data);
  }

  /**
   * 报名参加内测渠道
   */
  @API('POST', 'signUpInternalTestChannel.json')
  public async signUpInternalTest() {
    const { kdtId, request, userId } = this.ctx;
    const rootKdtId = this.ctx.getState('shopInfo').rootKdtId || kdtId;
    const { channelId, channelName } = request.body;
    const result = await this.saleChannelBetaTestService.signUp({
      /** 报名按总部维度统计, 需要用 rootKdtId */
      kdtId: rootKdtId,
      adminId: userId,
      channelId,
      channelName,
    });
    return this.ctx.success(result);
  }

  /**
   * 查询店铺的列表
   */
  @API('GET', 'getStoreList.json')
  public async getStoreList() {
    const appName = 'wsc-pc-channel';
    const { kdtId, request, userId } = this.ctx;
    const {
      pageNo,
      pageSize,
      channelIds,
      storeNameOrManagerPhoneOrName,
      storeName,
      /** 绑定的渠道id(或关系)，不包含过期的渠道 */
      channelIdsExcludeInvalid,
      /** 未绑定的渠道 */
      excludeChannelIds,
      isOfflineOpen,
      isOnlineOpen,
      supplyMode,
      /** 渠道id（appendOutShopBindInfo=true的时候必填） */
      appendOutShopBindInfoChannelId,
      orgCategorys: orgCategorysQuery = '[]',
      sortType,
      sortName,
      /** 分店店铺能力(包括过期的能力) */
      abilityCodesContainInvalid,
    } = request.query;

    let param: any = {
      kdtId,
      adminId: userId,
      storeNameOrManagerPhoneOrName,
      allContainedChannelIdsarray: channelIds,
      pageNo,
      pageSize,
      retailSource: appName,
      supplyMode,
      sortName,
    };
    // sortType 为 1 升序，2 降序 不考虑 0
    // 如果 sortType 为空，则不传 sortType, 防止后端报错
    if (sortType) {
      param.sortType = Number(sortType);
    }
    this.ctx.logger.info(`getStoreList param start：${JSON.stringify(param)}`);
    if (isOfflineOpen !== undefined) {
      param = {
        ...param,
        isOfflineOpen,
      };
    }
    if (isOnlineOpen !== undefined) {
      param = {
        ...param,
        isOnlineOpen,
      };
    }
    {
      // 不支持 ctx.queries 只能 序列化之后再 parse 一下
      let orgCategorys;
      try {
        orgCategorys = JSON.parse(orgCategorysQuery);
      } catch (error) {
        orgCategorys = [];
      }
      if (orgCategorys.length > 0) {
        param = {
          ...param,
          orgCategorys,
        };
      }
    }
    if (appendOutShopBindInfoChannelId) {
      param = {
        ...param,
        appendOutShopBindInfoChannelId,
      };
    }
    if (storeName) {
      param.storeName = storeName;
    }
    if (channelIdsExcludeInvalid) {
      param.channelIdsExcludeInvalid = JSON.parse(channelIdsExcludeInvalid);
    }
    if (excludeChannelIds) {
      param.excludeChannelIds = JSON.parse(excludeChannelIds);
    }
    if (abilityCodesContainInvalid) {
      param.abilityCodesContainInvalid = JSON.parse(abilityCodesContainInvalid);
    }
    this.ctx.logger.info(`getStoreList param：${JSON.stringify(param)}`);
    const data = await this.hQStoreSearchService.searchAfterWithDataPermission(param);

    return this.ctx.success(data);
  }

  /**
   * 获取授权链接
   */
  @API('GET', 'getAuthPageUrl.json')
  public async getAuthPageUrl() {
    const { request, userId } = this.ctx;
    const { kdtId, authType = 1, accountType, businessType = 1, solutionKey } = request.query;
    const payload = { kdtId, accountType };

    const params: Record<string, any> = {
      kdtId: kdtId || this.ctx.kdtId,
      userId,
      authType,
      accountType,
      businessType,
      payload: encodeURI(JSON.stringify(payload)),
    };

    if (solutionKey) {
      params.solutionKey = solutionKey;
    }

    const data = await this.channelAuthBindService.getAuthPageUrl(params);

    return this.ctx.success({
      ...data,
      url: data?.url,
    });
  }

  /**
   * 绑定店铺
   */
  @API('GET', 'bindStore.json')
  public async bindAccountToShop() {
    const { request, accountId } = this.ctx;
    const { businessType = 1, code, state } = request.query;
    const { kdtId, accountType } = JSON.parse(state);
    const params = {
      accountId,
      kdtId: kdtId || this.ctx.kdtId,
      createdFrom: 1,
      accountType,
      businessType,
      sessionId: this.ctx.sid,
      appAccountAuthCode: code,
    };
    try {
      this.ctx.setGlobal('errorInfo', '');
      await this.channelAuthBindService.bindAccountToShop(params);
    } catch (error) {
      this.ctx.setGlobal('errorInfo', error.msg);
    }

    return this.ctx.render('omni-channel/result.html');
  }

  /**
   * 绑定店铺
   */
  @API('GET', 'unBindStore.json')
  public async unbindAccountAndUnAuth() {
    const { request, userId, accountId } = this.ctx;
    const { businessType = 1, kdtId, accountType, authType = 1 } = request.query;
    const params = {
      kdtId: kdtId || this.ctx.kdtId,
      accountId,
      accountType,
      businessType,
      authType,
      userId,
    };
    const data = await this.channelAuthBindService.unbindAccountAndUnAuth(params);

    return this.ctx.success(data);
  }

  /**
   * 查询店铺的列表
   */
  @API('GET', 'queryChannelShopIsBind.json')
  public async queryChannelShopIsBind() {
    const { request } = this.ctx;
    const { kdtId, channelId } = request.query;

    const data = await this.distributionChannelReadService.queryChannelShopIsBind(channelId, kdtId);

    return this.ctx.success(data);
  }

  /**
   * 查询店铺的列表
   */
  @API('GET', 'queryChannelShopIsBindV2.json')
  public async queryChannelShopIsBindV2() {
    const { request } = this.ctx;
    const { kdtId, channelId, channelType } = request.query;

    const data = await this.channelShopService.queryChannelShopIsBind({
      channelId,
      kdtId,
      channelType,
    });

    return this.ctx.success(data);
  }

  /**
   *  查询是否绑定授权
   * @returns
   */
  @API('GET', 'getShopChannelAccount.json')
  public async getShopChannelAccount() {
    const { request } = this.ctx;
    const { kdtId, channelId, channelAccountType } = request.query;

    const data = await this.shopThirdChannelService.getShopChannelAccount({
      kdtId,
      channelAccountType,
      channelId,
    });

    return this.ctx.success(data);
  }

  /**
   * 获取北极星绑定店铺列表
   */
  @API('GET', 'queryNorthStarShopList.json')
  public async queryNorthStarShopList() {
    const { request } = this.ctx;
    const { kdtId, authCode } = request.query;
    const data = await this.northStarCouponService.queryNorthStarShopList({
      kdtId: kdtId || this.ctx.kdtId,
      authCode,
    });

    return this.ctx.success(data);
  }

  /**
   * 获取北极星绑定店铺列表
   */
  @API('POST', 'bindNorthStarChannelShop.json')
  public async bindNorthStarChannelShop() {
    const { shop, bindKdtId } = this.ctx.getPostData();
    const query = {
      ...shop,
      adminId: this.ctx.userId,
      kdtId: bindKdtId,
    };
    const data = await this.northStarCouponService.bindNorthStarChannelShop(query);

    return this.ctx.success(data);
  }

  /**
   * 批量设置
   */
  @API('POST', 'setStores.json')
  public async setStores() {
    const { kdtId, request } = this.ctx;
    const { body } = request;

    const data = await this.thirdPartyChannelShopWriteService.batchUpdateChannelShopConfigs({
      loginKdtId: kdtId,
      list: JSON.parse(body.param),
      operator: this.baseInfo,
    });

    return this.ctx.json(0, 'success', data);
  }

  /**
   * 获取店铺属性
   */
  @API('GET', 'getStoreProperty.json')
  public async getStoreProperty() {
    const { request } = this.ctx;
    const { channelId, kdtId } = request.query;
    const data = await this.distributionChannelReadService.queryThirdChannelShopBindInfo(
      channelId,
      kdtId,
    );

    return this.ctx.json(0, 'success', data);
  }

  /**
   * 单个设置
   */
  @API('POST', 'updateChannelShop.json')
  public async updateChannelShopConfigs() {
    const { kdtId: loginKdtId, request } = this.ctx;
    const { syncBusinessHours, operateStatus, kdtId, channelId, extConfigs } = request.body;
    const response = await this.thirdPartyChannelShopWriteService.updateChannelShopConfigs({
      syncBusinessHours,
      operateStatus,
      kdtId,
      loginKdtId,
      channelId,
      operator: this.baseInfo,
      extConfigs,
    });

    return this.ctx.json(0, 'success', response);
  }

  @API('POST', 'importUpdateChannelShopConfigs.json')
  public async importUpdateChannelShopConfigs() {
    const { channelId, fileName, fileUrl } = this.ctx.getPostData();
    const rootKdtId = this.ctx.getState('shopInfo').rootKdtId || this.ctx.kdtId;
    const res = await this.thirdPartyChannelShopWriteService.importUpdateTiktokChannelDeliveryConfigs(
      {
        channelId,
        fileName,
        fileUrl,
        hqKdtId: rootKdtId,
        operator: this.baseInfo,
      },
    );

    return this.ctx.json(0, 'success', res);
  }

  @API('POST', 'exportTiktokChannelDeliveryConfigs.json')
  public async exportTiktokChannelDeliveryConfigs() {
    const { channelId } = this.ctx.getPostData();
    const rootKdtId = this.ctx.getState('shopInfo').rootKdtId || this.ctx.kdtId;

    const res = await this.thirdPartyChannelShopWriteService.exportTiktokChannelDeliveryConfigs({
      channelId,
      hqKdtId: rootKdtId,
      operator: this.baseInfo,
    });

    return this.ctx.json(0, 'success', res);
  }

  @API('POST', 'handBindChannelShop.json')
  public async handBindChannelShop() {
    const { request } = this.ctx;
    const { channel, targetKdtId } = request.body;
    const response = await this.thirdPartyChannelShopWriteService.handBindChannelShop({
      targetKdtId,
      channel,
    });

    return this.ctx.json(0, 'success', response);
  }

  /**
   * 查询能力
   */
  @API('GET', 'queryHqAbility.json')
  public async queryHqAbility() {
    const { kdtId: loginKdtId, request } = this.ctx;
    const { abilityCode } = request.query;
    const response = await this.shopAbilityService.queryHqAbility(loginKdtId, abilityCode);

    return this.ctx.json(0, 'success', response);
  }

  @API('POST', 'updateChannelStockAllocation.json')
  public async updateChannelStockAllocation() {
    const { request } = this.ctx;
    const { channelId, storeId, allocationPercent } = request.body;
    const { entryApp, id } = this.baseInfo;
    const response = await this.channelStockAllocationApi.updateChannelStockAllocation({
      adminId: id,
      kdtId: this.ctx.kdtId,
      storeId,
      source: entryApp,
      retailSource: entryApp,
      items: [
        {
          allocationPercent: Number(allocationPercent),
          channelId,
        },
      ],
    });
    return this.ctx.json(0, 'success', response);
  }

  @API('GET', 'queryChannelStockAllocation.json')
  public async queryChannelStockAllocation() {
    const { request } = this.ctx;
    const { storeId } = request.query;
    const { entryApp, id } = this.baseInfo;
    const { kdtId } = this.ctx;
    const response = await this.channelStockAllocationApi.queryOne({
      kdtId,
      adminId: id,
      storeId: Number(storeId),
      retailSource: entryApp,
      source: entryApp,
    });
    return this.ctx.json(0, 'success', response);
  }

  @API('POST', 'modifyChannelStockAllocation.json')
  public async modifyChannelStockAllocation() {
    const { request } = this.ctx;
    const { channelId, storeId, planAllocationPercent, stockAllocationPercent } = request.body;
    const { entryApp, id } = this.baseInfo;
    const response = await this.channelStockAllocationApi.modifyChannelStockAllocation({
      adminId: id,
      kdtId: this.ctx.kdtId,
      storeId,
      source: entryApp,
      retailSource: entryApp,
      items: [
        {
          planAllocationPercent: Number(planAllocationPercent),
          stockAllocationPercent: Number(stockAllocationPercent),
          channelId,
        },
      ],
    });
    return this.ctx.json(0, 'success', response);
  }

  @API('GET', 'queryOneAllocationRelation.json')
  public async queryOneAllocationRelation() {
    const { request } = this.ctx;
    const { storeId } = request.query;
    const { entryApp, id } = this.baseInfo;
    const { kdtId } = this.ctx;
    const response = await this.channelStockAllocationApi.queryOneAllocationRelation({
      kdtId,
      adminId: id,
      storeId: Number(storeId),
      retailSource: entryApp,
      source: entryApp,
    });
    return this.ctx.json(0, 'success', response);
  }

  @API('POST', 'authorizeTiktokShop.json')
  public async authorizeTiktokShop() {
    const { request, kdtId } = this.ctx;
    const { id: adminId, entryApp } = this.baseInfo;
    const { authKdtIds, operatorId, channelId, outName, poiId } = request.body;
    const response = await this.channelShopService.authorizeTiktokShop({
      kdtId,
      authKdtIds,
      operatorId,
      channelId,
      outName,
      poiId,
      adminId,
      retailSource: entryApp,
      source: entryApp,
    });
    return this.ctx.json(0, 'success', response);
  }

  @API('POST', 'cancelTiktokShopAuth.json')
  public async cancelTiktokShopAuth() {
    const { request } = this.ctx;
    const { id: adminId, entryApp } = this.baseInfo;
    const { kdtId, operatorId, channelId } = request.body;
    const response = await this.channelShopService.cancelTiktokShopAuth({
      kdtId,
      operatorId,
      channelId,
      adminId,
      retailSource: entryApp,
      source: entryApp,
    });
    return this.ctx.json(0, 'success', response);
  }

  @API('GET', 'getShopNameByPooid.json')
  public async getShopNameByPooid() {
    const { request, kdtId } = this.ctx;
    const { poiId } = request.query;
    const response = await this.channelShopService.queryThirdShopInfo({
      kdtId,
      poiId,
    });
    return this.ctx.json(0, 'success', response);
  }

  @API('GET', 'queryChannelWeappIsBind.json')
  public async queryChannelWeappIsBind() {
    const { request, kdtId } = this.ctx;
    const { businessType, accountType } = request.query;

    const result = await this.channelCoreAccountService.queryMpBindInfoByKdtId({
      businessType,
      accountType,
      externalId: kdtId,
    });
    return this.ctx.json(0, 'success', result);
  }

  @API('GET', 'queryShopsByChannel.json')
  public async queryShopsByChannel() {
    const { request, kdtId } = this.ctx;
    const { pageSize, pageNo, channelId, searchKdtIds = '', shopName = '' } = request.query;

    let params: any = {
      channelId,
      pageNum: pageNo,
      shopName,
      pageSize,
      kdtId,
    };

    if (searchKdtIds) {
      params = {
        ...params,
        searchKdtIds: JSON.parse(searchKdtIds),
      };
    }

    const result = await this.shopThirdChannelService.queryShopsByChannel({
      ...params,
    });

    return this.ctx.json(0, 'success', result);
  }

  @API('GET', 'queryYzShops.json')
  public async queryYzShops() {
    const { request, kdtId } = this.ctx;
    const { pageSize, pageNo, channelId, shopName = '', bindStatus } = request.query;
    const { entryApp, id } = this.baseInfo;

    const params: any = {
      channelId,
      storeNameOrManagerPhoneOrName: shopName,
      adminId: id,
      retailSource: entryApp,
      pageNo,
      pageSize,
      kdtId,
      authStatus: bindStatus ? Number(bindStatus) : undefined,
    };

    const result = await this.channelShopService.queryYzShops({
      ...params,
    });

    return this.ctx.json(0, 'success', result);
  }

  @API('POST', 'batchShopAuth.json')
  public async batchShopAuth() {
    const { request, kdtId } = this.ctx;
    const { channelId, shopAuthRequests = [], type } = request.body;
    const { entryApp, id } = this.baseInfo;

    const params: any = {
      channelId,
      adminId: id,
      retailSource: entryApp,
      shopAuthRequests,
      type,
      kdtId,
    };
    const result = await this.channelShopService.batchShopAuth({
      ...params,
    });

    return this.ctx.json(0, 'success', result);
  }

  @API('GET', 'queryThirdShopList.json')
  public async queryThirdShopList() {
    const { request, kdtId } = this.ctx;
    const { pageSize, pageNo, channelId } = request.query;
    const { entryApp, id } = this.baseInfo;

    const params: any = {
      thirdChannelType: channelId,
      adminId: id,
      retailSource: entryApp,
      pageNo,
      pageSize,
      kdtId,
    };

    const result = await this.thirdShopService.queryThirdShopInfo({
      ...params,
    });

    return this.ctx.json(0, 'success', result);
  }

  @API('GET', 'queryThirdChannelShopList.json')
  public async queryThirdChannelShopList() {
    const { request, kdtId } = this.ctx;
    const { channelId } = request.query;

    const result = await this.thirdPartyChannelShopReadService.queryThirdChannelShopList({
      channelId,
      kdtId,
    });

    return this.ctx.json(0, 'success', result);
  }

  @API('GET', 'bindThirdChannelShop.json')
  public async bindThirdChannelShop() {
    const { request, kdtId } = this.ctx;
    const { channel, outShopId, targetKdtId } = request.query;

    const result = await this.thirdPartyChannelShopWriteService.bindThirdChannelShop({
      channel,
      targetKdtId,
      loginKdtId: kdtId,
      outShopId,
    });
    return this.ctx.json(0, 'success', result);
  }

  @API('GET', 'asyncBindThirdChannelShop.json')
  public async asyncBindThirdChannelShop() {
    const { request, kdtId } = this.ctx;
    const { channel, outShopId, targetKdtId, miniAppBusinessType = 7 } = request.query;

    const result = await this.thirdPartyChannelShopWriteService.asyncBindThirdChannelShop({
      channel,
      targetKdtId,
      loginKdtId: kdtId,
      outShopId,
      miniAppBusinessType: Number(miniAppBusinessType),
    });
    return this.ctx.json(0, 'success', result);
  }

  @API('GET', 'asyncBindThirdChannelShopSaveSure.json')
  public async asyncBindThirdChannelShopSaveSure() {
    const { request } = this.ctx;
    const { channel, outShopId, targetKdtId, key } = request.query;

    const result = await this.thirdPartyChannelShopWriteService.saveSure({
      channel,
      targetKdtId,
      outShopId,
      key,
    });
    return this.ctx.json(0, 'success', result);
  }

  @API('GET', 'queryProdExpiredSubKdt.json')
  public async queryProdExpiredSubKdt() {
    const { request } = this.ctx;
    const { daysBeforeExpired, prodCodes } = request.query;

    const shopInfo = this.ctx.getState('shopInfo');
    const hqKdtId = shopInfo.rootKdtId;

    const result = await this.hQStoreSearchService.queryProdExpiredSubKdt({
      daysBeforeExpired: +daysBeforeExpired,
      hqKdtId,
      prodCodes: JSON.parse(prodCodes),
    });
    return this.ctx.json(0, 'success', result);
  }

  /**
   * 查询店铺已绑定的渠道列表
   */
  @API('POST', 'exportOrgInfo.json')
  public async exportOrgInfo() {
    const { kdtId, request } = this.ctx;
    const { exportType, searchMap, orgCategory } = request.body;

    const params = {
      hqKdtId: kdtId,
      exportType,
      searchMap,
      orgCategory,
      operator: this.baseInfo,
    };

    const result = await this.shopExportService.submit(params);

    return this.ctx.success(result);
  }

  /**
   * 批量设置店铺配置请求
   */
  @API('POST', 'setShopConfigs.json')
  public async setShopConfigs() {
    const { request } = this.ctx;
    const { kdtId, configs } = request.body;

    const params = {
      kdtId,
      configs,
      operator: this.baseInfo,
    };

    const result = await this.shopConfigWriteService.setShopConfigs(params);

    return this.ctx.success(result);
  }

  /**
   * 批量修改店铺配置，支持不同kdtId和不同key同时设置
   */
  @API('POST', 'multiSetShopConfigs.json')
  public async multiSetShopConfigs() {
    const { request } = this.ctx;
    const { shopConfigList } = request.body;

    const params = {
      shopConfigList,
      operator: this.baseInfo,
    };

    const result = await this.shopConfigWriteService.multiSetShopConfigs(params);

    return this.ctx.success(result);
  }
}

export = BasicInfoController;
