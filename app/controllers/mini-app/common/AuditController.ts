import { Router, Inject, API } from '@youzan/assets-route-plugin';
import BaseController from '../BaseController';
import WeappCodeService from '../../../services/api/channels/apps/WeappCodeService';
import UnifiedCertQueryService from '../../../services/api/pay/customer/cert/UnifiedCertQueryService';
import ProdReadService from '../../../services/api/shopcenter/shopprod/prod/ProdReadService';
import differenceInDays from 'date-fns/difference_in_days';

/**
 * 通用提审业务
 */
@Router(':channelName/audit')
class AuthController extends BaseController {
  @Inject()
  private readonly weappCodeService!: WeappCodeService;

  @Inject()
  private readonly unifiedCertQueryService!: UnifiedCertQueryService;

  @Inject()
  private readonly prodReadService!: ProdReadService;

  /**
   * 获取小程序提交审核的所有前置检查项和检查通过项列表
   */
  @API('GET', 'status.json')
  public async getStatus() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { businessType = 1 } = this.ctx.query;
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');

    const params = {
      accountType,
      businessType,
      kdtId,
    };
    const result = await this.weappCodeService.getMiniProgramPreCheckResult(params);
    return ctx.json(0, 'ok', result);
  }

  /**
   * 获取营业执照信息
   */
  @API('GET', 'principal-msg.json')
  public async getPrincipalMsg() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const result = await this.unifiedCertQueryService.queryPrincipalMsg({
      sourceId: kdtId.toString(),
      sourceIdType: 'KDT_ID',
      unifiedCertType: 1,
    });

    if (result && result.merchantOrganizationCertResult) {
      return ctx.json(0, 'ok', {
        // 营业执照号
        creditNo: result.merchantOrganizationCertResult.creditNo,
        // 营业执照图片
        creditPhotoUrl: result.merchantOrganizationCertResult.creditPhotoUrl,
        // 开始日期
        creditValidStartTime: result.merchantOrganizationCertResult.creditValidStartTime,
        // 结束日期
        creditValidEndTime: result.merchantOrganizationCertResult.creditValidEndTime,
        // 企业名称
        organizationName: result.merchantOrganizationCertResult.organizationName,
      });
    }
    return ctx.json(0, 'ok', {});
  }

  @API('POST', 'submit.json')
  public async postSubmitAudit() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { businessType = 1, bundleId = '' } = this.ctx.query;
    const { businessType: postBusinessType } = this.ctx.request.body;
    // @ts-ignore
    const { accountType, bundleId: defaultBundleId } = ctx.getState('channelMeta');

    const params = {
      accountType,
      businessType: postBusinessType || businessType,
      kdtId,
      // 提审来源。3:微商城后台提交审核
      submitFrom: 3,
    };
    // 如果是支付宝小程序，要传 bundleId，来区分当前提审的是旗下的哪种小程序
    if (accountType === 10) {
      // @ts-ignore
      params.bundleId = bundleId || defaultBundleId;
    }
    const result = await this.weappCodeService.submitCustomWeappAccountAsync(params);
    return ctx.json(0, 'ok', result);
  }

  // 查询店铺服务
  @API('GET', 'shopExpiredInfo.json')
  public async getShopExpiredInfo() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const result = await this.prodReadService.queryShopProd(kdtId);
    result.lastDays = differenceInDays(result.endTime, Date.now());
    return ctx.json(0, 'ok', result);
  }
}

export = AuthController;
