import * as React from 'react';
import { RouteComponentProps, useHistory } from 'react-router-dom';
import { getCommonColumns } from 'pages/omni-channel/pages/new-list/components/table/columns';
import Table from 'pages/omni-channel/pages/new-list/components/table';
import * as api from 'pages/omni-channel/pages/list/components/table/api';
import { CHANNEL_ID, CHANNEL_TYPE } from 'pages/omni-channel/constant';
import { useQuery } from 'pages/omni-channel/hooks/use-query';
import Filter from 'pages/omni-channel/pages/new-list/components/filter';
import { Button, Link, Notify, Pop } from 'zent';
import { Divider, LinkButton } from '@youzan/react-components';
import { thirdChannelUnbind } from 'pages/external/jd-takeout/api';
import { isRetailSingleStore } from '@youzan/utils-shop';
import { OMNI_CHANNEL_TYPE } from 'pages/omni-channel/auth-button-utils/constant';

const externalGoodsUrl = _global.isSupportMultiChannel
  ? '/v2/goods/shop-external-channel'
  : '/v2/goods/external-channel';
// 小程序没有操作项
const getColumns = ({ tableRef }) => {
  return getCommonColumns().map((column) => {
    if (column.name === 'handle') {
      return {
        ...column,
        bodyRender: data => {
          const { channelId, kdtId } = data;
          const items = [<Link href={`${externalGoodsUrl}#/?channelId=${CHANNEL_ID.JD_WAIMAI}`}>商品管理</Link>  ,<Pop
            trigger="click"
            className="close-pop"
            content={
              <div className="pop-text">
                    解除授权后，不再同步有赞商品信息到外部渠道，也不再获取外部渠道订单信息
              </div>
            }
            confirmText="确认"
            cancelText="取消"
            onConfirm={() => {
              thirdChannelUnbind({ kdtId, channelId }).then(() => {
                Notify.success('解除成功');
                tableRef.current?.reload();
              });
            }}
            position="top-right"
          >
            <LinkButton
            >
              解除授权
            </LinkButton>
          </Pop>
            
          ];
          return <Divider items={items.filter(node => Boolean(node))} />;
        },
      };
    }
    return column;
  });
};

export const List: React.FC<RouteComponentProps> = () => {
  const history = useHistory();
  const query = useQuery();
  const [isShowChannelSetting, setIsShowChannelSetting] = React.useState(isRetailSingleStore ? false : true);
  const tableRef = React.useRef<any>();
  const [searchParams, setSearchParams] = React.useState({
    channelId: CHANNEL_ID.JD_WAIMAI,
    searchKdtId: query.searchKdtId || ''
  });

  // 搜索接口真实采用的字段和参数
  const adapterSearchParams = React.useMemo(() => {
    return {
      ...searchParams,
      channelType: CHANNEL_TYPE.WAIMAITONG,
    };
  }, [searchParams]);

  React.useEffect(() => {
    if (isRetailSingleStore) {
      api.getWaiMaiTongList({
        pageNo: 1,
        pageSize: 100,
        ...adapterSearchParams,
      }).then((res) => {
        if (res?.items?.length) {
          setIsShowChannelSetting(true);
        }
      });
    }
  }, []);

  const handleSearch = (values) => {
    const newSearchParams = {
      ...searchParams,
      ...values,
      channelType: OMNI_CHANNEL_TYPE.JD_WAIMAI,
    };
    setSearchParams(newSearchParams);
    history.replace(`?${new URLSearchParams(newSearchParams).toString()}`);
  };

  return (
    <div className="v-jd-takeout-auth-list">
      <Filter onSearch={handleSearch} renderLeftOperate={() => isShowChannelSetting ? <Button type="primary" onClick={() => {
        window.open(`/v4/channel/omni/omni-channel/auth-list#/list/edit?kdtId=${_global.kdtId}&channelId=${CHANNEL_ID.JD_WAIMAI}&accountType=${CHANNEL_ID.JD_WAIMAI}`, '_blank');
      }}>渠道设置</Button> : null} />
      <Table
        ref={tableRef}
        columns={getColumns({ tableRef })}
        searchParams={adapterSearchParams}
        channelType={CHANNEL_TYPE.WAIMAITONG}
        channelId={CHANNEL_ID.JD_WAIMAI}
        request={params => api.getWaiMaiTongList(params)}
      />
    </div>
  );
};

export default List;
