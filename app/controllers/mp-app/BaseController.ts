import BaseController from '../base/BaseController';
import { Inject } from '@youzan/assets-route-plugin';
import ChannelCoreAccountService from '../../services/api/channels/channel/core/ChannelCoreAccountService';

class MpAppBaseController extends BaseController {
  @Inject()
  public readonly channelCoreAccountService!: ChannelCoreAccountService;

  /**
   * 获取公众号绑定情况
   */
  public async getBindInfo() {
    const { kdtId, request } = this.ctx;
    const { businessType = 1 } = request.body;
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    this.validator.required(accountType, 'accountType 不能为空');
    this.validator.required(businessType, 'businessType 不能为空');
    return this.channelCoreAccountService.queryMpBindInfoByKdtId({
      businessType,
      accountType,
      externalId: kdtId,
    });
  }
}

export default MpAppBaseController;
