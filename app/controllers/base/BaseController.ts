import { PCBaseController } from '@youzan/wsc-pc-base';
import { Router } from '@youzan/assets-route-plugin';
import channelMap, { channelTypeEnum } from '../../constants/channelMap';
import externalChannelMap from '../../constants/externalChannel';

@Router(':channelName')
class BaseController extends PCBaseController {
  public channelName: string | undefined;

  public channelType: string | undefined;

  public async init() {
    await super.init();
    const { ctx } = this;
    let { channelName } = ctx.params;
    if (this.channelName) {
      channelName = this.channelName;
    } else {
      this.channelName = channelName;
    }
    const pathMatchRes: RegExpMatchArray | null = ctx.path.match(
      new RegExp(
        `^/v4/channel/(${Object.values(channelTypeEnum)
          .filter(item => item !== 'mini' && item !== 'external')
          .join('|')})/(api/)?${channelName}`,
      ),
    );
    this.channelType = pathMatchRes
      ? pathMatchRes[1]
      : Object.keys(externalChannelMap).includes(channelName)
      ? channelTypeEnum.external
      : channelTypeEnum.miniApp;
    const currentChannelMeta = {
      ...(channelMap as any)[this.channelType as string][channelName],
      channelName,
    };

    if (currentChannelMeta) {
      ctx.setState('channelName', currentChannelMeta.name);
      ctx.setState('channelId', currentChannelMeta.id);
      ctx.setGlobal('channelMeta', currentChannelMeta);
      ctx.setState('channelMeta', currentChannelMeta);
    }
  }

  /**
   * 获取apollo公告配置
   */
  public getApolloAnnouncement() {
    const { ctx, channelType, channelName } = this;
    const key =
      channelType === channelTypeEnum.miniApp ? channelName : `${channelType}-${channelName}`;
    return ctx.apolloClient.getConfig({
      appId: 'wsc-pc-channel',
      namespace: `announcement`,
      key,
    });
  }

  /**
   * 获取优秀案例
   */
  public getApolloExcellentExample() {
    const { ctx, channelName, channelType } = this;
    const key =
      channelType === channelTypeEnum.miniApp ? channelName : `${channelType}-${channelName}`;
    return ctx.apolloClient.getConfig({
      appId: 'wsc-pc-channel',
      namespace: `showcase`,
      key,
    });
  }

  /**
   * 获取广告banner
   */
  public getApolloAdBanner() {
    const { ctx, channelName, channelType } = this;
    const key =
      channelType === channelTypeEnum.miniApp ? channelName : `${channelType}-${channelName}`;
    return ctx.apolloClient.getConfig({
      appId: 'wsc-pc-channel',
      namespace: `adbanner`,
      key,
    });
  }

  // 获取通用的操作参数
  public getOperatorParams() {
    const { ctx } = this;
    const { kdtId, firstXff } = ctx;
    const { id: userId, nickName } = ctx.getLocalSession('userInfo') || {};
    const appName = 'wsc-pc-channel';
    return {
      fromApp: appName,
      kdtId,
      operator: {
        nickName,
        userId,
        clientIp: firstXff,
        source: appName,
      },
    };
  }

  // 获取总店kdtId
  public getRootKdtId() {
    const { rootKdtId } = this.ctx.getState('shopInfo');
    const kdtId = rootKdtId || this.ctx.kdtId;
    return kdtId;
  }

  /**
   * @description 判断是否在apollo白名单内
   * @param {string} key 白名单 key
   * @param {object} options 参数
   * @param {number} options.kdtId 店铺 kdtId
   * @param {boolean} options.useRootKdtId 是否使用 rootKdtId
   * @param {string} options.globalKey _global 下的字段名
   */
  isInWhiteListByApollo(
    key: string,
    options: { [p: string]: any } = {
      kdtId: '',
      useRootKdtId: '',
      globalKey: '',
    },
  ) {
    this.validator.required(key, 'key不能为空');

    const {
      useRootKdtId,
      globalKey,
      namespace = 'wsc-pc-channel.whitelist',
      appId = 'wsc-pc-channel',
    } = options;
    let kdtId = options.kdtId || this.ctx.kdtId;

    // 使用总部的 kdtId
    if (useRootKdtId) {
      const { rootKdtId } = this.ctx.getState('shopMetaInfo');
      kdtId = rootKdtId || kdtId;
    }

    const res = this.getApolloConfigData({
      appId,
      namespace,
      key,
    });
    let isInWhiteList = false;

    if (res) {
      try {
        isInWhiteList = this.parseWhiteList(res, kdtId);
      } catch (err) {
        this.ctx.logger.error('Apollo白名单配置解析出错', err, {
          key,
          content: res,
        });
      }
    }

    if (globalKey) {
      this.ctx.setGlobal(globalKey, isInWhiteList);
    }

    return isInWhiteList;
  }

  // 获取apollo配置数据
  getApolloConfigData({
    namespace,
    key,
    appId,
  }: {
    namespace: string;
    key: string;
    appId: string;
  }) {
    this.validator.required(appId, 'appId参数不能为空');
    this.validator.required(namespace, 'namespace参数不能为空');

    const params: { [str: string]: any } = { appId, namespace };
    if (key) {
      params.key = key;
    }
    const content = this.ctx.apolloClient.getConfig(params);
    return content;
  }

  /**
   * 解析白名单规则
   * @param {string} content 白名单内容
   * @param {number} kdtId 店铺 kdtId
   */
  parseWhiteList(content: string | any[], kdtId: string | number) {
    const yes: Array<string | number> = []; // 在
    const no: Array<string | number> = []; // 不在
    let percentage = 0; // 百分比
    kdtId = +kdtId || +this.ctx.kdtId; // 转成数字
    if (!content || !kdtId) {
      return false;
    }
    if (typeof content === 'string') content = content.split(',');
    content.forEach(item => {
      if (!item) {
        return;
      }
      item = ('' + item).trim();
      if (item.endsWith('%')) {
        // 解析百分比
        percentage = +item.slice(0, -1);
      } else if (item.startsWith('-')) {
        // 不在
        no.push(+item.slice(1));
      } else {
        // 在
        yes.push(+item);
      }
    });

    // 开始匹配 kdtId
    if (no.indexOf(kdtId) >= 0) {
      return false;
    }

    if (yes.indexOf(kdtId) >= 0) {
      return true;
    }

    return +`${kdtId}`.slice(-2) <= percentage - 1;
  }
}

export = BaseController;
