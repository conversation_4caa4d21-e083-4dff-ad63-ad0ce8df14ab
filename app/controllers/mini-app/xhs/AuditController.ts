import { Router, API, Inject } from '@youzan/assets-route-plugin';

import BaseController from './BaseController';
import CustomerUnifiedCertQueryService from '../../../services/api/pay/customer/cert/UnifiedCertQueryService';
import HongShuMiniAppService from '../../../services/api/channels/channel/core/HongShuMiniAppService';
import XiaohongshuHomepageService from '../../../services/api/ebiz/video/channels/uc/XiaohongshuHomepageService';

/**
 * 通用提审业务
 */
@Router('xhs/audit')
class AuthController extends BaseController {
  @Inject()
  public readonly CustomerUnifiedCertQueryService!: CustomerUnifiedCertQueryService;

  @Inject()
  public readonly HongShuMiniAppService!: HongShuMiniAppService;

  @Inject()
  public readonly XiaohongshuHomepageService!: XiaohongshuHomepageService;

  public async init() {
    this.channelName = 'xhs';
    super.init();
  }

  // 有赞主体认证名称和小红书小程序主体认证名称是否一致
  @API('GET', 'isSamePrincipal.json')
  public async isSamePrincipal() {
    const isSame = await this.isSameXhsPrincipalName();

    return this.ctx.json(0, 'ok', isSame || false);
  }

  // 刷新小红书小程序基础信息
  @API('POST', 'refreshBasicInfo.json')
  public async refreshBasicInfo() {
    const res = await this.HongShuMiniAppService.refreshBasicInfo({
      kdtId: this.ctx.kdtId,
    });
    return this.ctx.json(0, 'ok', res);
  }

  // 获取服务商邀请链接
  @API('GET', 'getInvitationUrl.json')
  public async getInvitationUrl() {
    const res = await this.XiaohongshuHomepageService.getInvitationUrl();
    return this.ctx.json(0, 'ok', res);
  }
}

export = AuthController;
