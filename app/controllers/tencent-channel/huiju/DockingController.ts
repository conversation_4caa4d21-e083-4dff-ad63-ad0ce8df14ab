import BaseController from './BaseController';
import { Router, Index, API, Inject } from '@youzan/assets-route-plugin';
import TencentHuiJuService from '../../../services/api/channels/channel/core/TencentHuiJuService';

@Router('huiju/docking')
class DockingController extends BaseController {
  @Inject()
  public readonly tencentHuiJuService!: TencentHuiJuService;

  @Index('entry')
  async getIndexHtml() {
    const { ctx } = this;
    this.validator.required(ctx.kdtId, 'kdtId 不能为空');

    const result = await this.tencentHuiJuService.get({
      kdtId: ctx.kdtId,
    });
    if (result) {
      ctx.setGlobal('authData', this.tencentHuiJuService.handleSensitiveData(result));
      return ctx.render('tencent-channel/huiju/entry.html');
    }
    return ctx.render('tencent-channel/huiju/auth.html');
  }

  @API('POST', 'bind.json')
  async startAuthorization() {
    const { ctx } = this;
    const {
      request: {
        body: { appId, appSecret, dataKey },
      },
    } = ctx;

    this.validator.required(appId, 'appId 不能为空');
    this.validator.required(appSecret, 'appSecret 不能为空');
    this.validator.required(dataKey, 'dataKey 不能为空');

    const result = await this.tencentHuiJuService.bind({
      appId,
      appSecret,
      dataKey,
      kdtId: ctx.kdtId,
      userId: ctx.userId,
    });

    return ctx.json(0, 'success', result);
  }

  @API('GET', 'unbind.json')
  async stopAuthorization() {
    const { ctx } = this;

    const result = await this.tencentHuiJuService.unbind({
      kdtId: ctx.kdtId,
      userId: ctx.userId,
    });

    return ctx.json(0, 'success', result);
  }
}

export = DockingController;
