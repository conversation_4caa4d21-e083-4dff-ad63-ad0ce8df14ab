import { Router, Index, API, Inject } from '@youzan/assets-route-plugin';

import CommonBasicInfoController from '../common/BasicInfoController';
import XiaohongshuChannelConsoleService from '../../../services/api/ebiz/video/channels/item/xhs/XiaohongshuChannelConsoleService';
import { ShopAbilityUtil } from '@youzan/plugin-shop-ability';

@Router('xhs/goods-sort')
class GoodsController extends CommonBasicInfoController {
  @Inject()
  private readonly XiaohongshuChannelConsoleService!: XiaohongshuChannelConsoleService;

  public async init() {
    this.channelName = 'xhs';
    super.init();
  }

  @Index(['', 'xhs-independent'])
  public async goodsSortHtml() {
    // await this.checkIsBind();
    const { ctx } = this;
    // 小红书独立版本判断
    const hasXhsIndependentAbility = await new ShopAbilityUtil(ctx).checkAbilityValid({
      keys: ['xhs_independent_ability'],
      kdtId: ctx.kdtId,
    });
    if (hasXhsIndependentAbility && ctx.url.indexOf('xhs-independent') < 0) {
      this.ctx.redirect('/v4/channel/xhs/goods-sort/xhs-independent');
      return;
    }

    await this.checkXhsLocalLife();
    ctx.setState('subtitle', '商品推广');
    return ctx.render('mini-app/xhs/goods-sort/index.html');
  }

  @API('GET', 'get-goods-list.json')
  public async getGoodsList() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const res = await this.XiaohongshuChannelConsoleService.getXhsPageItemTab(kdtId);
    return ctx.json(0, 'ok', res || []);
  }

  @API('POST', 'set-goods-list.json')
  public async setGoodsList() {
    const { ctx } = this;
    const {
      kdtId,
      request: {
        body: { isApply, itemIdData, bizType },
      },
    } = ctx;
    const query: any = {
      isApply,
      kdtId,
      bizType,
      ...itemIdData,
    };
    const res = await this.XiaohongshuChannelConsoleService.setHomePageItemTab(query);
    return ctx.json(0, 'ok', res);
  }

  @API('GET', 'get-xhs-goods-list.json')
  public async getXhsGoodsList() {
    const { ctx } = this;
    const { kdtId, request } = ctx;
    const { auditStatus, channelCanSale, pageSize, page, title, bizType } = request.query;
    const res = await this.XiaohongshuChannelConsoleService.listChannelPaged({
      auditStatus,
      channelCanSale,
      pageSize,
      page,
      title,
      kdtId,
      bizType,
    });

    return ctx.json(0, 'ok', res);
  }
}

export = GoodsController;
