import BaseController from '../BaseController';

class WechatVideoBaseController extends BaseController {
  // 获取小程序基本信息
  protected async getMiniAppAccountInfo() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { businessType = 1 } = this.ctx.query;
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');

    const params = {
      accountType,
      businessType,
      kdtId,
    };
    const result = await this.channelCoreAccountService.queryMpAccountInfoByKdtId(params);
    return result;
  }

  /**
   * 获取小程序绑定情况
   */
  protected async getMiniAppBindInfo() {
    const { kdtId, request } = this.ctx;
    const { businessType = 1 } = request.body;
    // @ts-ignore
    const { accountType } = this.ctx.getState('channelMeta');
    this.validator.required(accountType, 'accountType 不能为空');
    this.validator.required(businessType, 'businessType 不能为空');
    return this.channelCoreAccountService.queryMpBindInfoByKdtId({
      businessType,
      accountType,
      externalId: kdtId,
    });
  }
}

export default WechatVideoBaseController;
