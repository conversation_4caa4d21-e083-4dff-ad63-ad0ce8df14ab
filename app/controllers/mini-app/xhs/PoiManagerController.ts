import { Router, Index, Inject, API, Metadata } from '@youzan/assets-route-plugin';
import { checkRetailHqStore } from '@youzan/utils-shop';
import CommonBasicInfoController from '../common/BasicInfoController';
import PoiManagerService from '../../../services/api/channels/channel/core/PoiManagerService';
import XiaohongshuItemService from '../../../services/api/channels/channel/core/XiaohongshuItemService';
import XhsShopOpenService from '../../../services/api/ebiz/video/channels/uc/XhsShopOpenService';
import { settleConfigKey } from '../../../constants/xhsLocalLife';

@Router('xhs/poi-manager')
class PoiManagerController extends CommonBasicInfoController {
  @Inject()
  private poiManagerService!: PoiManagerService;

  @Inject()
  private xiaohongshuItemService!: XiaohongshuItemService;

  @Inject()
  private xhsShopOpenService!: XhsShopOpenService;

  public async init() {
    this.channelName = 'xhs';
    super.init();
  }

  @Index('')
  @Metadata('INDEX')
  async getPoiManagerHtml() {
    const { ctx } = this;
    ctx.setState('subtitle', '门店管理（POI）');

    const xhsBasicInfo = await this.getXhsMiniAppAccountInfo();
    ctx.setGlobal('pageData', {
      xhsBasicInfo,
    });
    // if (!xhsLocalLife) {
    //   return ctx.redirect('/v4/channel/xhs/dashboard');
    // }

    // // 每次进入页面 都要调一下 fetch  2024-10-10 测试用例评审 暂时不调
    // const { kdtId } = this.ctx;
    // await this.poiManagerService.fetch({ kdtId });

    return ctx.render('mini-app/xhs/poi-manager/index.html');
  }

  // 分页查询小红书门店列表
  @API('GET', 'list-of-page.json')
  public async listOfPage() {
    const { kdtId, request } = this.ctx;
    const { pageNo = 1, pageSize = 10, bindStatus = 0 } = request.query;
    const params = { kdtId, pageNo, pageSize, bindStatus };
    if (+bindStatus === 0) {
      delete params.bindStatus;
    }

    const result = await this.poiManagerService.listOfPage(params);

    this.ctx.json(0, 'ok', result);
  }

  @API('GET', 'list-of-page-v2.json')
  public async listOfPageV2() {
    const { kdtId, request } = this.ctx;
    const { pageNo = 1, pageSize = 10, bindStatus = 0 } = request.query;
    const params = { kdtId, pageNo, pageSize, bindStatus };
    if (+bindStatus === 0) {
      delete params.bindStatus;
    }

    const result = await this.poiManagerService.listOfPageV2(params);
    const { items = [], paginator = { totalCount: 0 }, ...others } = result;
    const currentPage = items.splice((+pageNo - 1) * +pageSize, +pageSize);

    this.ctx.json(0, 'ok', {
      ...others,
      items: currentPage,
      paginator: {
        page: +pageNo,
        pageSize: +pageSize,
        totalCount: paginator.totalCount,
      },
    });
  }

  // 全量同步小红书最新的门店数据回有赞
  @API('GET', 'fetch.json')
  public async fetch() {
    const { kdtId } = this.ctx;
    const result = await this.poiManagerService.fetch({ kdtId });

    return this.ctx.json(0, 'ok', result);
  }

  // 触发水印
  @API('GET', 'generatePrincipalWatermark.json')
  public async generatePrincipalWatermark() {
    const { kdtId } = this.ctx;
    const result = await this.xhsShopOpenService.generatePrincipalWatermark({ kdtId });

    return this.ctx.json(0, 'ok', result);
  }

  // poi 选店组件 查询可绑定小红书门店的有赞门店
  @API('GET', 'shop-selector-of-page.json')
  public async shopSelectorOfPage() {
    const { kdtId, request } = this.ctx;
    const { pageNo = 1, pageSize = 10, shopName = '' } = request.query;
    const result = await this.poiManagerService.shopSelectorOfPage({
      kdtId,
      pageNo,
      pageSize,
      shopName,
    });

    return this.ctx.json(0, 'ok', result);
  }

  // 绑定有赞门店
  @API('GET', 'bind-shop.json')
  public async bindShop() {
    const { kdtId, request } = this.ctx;
    const { bindPoiId, bindKdtId } = request.query;
    const result = await this.poiManagerService.bindShop({
      bindPoiId,
      kdtId,
      bindKdtId,
    });

    return this.ctx.json(0, 'ok', result);
  }

  // 解绑有赞门店
  @API('GET', 'unbind-shop.json')
  public async unbindShop() {
    const { kdtId, request } = this.ctx;
    const { bindPoiId } = request.query;
    const result = await this.poiManagerService.unbindShop({
      bindPoiId,
      kdtId,
    });

    return this.ctx.json(0, 'ok', result);
  }

  // 换绑有赞门店
  @API('GET', 'chang-bind-shop.json')
  public async changBindShop() {
    const { kdtId, request } = this.ctx;
    const { bindPoiId, bindKdtId } = request.query;
    const result = await this.poiManagerService.changBindShop({
      bindPoiId,
      kdtId,
      bindKdtId,
    });

    return this.ctx.json(0, 'ok', result);
  }

  // 删除POI
  @API('GET', 'delete.json')
  public async delete() {
    const { request } = this.ctx;
    const { id } = request.query;

    const result = await this.poiManagerService.delete({ id });
    return this.ctx.json(0, 'ok', result);
  }

  // 根据POI查询绑定信息
  @API('GET', 'find-poi-bind.json')
  public async findPoiBind() {
    const { kdtId, request } = this.ctx;
    const { bindPoiId } = request.query;
    const result = await this.poiManagerService.findPoiBind({
      bindPoiId,
      kdtId,
    });

    return this.ctx.json(0, 'ok', result);
  }

  // 自定义结算方式
  @API('GET', 'custom-settle-type.json')
  public async customSettleType() {
    const { kdtId, request } = this.ctx;
    const { settleType } = request.query;
    const shopInfo = this.ctx.getState('shopInfo');
    const isRetailHqStore = checkRetailHqStore(shopInfo);

    if (isRetailHqStore) {
      const operator = this.getOperatorParams();
      const params = {
        kdtId,
        key: settleConfigKey,
        value: settleType,
        operator: {
          fromApp: operator.fromApp,
          name: operator.operator.nickName,
          id: operator.operator.userId,
          type: 1,
        },
      };

      const result = await this.shopConfigWriteService.setShopConfig(params).catch(() => {});
      if (result) {
        return this.ctx.json(0, 'ok', { success: result, msg: '设置成功' });
      }

      return this.ctx.json(0, 'ok', { success: false, msg: '设置失败' });
    }

    return this.ctx.json(0, 'ok', { success: false, msg: '非总部无法设置结算方式' });
  }

  // 获取所有 POI
  @API('GET', 'get-all-poi.json')
  public async getAllPoi() {
    const { kdtId, request } = this.ctx;
    const { itemId = '' } = request.query;
    const params = { kdtId, page: 1, pageSize: 100, itemId };
    const result = [];

    let done = false;

    do {
      const list = await this.xiaohongshuItemService.pageItemAvailablePoi(params).catch(() => {});
      const { items = [] } = list || {};

      result.push(...items);

      done = items.length !== params.pageSize;
      params.page++;
    } while (!done);

    return this.ctx.json(0, 'ok', result);
  }

  // 获取 tradeAbility
  @API('GET', 'get-trade-ability.json')
  public async getTradeAbility() {
    const xhsAccountInfo = await this.getXhsMiniAppAccountInfo();
    const { tradeAbility = 0 } = xhsAccountInfo || {};

    return this.ctx.json(0, 'ok', tradeAbility);
  }

  // pageItemAvailablePoi
  @API('GET', 'page-item-available-poi.json')
  public async pageItemAvailablePoi() {
    const { kdtId, request } = this.ctx;
    const { pageNo = 1, pageSize = 10, itemId = '' } = request.query;

    const params = {
      kdtId,
      page: +pageNo,
      pageSize: +pageSize,
      itemId: +itemId,
    };

    // @ts-ignore
    const result = await this.xiaohongshuItemService.pageItemAvailablePoi(params);

    return this.ctx.json(0, 'ok', result);
  }
}

export = PoiManagerController;
