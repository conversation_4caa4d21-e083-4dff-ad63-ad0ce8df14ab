import { Router, Inject, API } from '@youzan/assets-route-plugin';
import BaseController from '../BaseController';
import WeappCodeService from '../../../services/api/channels/apps/WeappCodeService';
import ChannelTokenService from '../../../services/api/channels/channel/core/ChannelTokenService';
import QqAppletMchIdService from '../../../services/api/pay/cme/QqAppletMchIdService';
import channelMap from '../../../constants/miniAppChannelMap';

/**
 * 通用提审业务
 */
@Router('qq/audit')
class AuthController extends BaseController {
  @Inject()
  private readonly weappCodeService!: WeappCodeService;

  @Inject()
  private channelTokenService!: ChannelTokenService;

  @Inject()
  private qqAppletMchIdService!: QqAppletMchIdService;

  public async init() {
    this.channelName = 'qq';
    super.init();
  }

  /**
   * 获取小程序提交审核的所有前置检查项和检查通过项列表
   */
  @API('GET', 'status.json')
  public async getStatus() {
    const { ctx } = this;
    const { kdtId } = ctx;
    const { businessType = 1 } = this.ctx.query;
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');

    const params = {
      accountType,
      businessType,
      kdtId,
    };
    const result = await this.weappCodeService.getMiniProgramPreCheckResult(params);

    // qq 渠道内校验支付
    const { preCheckPassList = [] } = result;
    const lastItemPass = preCheckPassList.length
      ? preCheckPassList[preCheckPassList.length - 1].preCheckPass
      : false;

    result.allPreCheckList = result.allPreCheckList.concat({ name: '支付配置检查' });
    const setPayCheckItem = (pass: boolean) => {
      result.preCheckPassList = result.preCheckPassList.concat({
        name: '支付配置检查',
        code: 'payment',
        preCheckPass: pass,
      });
    };

    if (lastItemPass) {
      const token = await this.channelTokenService.getChannelToken({
        accountType: channelMap.qq.accountType,
        businessType,
        kdtId: ctx.kdtId,
      });

      if (token && token.accessToken) {
        // 获取支付配置结果
        const payConfig = await this.qqAppletMchIdService.configMchId({
          accessToken: token.accessToken,
        });
        ctx.logger.info(`qq 小程序获取支付配置结果 ${payConfig}`, '', {});

        setPayCheckItem(!!payConfig);
      } else {
        setPayCheckItem(false);
      }
    }

    return ctx.json(0, 'ok', result);
  }
}

export = AuthController;
