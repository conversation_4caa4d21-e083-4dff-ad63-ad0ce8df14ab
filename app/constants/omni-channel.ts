import { channelTypeEnum } from './channelMap';

export enum CHANNEL_TYPE {
  /** 外卖通 */
  WAIMAITONG = 1,
  /** 卡券核销 */
  KAQUAN = 2,
  /** 抖音卡券 */
  COUPON = 3,
  /** 餐饮预订 */
  TABLE_BOOKING = 4,
}

export enum CHANNEL_ID {
  ALI_PAY = 10000,
  XHS_APP = 600,
  WEAPP = 500,
  MT_WM = 200,
  ELEME_WM = 201,
  MEITUAN = 700,
  TIKTOK = 800,
  MEITUAN_SHANGOU = 203,
  TIKTOK_SHELF = 401,
  XIAOHONGSHU = 900,
  TIKTOK_COUPON = 401,
  TIKTOK_SXT = 403,
  MEITUAN_TABLE_BOOKING = 1000,
}

export const GetMPVersionKeys = [
  CHANNEL_ID.WEAPP,
  CHANNEL_ID.TIKTOK_SHELF,
  CHANNEL_ID.XHS_APP,
  CHANNEL_ID.ALI_PAY,
];

export const ChannelVersionMap = {
  [CHANNEL_ID.WEAPP]: {
    channelName: 'weapp',
    channelType: channelTypeEnum.miniApp,
  },
  [CHANNEL_ID.TIKTOK_SHELF]: {
    channelName: 'tiktok-shelf',
    channelType: channelTypeEnum.miniApp,
  },
  [CHANNEL_ID.XHS_APP]: {
    channelName: 'xhs',
    channelType: channelTypeEnum.miniApp,
  },
  [CHANNEL_ID.ALI_PAY]: {
    channelName: 'alipay',
    channelType: channelTypeEnum.miniApp,
  },
};

export enum ChannelStatus {
  unSubscribe = 0,
  unBind = 1,
  alBind = 2,
}
