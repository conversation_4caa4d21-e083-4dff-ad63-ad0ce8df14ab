import { Router, API, Inject, Index } from '@youzan/assets-route-plugin';
import CommonBasicInfoController from '../common/BasicInfoController';
import TiktokAccountInfoService from '../../../services/api/channels/tiktok/TiktokAccountInfoService';

@Router('tiktok-coupon/basic-info')
class BasicInfoController extends CommonBasicInfoController {
  @Inject()
  private readonly tikTokAccountInfoService!: TiktokAccountInfoService;

  public async init() {
    this.channelName = 'tiktok-coupon';
    super.init();
  }

  @Index('')
  public async getBaseInfoHtml() {
    await this.checkIsBind();
    const { ctx } = this;
    ctx.setState('subtitle', '基本信息');
    return ctx.render('mini-app/common/basic-info/index.html');
  }

  /**
   * 获取抖音小程序信息
   */
  @API('GET', 'account-info.json')
  public async getAccountInfoJson() {
    const accountInfo = await this.getAccountInfo();
    await this.ctx.json(0, 'ok', {
      name: accountInfo.name,
      avatar: accountInfo.icon,
      description: accountInfo.desc,
      category: accountInfo.categoryInfo,
      appId: accountInfo.appId,
      registerSource: accountInfo.createdFrom,
      categoryInfoList: accountInfo.categoryInfoList,
    });
  }

  /**
   * 获取经营攻略数据
   */
  @API('GET', 'business-strategy-data.json')
  public async getBusinessStrategyData() {
    const res = await this.ctx.apolloClient.getConfig({
      appId: 'wsc-pc-channel',
      namespace: 'showcase',
      key: 'business-strategy',
    });
    const allConfig = res || {};
    const items = allConfig[this.channelName as string] || [];
    await this.ctx.json(0, 'ok', items);
  }

  protected async getAccountInfo() {
    const { kdtId, request } = this.ctx;
    const { businessType = 8 } = request.query;
    const result = await this.tikTokAccountInfoService.getBasicInfo({ kdtId, businessType });
    return result;
  }
}

export = BasicInfoController;
