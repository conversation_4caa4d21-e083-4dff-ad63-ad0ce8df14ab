import { Router, API, Inject } from '@youzan/assets-route-plugin';
import BaseController from '../BaseController';
import ChannelAuthBindService from '../../../services/api/channels/channel/core/ChannelAuthBindService';
import ChannelTokenService from '../../../services/api/channels/channel/core/ChannelTokenService';

@Router('redbook/auth')
class BasicInfoController extends BaseController {
  @Inject()
  private channelAuthBindService!: ChannelAuthBindService;

  @Inject()
  private channelTokenService!: ChannelTokenService;

  public async init() {
    this.channelName = 'redbook';
    super.init();
  }

  // 提交绑定信息
  @API('POST', 'to-bind.json')
  public async postAuthBindToShopJson() {
    const { ctx } = this;
    const { appId, siteId, siteSecret, businessType = 1 } = ctx.request.body;
    this.validator.required(appId, 'appId 不能为空');
    this.validator.required(siteId, 'siteId 不能为空');
    this.validator.required(siteSecret, 'siteSecret 不能为空');
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');

    await this.channelAuthBindService.authBindToShop({
      accountType,
      appId,
      businessType,
      kdtId: ctx.kdtId,
      siteId,
      siteSecret,
      userId: ctx.userId,
    });

    ctx.json(0, 'ok');
  }

  // 获取绑定信息
  @API('GET', 'info.json')
  public async getAuthBindInfoJson() {
    const { ctx } = this;
    const { businessType = 1 } = ctx.request.query;
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');
    const result = await this.channelTokenService.getChannelToken({
      kdtId: ctx.kdtId,
      accountType,
      businessType,
    });
    if (result.siteSecret) {
      result.siteSecret = result.siteSecret.replace(/^(\d{4}).*(\d{4})$/, '$1****$2');
    }
    ctx.json(0, 'ok', result);
  }

  // 解除绑定
  @API('POST', 'to-unbind.json')
  public async postAuthUnBindJson() {
    const { ctx } = this;
    const { businessType = 1 } = ctx.request.body;
    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');

    const result = await this.channelAuthBindService.unbindAccountFromShop({
      accountId: ctx.accountId,
      kdtId: ctx.kdtId,
      isFromCp: false,
      accountType,
      businessType,
    });

    ctx.json(0, 'ok', result);
  }
}

export = BasicInfoController;
