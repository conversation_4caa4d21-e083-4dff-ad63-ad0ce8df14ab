import { Router, Index, Inject, Metadata } from '@youzan/assets-route-plugin';
import GrayReleaseService from '@youzan/wsc-pc-base/app/services/common/GrayReleaseService';
import CommonBasicInfoController from '../common/BasicInfoController';

@Router('xhs/pro-homepage')
class ProHomepageController extends CommonBasicInfoController {
  @Inject()
  private readonly grayReleaseService!: GrayReleaseService;

  public async init() {
    this.channelName = 'xhs';
    super.init();
  }

  @Index('')
  @Metadata('INDEX')
  async getPoiManagerHtml() {
    const { ctx } = this;
    ctx.setState('subtitle', '专业号主页菜单');

    const [isDecoInGray, accountInfo] = await Promise.all([
      this.grayReleaseService.isInGrayReleaseByKdtId('brand-template-new', ctx.kdtId),
      this.getAccountInfo(),
    ]);

    ctx.setGlobal('isDecoInGray', isDecoInGray);
    ctx.setGlobal('accountInfo', accountInfo);

    return ctx.render('mini-app/xhs/pro-homepage/index.html');
  }
}

export = ProHomepageController;
