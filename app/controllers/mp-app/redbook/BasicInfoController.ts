import { Router, Index, API } from '@youzan/assets-route-plugin';
import BaseController from '../BaseController';
import channelMap from '../../../constants/mpAppChannelMap';
import { ShopAbilityUtil } from '@youzan/plugin-shop-ability';

@Router('redbook/basic-info')
class BasicInfoController extends BaseController {
  public async init() {
    this.channelName = 'redbook';
    super.init();
  }

  @Index(['', 'xhs-independent'])
  public async getBaseInfoHtml() {
    const { ctx } = this;
    // 小红书独立版本判断
    const hasXhsIndependentAbility = await new ShopAbilityUtil(ctx).checkAbilityValid({
      keys: ['xhs_independent_ability'],
      kdtId: ctx.kdtId,
    });
    if (hasXhsIndependentAbility && ctx.url.indexOf('xhs-independent') < 0) {
      this.ctx.redirect('/v4/channel/xhs/goods/xhs-independent');
      return;
    }
    ctx.setState('subtitle', '基础信息');
    return ctx.render('mini-app/common/basic-info/index.html');
  }

  /**
   * 获取小红书账号信息
   */
  @API('GET', 'account-info.json')
  public async getAccountInfoJson() {
    const { kdtId, request } = this.ctx;
    const { businessType = 1 } = request.query;
    const accountInfo = await this.channelCoreAccountService.getHongShuAccountInfo({
      accountType: channelMap.redbook.accountType,
      businessType,
      externalId: kdtId,
    });
    if (!accountInfo) {
      return this.ctx.json(-1, '小红书账号未绑定');
    }
    await this.ctx.json(0, 'ok', {
      name: accountInfo.nickName,
      avatar: accountInfo.avatar,
      sellerId: accountInfo.sellerId,
      mpId: accountInfo.mpId,
    });
  }
}

export = BasicInfoController;
