import { Index, Router, Inject, API } from '@youzan/assets-route-plugin';
import ChannelAuthBindService, {
  IGetAuthPageUrlParams,
} from '../../../services/api/channels/channel/core/ChannelAuthBindService';
import AuthTokenService from '../../../services/api/uic/auth/AuthTokenService';
import BaseController from '../BaseController';
import channelMap from '../../../constants/miniAppChannelMap';

/**
 * 通用授权
 */
@Router(':channelName/auth')
class AuthController extends BaseController {
  @Inject()
  private channelAuthBindService!: ChannelAuthBindService;

  @Inject()
  private authTokenService!: AuthTokenService;

  @Index('certification')
  async getCertificationHtml() {
    const { ctx } = this;
    await ctx.render('mini-app/common/auth/certification.html');
  }

  @Index('empty')
  async getEmptyHtml() {
    const { ctx } = this;
    ctx.setState('subtitle', '请前往授权发布');
    await ctx.render('mini-app/common/auth/empty.html');
  }

  @Index('callback')
  async getCallbackHtml() {
    const { ctx } = this;

    try {
      ctx.logger.info(`访问callback，入参: ${JSON.stringify(ctx.query)}`, '', {});
    } catch (e) {}

    // @ts-ignore
    const channelMeta: any = ctx.getState('channelMeta');
    const { authType, businessType = 1 } = ctx.query;
    let code = ctx.query[channelMeta.codeKey || 'code'];

    // 三方 code key 更换后，兼容
    if (channelMeta.newCodeKey && ctx.query[channelMeta.newCodeKey]) {
      code = ctx.query[channelMeta.newCodeKey];
    }

    const expiresIn = ctx.query[channelMeta.expiresIn || 'expires_in'];

    this.validator.required(code, '参数错误，code 不能为空');

    let params: any = {
      createdFrom: 1,
      accountType: channelMeta.accountType,
      businessType,
      appAccountAuthCode: code,
    };

    if (
      channelMeta.accountType === channelMap.xhs.accountType ||
      channelMeta.accountType === channelMap.ks.accountType
    ) {
      const { kdtId, accountId, sid, accountType } = ctx.query;
      this.validator.required(kdtId, '参数错误，kdtId 不能为空');
      this.validator.required(accountId, '参数错误，accountId 不能为空');
      this.validator.required(sid, '参数错误，sid 不能为空');
      params = {
        ...params,
        accountType,
        kdtId,
        accountId,
        sid,
      };
    } else {
      const localSession = ctx.getLocalSession('userInfo') || {};
      const accountId = localSession.userId;

      params = {
        ...params,
        kdtId: ctx.kdtId,
        accountId,
        sid: ctx.sid,
      };
    }

    if (expiresIn) {
      // @ts-ignore
      params.expiresIn = Number(expiresIn);
    }

    try {
      switch (String(authType)) {
        // 绑定
        case '1':
          await this.channelAuthBindService.bindAccountToShop(params);
          break;
        // 重新绑定
        case '2':
          await this.channelAuthBindService.reAuth(params);
          break;
      }
      ctx.setGlobal({ callback: { code: 0, msg: 'ok' } });
      if (channelMeta.accountType === channelMap.xhs.accountType) {
        await this.getSuccessHtml();
        return;
      }
    } catch (e) {
      ctx.logger.error(
        `有赞侧授权失败 -- 失败原因 - ${JSON.stringify(e)} -- 入参 - ${JSON.stringify(ctx.query)}`,
      );
      ctx.setGlobal({ callback: { code: (e as any).code, msg: (e as any).msg } });
    }

    await ctx.render('mini-app/common/auth/callback.html');
  }

  /**
   * 获取授权地址
   */
  @Index('url')
  public async getAuthPageUrl() {
    const { ctx } = this;
    const { kdtId, userId, request } = ctx;
    const { businessType = 1, reAuth, redirectUri } = request.query;

    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');
    this.validator.required(accountType, 'accountType 不能为空');

    const params: IGetAuthPageUrlParams = {
      accountType,
      // businessType: 1/商城业务 2/品宣
      businessType,
      kdtId,
      userId,
      // authType: 1/首次授权 2/重新授权
      authType: 1,
    };
    if (redirectUri) {
      params.redirectUri = redirectUri;
    }
    if (reAuth) {
      params.authType = 2;
    }
    const result = await this.channelAuthBindService.getAuthPageUrl(params);
    ctx.setGlobal('authUrl', result.url);
    await ctx.render('mini-app/common/auth/url.html');
  }

  /**
   * 解除渠道绑定
   */
  @API('POST', 'unbind.json')
  public async postUnbind() {
    const { ctx } = this;
    const {
      request: {
        body: { token, businessType = 1 },
      },
    } = ctx;
    this.validator.required(token, '参数错误，token 参数必填');

    // @ts-ignore
    const { accountType } = ctx.getState('channelMeta');
    const params = {
      accountId: ctx.accountId,
      kdtId: ctx.kdtId,
      isFromCp: false,
      accountType,
      businessType,
    };

    // 1、扫码核身验证
    const tokenCheckResult = await this.authTokenService.tokenCheck({
      tokenAuthUserInfoDto: {
        sessionId: ctx.sid,
      },
      authTokenInfo: { token, bizType: 3 },
      scene: {
        userAgent: ctx.userAgent,
        ipAddress: ctx.firstXff,
      },
    });

    // 2、解绑
    if (tokenCheckResult) {
      const result = await this.channelAuthBindService.unbindAccountFromShop(params);
      await ctx.json(0, 'ok', result);
    } else {
      await ctx.json(-1, '管理员身份验证失败');
    }
  }

  /**
   * 获取小程序绑定情况
   */
  @API('GET', 'mp-bind-info.json')
  public async queryMpBindInfoByKdtId() {
    const result = await this.getBindInfo();
    await this.ctx.json(0, 'success', result);
  }

  @Index('success')
  public async getSuccessHtml() {
    const { ctx } = this;
    ctx.setState('subtitle', '授权成功');
    await ctx.render('mini-app/common/auth/success.html');
  }
}

export = AuthController;
